package components

import (
    "webgo/pkg/gos/templates"
    "webgo/hpv/locations/transport/responses"
)

templ ListLocationsCpn(locationsNew []responses.Locations) {
    <div class="col-1/3 location-list-details">
        <div class="consultant-locations-list" id="consultant-locations-list">
            <div class="loading" style="display: none;">
                <div class="msd-loader-container">
                    <div class="msd-loader"></div>
                </div>
            </div>
            <div class="empty-state" style="display: none;">
                <img src={ templates.AssetURL("/asset/images/common/no-location.svg") } alt="No Location" />
            </div>
            <div class="consultant-locations" data-page-offset="1">
                    for _, location := range locationsNew {
                        @locationItem(location)
                    }
            </div>
        </div>
    </div>
}

templ locationItem(location responses.Locations) {
    {{ maplocation := "" }}
    if location.Map != nil {
        {{ maplocation = *location.Map }}
    }
    <article class="location" data-id={ location.Id } data-name={ location.Name } data-address={ location.Address }
        data-phone={ location.Phone } data-work-time={ location.WorkTime } data-link-map={ *location.LinkMap }
        data-link-google={ "https://maps.google.com/maps?q=" + location.Lat + "," + location.Lng } data-map={ maplocation }>
        <div class="location__thumb">
            <img src={ templates.ImgURL( location.Img ) } alt={ location.Name }>
        </div>
        <div class="location__info">
            <h3 class="location__info__name">
                { location.Name }
            </h3>
            <address class="location__info__address">
                <svg class="address__icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <use xlink:href="#location-solid-icon" />
                </svg>
                <p class="address__text">{ location.Address }</p>
            </address>
            <address class="location__info__phone">
                @iconSvgCop("phone-solid-icon", "phone__icon")
                <p class="phone-number__text"> { location.Phone }</p>
            </address>
        </div>
    </article>
}