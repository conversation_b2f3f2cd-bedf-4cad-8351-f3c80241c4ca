.popover-modal-delay-change-page {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
  transition: all 250ms ease-in-out;
  font-size: 1.2rem;
  display: none;
}
.popover-modal-delay-change-page.active {
  display: block;
}
.popover-modal-delay-change-page.active .popover-content {
  transform: scale(1);
  opacity: 1;
}
.popover-modal-delay-change-page.active .overlay {
  opacity: 1;
}
.popover-modal-delay-change-page .overlay {
  position: absolute;
  z-index: 0;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(51, 51, 51, 0.5);
  opacity: 1;
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
}
.popover-modal-delay-change-page .popover-content {
  z-index: 9;
  background-color: white;
  position: absolute;
  bottom: 4em;
  right: 6em;
  padding: 1.5em;
  border-radius: var(--radius-md);
}

.popover-modal-delay-change-page .ribbon-white {
  left: 0;
  top: -28px;
  width: 65%;
  height: 40px;
  border-top-left-radius: var(--radius-md);
  border-top-right-radius: var(--radius-md);
  background-color: white;
  position: absolute;
  z-index: -1;
}
.popover-modal-delay-change-page .title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5em;
  font-weight: 500;
  color: var(--neutral-200);
}
.popover-modal-delay-change-page .title__left {
  font-size: 2.1em;
}
.popover-modal-delay-change-page .title__right {
  font-size: 1.3em;
  font-weight: 400;
}
.popover-modal-delay-change-page .popover-content {
  border-bottom-left-radius: var(--radius-md);
  border-top-right-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
  border-top-left-radius: 0;
  padding-top: 0;
  max-width: 65em;
}
.popover-modal-delay-change-page .content {
  background-color: var(--primary-800);
  color: white;
  padding: 1.6em;
  border-radius: var(--radius-sm);
  font-size: 1.3em;
  display: flex;
  flex-direction: column;
  gap: 1.5em;
  font-weight: 400;
}
.popover-modal-delay-change-page .content a {
  color: var(--third-400);
}

@media (max-width: 1099px) {
  .popover-modal-delay-change-page .title__left {
    font-size: 1.3em;
  }
  .popover-modal-delay-change-page .title__right {
    font-size: 0.8em;
  }
  .popover-modal-delay-change-page .content {
    font-size: 1em;
  }
  .popover-modal-delay-change-page .popover-content {
    bottom: 12em;
    left: 1em;
    right: 1em;
    margin: auto;
  }
}
.modal-locations {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
  transition: all 200ms ease-in-out;
  font-size: 1.2rem;
  display: none;
}
.modal-locations.active {
  display: block;
}
.modal-locations.active .locations {
  transform: scale(1);
  opacity: 1;
}
.modal-locations.active .overlay {
  opacity: 1;
}
.modal-locations .overlay {
  position: absolute;
  z-index: 0;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(51, 51, 51, 0.5);
  opacity: 0;
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
}
.modal-locations .locations {
  visibility: visible;
  transform: scale(0.7);
  opacity: 0;
  transform-origin: top bottom;
  transition: all 200ms ease-in-out;
  background-color: white;
  position: absolute;
  border-radius: var(--radius-lg);
  border: 2px solid var(--primary-500);
  overflow: hidden;
  gap: 1em;
  flex-direction: row;
  display: flex;
  position: fixed;
  right: 0;
  bottom: 0;
  top: 0;
  left: 0;
  margin: auto;
  z-index: 99;
  width: -moz-fit-content;
  width: fit-content;
  height: -moz-fit-content;
  height: fit-content;
  font-size: 1em;
  max-width: 95%;
}
.modal-locations .locations .number-code {
  text-align: center;
  width: 100%;
  position: absolute;
  bottom: 1.2em;
  left: 0;
  right: 0;
  color: #06887f;
  font-size: 1em;
}

.header-title {
  flex: 8;
  position: relative;
  color: white;
  padding: 0em 3em;
  background-image: linear-gradient(to right top, #0a9385, #18a496, #32a89a, #58bbb0, #aae3dc, #c4f0eb);
  -webkit-clip-path: ellipse(55% 80% at 45% 50%);
          clip-path: ellipse(55% 80% at 45% 50%);
  display: flex;
  justify-content: center;
  align-items: center;
}
.header-title .title {
  width: 36.7em;
  aspect-ratio: 367/79;
}
.header-title .title img {
  height: inherit;
  -o-object-fit: cover;
     object-fit: cover;
}
.header-title .title__main {
  font-size: 3.5rem;
  font-weight: bold;
  text-shadow: 0.1em 0.1em 0.2em var(--primary-800);
}
.header-title .title__sub {
  font-size: 2.5rem;
  font-weight: 600;
  text-shadow: 0.1em 0.1em 0.2em var(--primary-800);
}
.header-title .key-visual {
  position: absolute;
  width: 87%;
  z-index: -1;
  transform: rotate(4deg);
}

.btn-close-modal {
  position: absolute;
  top: 0.5em;
  right: 0.5em;
  width: 1.5em;
  height: 1.5em;
  border-radius: 50%;
  background-color: transparent;
  text-align: center;
  line-height: 1.3em;
  font-weight: 400;
  cursor: pointer;
  font-size: 2.2em;
  color: var(--primary-500);
  transition: all 250ms ease-in-out;
  border: 2px solid;
  z-index: 99;
}
.btn-close-modal:hover {
  color: white;
  border-color: var(--primary-800);
  background-color: var(--primary-800);
}

.location-list {
  flex: 4;
  position: relative;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: repeat(4, 1fr);
  gap: 1em;
  padding: 4.5em 3em 1.5em;
  padding-bottom: 4.5em;
}

.location-item {
  cursor: pointer;
  transition: all 200ms ease-in-out;
  background-color: white;
  padding: 1em 4em;
  transform: scale(1);
  border-radius: var(--radius-sm);
  text-align: center;
  border: 1px solid transparent;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.location-item img {
  width: 70%;
  margin: auto;
  aspect-ratio: 15/9;
}
.location-item:hover {
  box-shadow: 2px 1px 6px rgba(51, 48, 0, 0.1254901961);
  transform: scale(1.05);
  border-color: var(--primary-200);
}
.location-item:hover img {
  filter: grayscale(0);
}

/* Define the animation */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 1;
  }
  to {
    transform: translateX(0);
    opacity: 0;
  }
}
@media (max-width: 1499px) {
  .modal-locations {
    font-size: 1rem;
  }
}
@media (max-width: 1399px) {
  .modal-locations {
    font-size: 0.8rem;
  }
}
@media (max-width: 1099px) {
  .modal-locations .locations {
    left: 0;
    right: 0;
  }
  .header-title .title__main {
    font-size: 2em;
  }
}
@media (max-width: 575px) {
  .modal-locations .locations {
    width: 80%;
    flex-direction: column;
  }
  .header-title {
    flex: auto;
    height: 20svh;
    -webkit-clip-path: ellipse(65% 60% at 50% 40%);
            clip-path: ellipse(65% 60% at 50% 40%);
  }
  .location-item img {
    width: 55%;
  }
}
.glow-on-hover:before {
  content: "";
  background: linear-gradient(45deg, #c9cf4d, #51caaf, #16c4dc, #00ffd5, #dfda53);
  position: absolute;
  top: -2px;
  left: -2px;
  background-size: 400%;
  z-index: -1;
  filter: blur(5px);
  width: calc(100% + 4px);
  height: calc(100% + 4px);
  animation: glowing 20s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  border-radius: 10px;
}
.glow-on-hover:after {
  z-index: -1;
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background: white;
  left: 0;
  top: 0;
  border-radius: 10px;
}
.glow-on-hover:hover:before {
  opacity: 1;
}

@keyframes glowing {
  0% {
    background-position: 0 0;
  }
  50% {
    background-position: 400% 0;
  }
  100% {
    background-position: 0 0;
  }
}
.fast-action-controls {
  font-size: 0.625vw;
  position: fixed;
  right: 0.8em;
  bottom: 3.5em;
  display: flex;
  align-items: flex-end;
  flex-direction: column;
  gap: 1.5em;
  color: var(--neutral-400);
  z-index: 42;
}
.fast-action-controls .action-item {
  text-align: center;
  display: flex;
  flex-direction: row;
  border-radius: var(--radius-sm);
  padding: 0.5em;
  color: white;
  font-size: 1.2em;
  background-color: var(--primary-500);
  vertical-align: middle;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease-in;
  border-radius: 0.5em;
  height: 3em;
  max-width: -moz-fit-content;
  max-width: fit-content;
  box-shadow: 2px 2px 5px rgba(1, 95, 85, 0.4509803922);
  padding: 0.5em 0.8em;
}
.fast-action-controls .action-item.active {
  background-color: var(--primary-800);
}
.fast-action-controls .action-item img {
  width: 2em;
  height: 2em;
}
.fast-action-controls .action-item__title {
  transition: all 250ms ease-in;
  margin: 0;
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  opacity: 1;
  margin-left: 0.4em;
}
.fast-action-controls .action-item#btn-location-round-id img {
  height: 1.5em;
}
.fast-action-controls .action-item:hover .action-item__title {
  width: 100%;
  opacity: 1;
  margin-left: 0.4em;
}
.fast-action-controls .action-item#location-id {
  display: none;
}

@media (max-width: 1099px) {
  .fast-action-controls {
    flex-direction: row;
        bottom: 7em;
        left: 0;
        right: 0;
        margin: auto;
        justify-content: center;
        font-size: 1.25vw;
  }
}
@media (min-width: 1100px) {
  .fast-action-controls .action-item {
    font-size: 1.5em;
  }
  .fast-action-controls .action-item__title {
    width: 0;
    padding: 0;
    opacity: 0;
    margin: 0;
  }
  .fast-action-controls .action-item#location-id {
    display: flex;
  }
}
@media (max-width: 575px) {
    .fast-action-controls {
        flex-direction: row;
        bottom: 7em;
        left: 0;
        right: 0;
        margin: auto;
        justify-content: center;
        font-size: 2vw;
    }
}

@media (min-width: 319px) and (max-width: 376px) {
    .fast-action-controls {
        bottom: 10em;
        font-size: 2.2vw;
    }
}    