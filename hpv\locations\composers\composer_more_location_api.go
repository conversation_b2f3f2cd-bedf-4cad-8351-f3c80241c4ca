package composers

import (
	"webgo/hpv/locations/repository"
	"webgo/hpv/locations/transport/apis"
	"webgo/hpv/locations/usecase"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/component/gormc"
	"webgo/pkg/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type composerApiMoreLocations interface {
	MoreLocationApi() fiber.Handler
}

func ComposerMoreLocationApiService(serviceCtx sctx.ServiceContext) composerApiMoreLocations {
	db := serviceCtx.MustGet(configs.KeyCompGorm).(gormc.GormComponent).GetDB()
	log := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web")

	repo := repository.NewLocationsRepo(db)
	usc := usecase.NewMoreLocationsUsc(repo, log)

	api := apis.MoreLocationApi(usc)
	return api
}
