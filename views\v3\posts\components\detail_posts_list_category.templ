package components

import "webgo/hpv/postsv3/transport/responses"
import "webgo/pkg/gos/templates"
import "webgo/pkg/gos/utils"

templ PostListCategory(posts *[]responses.PostListCategory) {
    <section class="posts-related">
        <div class="posts-related-list posts-related-list--grid4">    
            if posts != nil && len(*posts) > 0 {
                for _, post := range *posts {
                    @PostListCategoryItem(post)
                }
            }
        </div>
    </section>
}

templ PostListCategoryItem(post responses.PostListCategory) {
    <article class="posts-related-item">
        <figure class="posts-related-item-img">
            <a href={templates.SafeURL( post.PagePath )} title={ post.Title }>
                <img src={ templates.ImgURL(post.Img) } alt={ post.Title }>
            </a>                 
        </figure>
        <header class="posts-related-item-header">
            <h3 class="posts-related-item-title">
                <a class="line-clamp-3" href={templates.SafeURL( post.PagePath )} title={ post.Title }> { post.Title } </a>
            </h3>
            <p class="posts-related-item-des">
                { post.Description }
            </p>
            <ul class="posts-related-item-meta">
                <li class="posts-related-item-meta-item">
                    <span class="posts-related-item-meta-icon">
                        <svg xmlns:xlink="http://www.w3.org/1999/xlink">
                            <use xlink:href="#liked-icon" fill="currentColor"></use>
                        </svg>
                    </span>           
                    <span>100</span>
                </li>
                <li class="posts-related-item-meta-item">
                    <span class="posts-related-item-meta-icon">
                        <svg xmlns:xlink="http://www.w3.org/1999/xlink">
                            <use xlink:href="#eye-icon" fill="currentColor"></use>
                        </svg>
                    </span>           
                    <span>{ utils.FormatNumberWithDot(post.Views) }</span>
                </li>
            </ul>    
        </header>
    </article>  
}