package usecase

import (
	"context"
	"webgo/hpv/common/enums"
	"webgo/hpv/entity"
	"webgo/hpv/locations/transport/requests"
	"webgo/pkg/gos/utils"
	"webgo/pkg/sctx"
)

type ApiLocationRepo interface {
	FindLocationsRepo(ctx context.Context, filter *utils.Filters) ([]*entity.LocationsEntity, error)
}

type apiLocationUsc struct {
	repo   ApiLocationRepo
	logger sctx.Logger
}

func NewApiLocationUsc(repo ApiLocationRepo, logger sctx.Logger) *apiLocationUsc {
	return &apiLocationUsc{
		repo:   repo,
		logger: logger,
	}
}

/**
 * api search location
 */
func (usc *apiLocationUsc) SearchLocationApi(ctx context.Context, payload *requests.SearchLocationReq) ([]*entity.LocationsEntity, error) {
	filter := utils.Filters{
		Columns: &[]string{
			"id", "name", "img", "page_path", "address", "phone", "work_time", "link_map", "map", "lat", "lng",
		},
		PageSize: enums.LIMIT_20,
		OrderBy:  &[]string{"id DESC"},
	}

	conds := map[string]interface{}{
		"status": 1,
	}
	if payload.ProvinceFakeId != nil && *payload.ProvinceFakeId != "" {
		conds["city_id"] = *payload.ProvinceFakeId
	}
	if payload.DistrictFakeId != nil && *payload.DistrictFakeId != "" {
		conds["district_id"] = *payload.DistrictFakeId
	}
	if payload.SearchAddress != nil && *payload.SearchAddress != "" {
		conds["address ILIKE ?"] = "%" + *payload.SearchAddress + "%"
	}

	filter.Conds = &conds

	locations, err := usc.repo.FindLocationsRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return locations, nil
}
