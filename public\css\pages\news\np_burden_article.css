.ana-col {
    margin-top: 2em;
    gap: 1em;
    display: flex;
    flex-direction: column;
    font-size: 0.652vw;
}
.ana-col__thumb{
    width: 31.5%;
    position: relative;
    aspect-ratio: 4/3;
}
.ana-col__title{
    font-size: 2em;
    font-weight: 700;
    line-height: 1.3em;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis
}
.ana__desc{
    font-size: 1.4em;
    font-weight: 300;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: initial;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
}
.nitem__bottom{
    position: absolute;
    left: 6%;
    bottom: 4%;
    right: 3%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.nitem__support{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2em;
}
.ana-col__support-item{
    font-size: 1.1em;
    font-weight: 400;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5em;
    color: rgba(0, 149, 133, 1);
}
.ana-col__content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 68.5%;
    padding: 2em 2em 1em 2em;
    gap: 1em;
}
.ana-articles {
    display: flex;
    flex-direction: column;
    gap: 1em;
}
.nitem.hidden {
    display: none;
}
.ana-articles .nitem { 
    display: flex;
    gap: 1em;
    transition: all 0.3s ease-in-out;
    background: rgba(246, 248, 250, 1);
    border-top-left-radius: 1em;
    border-bottom-left-radius: 1em;
    overflow: hidden;
}
.ana-articles .nitem:hover .ana__thumb-img { 
    transform: scale(1.05);
    transition: transform 0.3s linear;
}
.menu-line {
    position: relative;
    height: 1px;
    background-color: #D0D0D0;
    width: 75%;
    margin-top: 1em;
    bottom: 0;
    left: 0;
}
.btn-show-more {
    background: none;
    border: none;
    cursor: pointer;
    transition: transform 0.3s;
    width: 3.6em;
    aspect-ratio: 1;
    margin-top: 1em;
}
/* Responsive */
@media (max-width: 768px) {
    .ana-col {
        font-size: 1.25vw;
    }
}
@media (max-width: 575px) {
    .ana-main .nitem{
        flex-direction: row;
    }
}