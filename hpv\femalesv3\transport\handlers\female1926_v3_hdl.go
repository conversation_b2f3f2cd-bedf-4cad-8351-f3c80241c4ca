package handlers

import (
	"context"
	"errors"
	"webgo/hpv/femalesv3/transport/responses"
	"webgo/pkg/gos/templates"
	"webgo/pkg/sctx/core"
	"webgo/views/v3/females"

	"github.com/gofiber/fiber/v2"
)

type Female1926V3PostUsc interface {
	ListFemale1926V3Usc(ctx context.Context) (*responses.Female1926V3Resp, error)
}

type female1926V3Hdl struct {
	usc Female1926V3PostUsc
}

func NewFemale1926V3Hdl(usc Female1926V3PostUsc) *female1926V3Hdl {
	return &female1926V3Hdl{
		usc: usc,
	}
}

/***
 * 
 * Page: Female 1926
 */

func (h *female1926V3Hdl) ListFemale1926V3Hdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		datas, err := h.usc.ListFemale1926V3Usc(c.Context())
		if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
			return c.Redirect("/page-404")
		}
			return templates.Render(c, females.Female1926(datas))
	}
}