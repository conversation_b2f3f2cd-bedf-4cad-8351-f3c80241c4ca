package females

import "webgo/pkg/gos/templates"
import "webgo/views/v3/layouts"
import "webgo/hpv/femalesv3/transport/responses"
import "webgo/views/v3/females/components/female1926"


templ Female1926(datas *responses.Female1926V3Resp) {
    @layouts.Master(datas.Seo,[]templ.Component{head1926()}, FooterFemale1926(datas),female1926JS()) {
        <main class="container container-content">
            <div class="main-container" id="main-container">
                @female1926.Female1926Banner()
                @female1926.Female1926ContentGoal()
                @female1926.Female1926ContentResult()
                @female1926.Female1926ContentFact()
                @female1926.Female1926ContentProtection()

                if datas != nil && len(datas.PostNew) > 0{
                    @female1926.Female1926ContentArticle(datas.PostNew)                
                }
                
                @female1926.Female1926SubFooter()

            </div>
        </main>    
    }
}

templ head1926() {
    <link rel="stylesheet" href={templates.AssetURL("/asset/js/lib/swiper/swiper-bundle.min.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/female-19-26/female-19-26-adapt.css")}>
}

templ female1926JS() {
    <script type="text/javascript" src={templates.AssetURL("/asset/js/lib/swiper/swiper-bundle.min.js")}></script>
    <script type="text/javascript" src={templates.AssetURL("/asset/js/pages/male-19-26.js")}></script>

    <script type="text/javascript">
      (() => {
         const scriptElement = document.createElement('script');
         scriptElement.id = "js-load-lib-swiper"
         document.body.appendChild(scriptElement);
      })()
   </script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
         const lazyImages = document.querySelectorAll("img.lazyload");

         const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach((entry) => {
               if (entry.isIntersecting) {
                  const img = entry.target;
                  img.src = img.dataset.src;
                  if (img.dataset.srcset) {
                     img.srcset = img.dataset.srcset;
                  }
                  img.classList.remove("lazyload");
                  observer.unobserve(img);
               }
            });
         });

         lazyImages.forEach((img) => {
            imageObserver.observe(img);
         });
      });
      document.querySelectorAll(".open-popup-locations").forEach(button => {
      button.addEventListener("click", () => {
         document.querySelector("#btn-location-round-id").click()
      });
      });
   </script>
   <script>
         document.addEventListener('DOMContentLoaded', function () {
         const contentContainers = document.querySelectorAll('.content-footer');
         
         contentContainers.forEach((content, index) => {
            const showMoreButton = document.createElement('div');
            showMoreButton.classList.add('show-more');
            showMoreButton.innerHTML = 'Xem thêm &#9660';
            content.parentNode.insertBefore(showMoreButton, content.nextSibling);
   
            showMoreButton.addEventListener('click', function () {
               if (content.style.maxHeight) {
               content.style.maxHeight = null;
               showMoreButton.innerHTML = 'Xem thêm &#9660'; 
               } else {
               content.style.maxHeight = content.scrollHeight + 'px';
               showMoreButton.innerHTML = 'Ẩn &#9650'; 
               }
            });
         });
         });
   </script>
}

func FooterFemale1926(datas *responses.Female1926V3Resp) templ.Component{
    if datas != nil && datas.Options != nil {
        if banner, ok := datas.Options["footer-female-1926"]; ok {
            return templ.Raw(banner)
        }
    }    
        return nil
}