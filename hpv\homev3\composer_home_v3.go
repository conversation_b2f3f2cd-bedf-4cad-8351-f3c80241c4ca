package homev3

import (
	"webgo/hpv/homev3/transport/handlers"
	"webgo/hpv/homev3/usecase"
	"webgo/hpv/repository"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/component/gormc"
	"webgo/pkg/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type composerHome interface {
	ListHomeHdl() fiber.Handler
	ListHomePageSpeedHdl() fiber.Handler
}

func ComposerHomeSVC(serviceCtx sctx.ServiceContext) composerHome {
	db := serviceCtx.MustGet(configs.KeyCompGorm).(gormc.GormComponent).GetDB()
	log := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web")

	repoPost := repository.NewPostRepo(db)
	repoOption := repository.NewOptionValueRepo(db)
	repoCategory := repository.NewCategoryRepo(db)
	
	usc := usecase.NewHomeUsc(repoPost,repoCategory, repoOption, log)
	hdl := handlers.NewHomeHdl(usc)

	return hdl
}
