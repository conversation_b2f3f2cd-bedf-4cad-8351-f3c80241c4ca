@media (min-width: 1024px) {
    .container.container-detail{
        max-width: 960px !important;
        margin: 0 auto;
        padding: 0 !important;
        margin-top: 2vw;
    }
}
@media (max-width: 1099px) {
    .root-container{
        overflow-y: unset!important;
    }
}
@media (min-width: 1100px) {
    .root-container {
        gap: unset;
    }
}

:root{
    --color-white: #ffffff;
    --color-black: #000000;
    --black-rgb: 0,0,0;
    --color-gray: 0,149,133; 
    --color-yellow: 255, 222, 0;
    --color-green: 0, 126, 130;
}

html{
    scroll-behavior: smooth;
    font-size: 62.5%;
}

.root-container{
    justify-content: unset!important;
}

.border{
    border: 1px solid #000;
}
.mb-1{
    margin-bottom: 1rem!important;
}

.mt-2{
    margin-top: 2rem!important;
}


.mb-2{
    margin-bottom: 2rem!important;
}

.mb-3{
    margin-bottom: 3rem!important;
}

.mb-4{
    margin-bottom: 4rem!important;
}

.my-2{
    margin-top: 2rem;
    margin-bottom: 2rem;
}

.my-3{
    margin-top: 3rem;
    margin-bottom: 3rem;
}

.px-2{
    padding-left: 2rem;
    padding-right: 2rem;
}

.fs-10{
    font-size: 1em;
}
.fs-12{
    font-size: 1.2em;
}
.fs-13{
    font-size: 1.3em;
}
.fs-14{
    font-size: 1.4em;
}
.fs-16{
    font-size: 1.6em;
}

.ff-unbounded{
    font-family: "Unbounded", sans-serif;
}

.fw-400{
    font-weight: 400;
}

.fw-700{
    font-weight: 700;
}

.line-clamp-3{
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
}

/* --- post detail --- */
.container-detail{
    line-height: 1.2;
    font-size: 1rem;
}

.breadcrumb{
    display: flex;
    align-items: center;
    font-size: 1rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.breadcrumb-item{
    font-size: 1.3em;
}

.breadcrumb-item+.breadcrumb-item::before {   
    content: ">";
    margin: 0 .5em;
    font-size: 1.2em;
    line-height: 2em;    
}

.title-detail{
    font-size: 2.4rem;
    font-weight: 500;
    line-height: 1.35;
    margin: 0;
    padding: 0;
}

.detail-note{
    font-size: 1rem;
    font-family: "Unbounded", sans-serif;
    margin-bottom: 1em;
}

.detail-des{
    font-size: 1.3em;
    font-weight: 400;
    margin-bottom: 1.5em;
    text-align: justify;
}

.article-meta{
    list-style: none;
    margin: 0;
    padding: 0;
    font-size: 1rem;
    margin-top: 2em;
    display: flex;
    align-items: center;
    gap: 1.5em;
}

.article-meta-item{
    font-size: 1.2em;
    display: flex;
    align-items: center;
    gap: 0.3em;
}
.article-meta-item.active >.article-meta-icon, .article-meta-item:hover >.article-meta-icon{
    background-color: rgb(var(--color-gray));
}

.article-meta-icon{
    width: 2.5em;
    height: 2.5em;
    border-radius: 50%;
    background-color: rgb(var(--color-gray), 0.2);
    padding: 0.3em;
    position: relative;    
    transition: all 0.3s ease-in-out;
}

.article-meta-icon > svg{   
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);    
    font-size: 1.5em;   
}

.article-meta-icon > svg > use{
    stroke: var(--color-black)!important;
    stroke-width: 2px;
}
.article-meta-item.active >.article-meta-icon>svg>use, .article-meta-item:hover >.article-meta-icon >svg >use{
    stroke: #fff!important;
    stroke-width: 2px;
}

.article-meta-item >span{
    font-size: 1em;
    font-style: italic;
    font-weight: 400;
}
.detail-img{
    width: 100%;
    height: auto;   
    border-radius: 1rem;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}
#copyToast {
    visibility: hidden;
    min-width: 140px;
    background-color: var(--neutral-200);
    color: #fff;
    text-align: center;
    border-radius: 4px;
    padding: 8px 12px;
    /* position: fixed; */
    z-index: 1000;
    bottom: 80px; /* vị trí phía trên icon */
    left: 50%;
    transform: translateX(0%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  #copyToast.show {
    visibility: visible;
    opacity: 1;
  }

/* ---------------------------------table of content ------------------ */
.table-of-conent-wrapper{
    font-size: 1rem;
    padding: 3em;
    background-color: rgba(var(--color-gray), 0.1);
    border-radius: 1.5em;
    overflow: hidden; 
}
.table-of-conent{
    max-height: 25em;
}
.custom-scroll {
    overflow-y: auto;
}
  
/* scroll bar track */
.custom-scroll::-webkit-scrollbar {
    width: 0.2em;
}
  
.custom-scroll::-webkit-scrollbar-track {
    background-color: rgba(var(--color-gray), 0.4);
    border-radius: 5em;
}
  
/* scroll bar thumb */
.custom-scroll::-webkit-scrollbar-thumb {
    background: var(--color-white);
    border-radius: 5em;
    box-shadow: 0 0 0 0.2em var(--color-white);
    transition: all 0.3s ease-in-out;   
}

.table-of-conent-title {
    font-size: 2.4em;
    font-weight: 600;
    margin-bottom: 0.5em;
}  

.table-of-conent-list {
    counter-reset: section;
    font-size: 1rem;
    padding-left: 2em;
}
  
.table-of-conent-list > li {
    font-size: 1.3em;
    counter-increment: section;
    list-style: none;
    position: relative;
    margin-bottom: 0.5em;
}

.table-of-conent-list > li >a{
    font-weight: 400;
}
.table-of-conent-list a:hover{
    color: #f86f00;
    text-decoration: none;
}  
  
.table-of-conent-list > li::before {
    content: counter(section) ". ";   
    position: absolute;
    left: -1.5em;    
}
  
.table-of-conent-list > li > ul {
    margin-top: 0.5em;
    counter-reset: subsection;
    padding-left: 2em;
}
  
.table-of-conent-list > li > ul > li {
    counter-increment: subsection;
    list-style: none;
    position: relative;
    margin-bottom: 0.5em;
    padding-right: 1em;
}
  
.table-of-conent-list > li > ul > li::before {
    content: counter(section) "." counter(subsection) " ";
    position: absolute;
    left: -2em;
}

/* --- content --- */
.content-detail{
    font-size: 1rem;
    line-height: 1.2;   
}
.content-detail h2{
    font-size: 1.8em;
    font-weight: 500;   
    margin-bottom: 0.7em;   
}

.content-detail h3{
    font-size: 1.6em;  
    font-weight: 400;   
    margin-bottom: 0.7em;
}

.content-detail p{
    font-size: 1.4em;
    margin-bottom: 0.7em;
    text-align: justify;
}

.content-detail img{
    max-width: 100%;
    height: auto;
    display: block;
    margin: 1em auto;
    border-radius: 0.5em;;
}

.content-detail ul,
.content-detail ol {
    font-size: 1.3em;    
    padding-left: 5em;
    margin-bottom: 1em;

}
.content-detail ul li,
.content-detail ol li {
    margin-bottom: 0.3em;
    position: relative;
}

.content-detail h2 + ul,.content-detail h2 + ol{
    padding-left: 6em;
}


.content-detail ul li::marker {
    color: rgba(var(--color-gray), 0.9);

}

.content-detail blockquote {
    border-left: 4px solid #ccc;
    padding-left: 1em;
    font-style: italic;
    color: #666;
    margin: 1.5em 0;
}

.content-detail table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
    font-size: 0.95em;
}

.content-detail table,
.content-detail th,
.content-detail td {
    border: 1px solid #ddd;
}

.content-detail th,
.content-detail td {
    padding: 0.75em;
    text-align: left;
}

.content-detail iframe,
.content-detail video {
    width: 100%;
    max-width: 100%;
    height: auto;
    margin: 1em 0;
    border-radius: 0.5em;
}

/* .content-detail h2 + p  {
    padding-left: 2em;
}

.content-detail h2 + p + h3{
    padding-left: 1.6em;
}

.content-detail h2 + h3 {
    padding-left: 2em;
}

.content-detail h3 + p {
    padding-left: 4em;
} */

.content-detail a{
    color: rgb(var(--color-gray));
}
.content-detail a:hover{
    color: #f86f00;
    text-decoration: none;
}

/* --- detail mores --- */
.detail-mores{
    font-size: 1rem;
    padding: 0 2em;
    color: rgb(var(--color-gray));
}

.detail-mores-title{
    font-size: 1.6em;
    font-weight: 500;
    margin-bottom: 0.9em;
    color: var(--color-black);
}

.detail-mores-list {
    display: flex;
    flex-direction: column;
    gap: 1em;
    font-weight: 400;
    padding-left: 2em;
    list-style: none;
}

.detail-mores-list > li {
    display: flex;
    align-items: center;
    gap: 0.5em;
    font-size: 1.2em;
    font-weight: 400;
    position: relative;
    padding-left: 1em;
    transition: all 0.3s ease-in-out;
    cursor: pointer;
}

.detail-mores-list > li::before {
    content: "•";
    position: absolute;
    left: 0;
    color: rgb(var(--color-gray));
    font-size: 1.2em;
}

.detail-mores-list > li:hover {
    color: #f86f00;
    transform: translateX(1em);
}

/* --- banner mino --- */
.banner-mino{
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: 1em;   
    font-size: 1rem;
    position: relative;
    padding: 0 2em 0 4em;
    margin-left: 2em;
    margin-right: 2em;
}

.banner-mino::before{
    content: "";
    position: absolute;
    top: 4.5em;
    left: 0;
    right: 0;
    bottom: 0;  
    z-index: -1;
    border-radius: 2em;
    background: linear-gradient(267.1deg, #5CC7BE 0%, #009585 100%);
}

.banner-mino-content{
    width: 60%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content:center;
    align-items: center;
    padding-bottom: 2em
}

.banner-mino-title--small{
    font-size: 2.35em;
    font-weight: 400;
    color: var(--color-white);
}

.banner-mino-title{
    font-size: 4em;
    line-height: 1.1;
    font-weight: 700;
    color: var(--color-white);
    margin-bottom: 0.5em;
}

.banner-mino-btns{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1.5em;
}

.banner-mino-btn{
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 0.2em;
    font-size: 1em;
    padding: 0.8em 5em;
    border-radius: 4em;
    background-color: rgb(var(--color-yellow));
    cursor: pointer;
    transition: all 0.3s linear;
}

.banner-mino-btn:hover {
    transform: translateY(-3px);
    background-color: var(--secondary-700);
}

.banner-mino-btn span{
    font-size: 1.2em;
    line-height: 1.1;
    font-weight: 500;
}

.banner-mino-img{
    width: 40%;
    height: 90%;
    display: flex;
    justify-content: center;
    align-items: center;  
}

/* --- detail-reference ---*/
.detail-reference{
    font-size: 1rem;
    padding: 0 2em;
    color: rgba(var(--black-rgb), 0.7);
}

.detail-reference-title{
    font-size: 1.2em;
    font-weight: 500;
    margin-bottom: 1em;
    color: rgba(var(--black-rgb), 0.7);
}
.detail-reference-list{
    font-size: 1em;
    list-style:decimal; 
    padding-left: 3em;
}
.detail-reference-list > li{
    margin-bottom: 0.8em;
    padding-left: 0.2em;
    text-align: justify;
    transition: all 0.3s ease-in-out;
}

.detail-reference-list > li:hover{
    color: #f86f00;
    text-decoration: none;
}

/* --- post related ---*/
.posts-related{
    font-size: 1rem;
    padding: 0 2em;
    border-top: 2px solid rgb(var(--color-gray));
}

.btn-top{
    display: flex;
    align-items: center;
    justify-content: center;
    width:3.6em;
    height: 3.6em;
    border-radius: 50%;
    border: 1px solid rgb(var(--color-gray));
    margin: 1.5em auto;
    position: relative;
    transition: all 0.3s ease-in-out;
}

.btn-top>svg{   
    width: 3em;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    fill: rgb(var(--color-gray));
}

.btn-top>svg>use{
    stroke: rgb(var(--color-gray));
    stroke-width: 1px;
}

.btn-top:hover{
  background-color: rgb(var(--color-yellow));
}
.btn-top:hover >svg>use{
    stroke: rgba(var(--black-rgb), 0.7);   
}
.btn-top:hover >svg{
    fill: rgba(var(--black-rgb), 0.7);   
}

.posts-related-list--c4{
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1.5em;
    margin-top: 5rem;
    margin-bottom: 3rem;
}

.posts-related-list--c4 > * {
    flex: 0 0 calc(25% - 1.5em);
    box-sizing: border-box;   
}

.posts-related-list--grid4{
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5em;
    margin-top: 5rem;
    margin-bottom: 3rem;
}


/* --- post related item ---*/
.posts-related-item{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 1rem;
    gap: 1em;   
    border-radius: 1em;
    background-color: rgba(var(--color-gray), 0.1);
    transition: all 0.3s ease-in-out; 
}
.posts-related-item:hover{
    background-color: rgb(var(--color-yellow), 0.1);
    transform: translateY(-0.5em);    
}

.posts-related-item-img{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;  
   

}
.posts-related-item-img img{
    aspect-ratio: 4 / 3;
    overflow: hidden;
    width: 100%;
    object-fit: cover;
    object-position: center;
    border-top-left-radius: 1em;
    border-top-right-radius: 1em;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.posts-related-item-header{
    display: flex;
    flex-direction: column;   
    gap: 1em;
    font-size: 1em;   
    padding: 0 1em 1em 1em
}

.posts-related-item-title{
    height: 3.8em;
    font-size: 1.4em;
    font-weight: 400;
}

.posts-related-item-title:hover{
    color: #f86f00;
    text-decoration: none;
}

.posts-related-item-des{
    display: none;
}

.posts-related-item-meta{
    display: flex;
    align-items: center;
    gap: 1em;
    font-size: 1em;
    list-style: none;
    margin: 0;
}

.posts-related-item-meta-item{
    display: flex;
    align-items: center;
    gap: 0.2em;
    font-size: 1em;
    color: rgba(var(--black-rgb), 0.8);
    ;
}

.posts-related-item-meta-icon{
    width: 1.7em;
    height: 1.7em;
    position: relative;    
    transition: all 0.3s ease-in-out;
}
.posts-related-item-meta-icon > svg{   
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);    
    font-size: 1.5em;  
    width: 100%;
    height: auto; 
}

.posts-related-item-meta-icon > svg > use{
    stroke: rgb(var(--color-gray));
    stroke-width: 2px;
}

.posts-related-item-meta-item>span {
    font-size: 1em;
    font-style: italic;
    font-weight: 400;
}

/* --- footer hpv ---*/
.footer-hpv{  
    font-size: 1rem;
    padding: 3em;
    background-color: rgb(var(--color-green));   
}

.footer-content{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 1em;    
}

.footer-content-text{
    font-size: 1.2em;
    font-weight: 300;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: rgba(var(--black-rgb), 0.7);  
}

.footer-policy-list{
    font-size: 1.2em;
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 400;
}

.footer-policy-list li {
    position: relative;
}

.footer-policy-list li +li::before {
    content: "|";
    font-size: 1.2em;
    margin: 0 0.7em;
    color: rgba(var(--black-rgb), 0.7);
    font-weight: 400;
}

/* Section - Question */
.section-question-moh {
    position: relative;
    width: 100%;
    height: auto;
    margin: 5svh auto;
    font-size: 0.3vw;
    padding: 0 2rem;
}
.section-question-moh.section-2 {
    margin-bottom: 5svh;
}

.group-question {
    position: relative;
    width: 100%;
    height: 25em;
    border-radius: 20px;
    overflow: hidden;
}

.group-question__left {
    width: 56%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding: 2em 15em 2em 2em;
    background: linear-gradient(90deg, #029986 0%, #3ABAA8 72.16%, rgba(74, 194, 179, 0) 100%);
    z-index: 1;
    position: relative;
}

.group-question__btn {
    display: block;
    width: fit-content;
    transition: all 250ms ease-in-out;
    text-transform: uppercase;
    background-color: var(--secondary-500);
    color: var(--neutral-100);
    margin-top: 1em;
    padding: 1em 2em;
    border-radius: 3.2em;
    font-weight: 500;
    line-height: 1.1;
    text-align: center;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}

.group-question__btn:hover {
    background-color: var(--secondary-700);
    transform: translateY(-2px);
}

.group-question__disc {
    font-weight: 700;
    font-size: 3em;
    line-height: 1.1;
    color: var(--neutral-900);
    text-align: center;
}
.group-question__fact {
    font-weight: 300;
    font-size: 2.4em;
    line-height: 1.2;
    text-align: center;
    color: var(--neutral-900);
}

.group-question__btn span {
    font-weight: 700;
    font-size: 1.6em;
    line-height: 1.4;
}

.group-question__btn img {
    width: 2.4em;
    margin-left: 10px;
    transform: translateX(-5px);
    filter: brightness(0) saturate(100%) invert(0%) sepia(87%) saturate(7449%) hue-rotate(242deg) brightness(111%) contrast(90%);
}
.hpv-data {
    display: flex;
    gap: 3em;
}
.hpv-data__female,
.hpv-data__male {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2em;
}
.hpv-data__male {
    padding-left: 2em;
}
.hpv-data__male::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 90%;
    aspect-ratio: 1;
    background: var(--neutral-900);
    border-radius: 50%;
    z-index: 0;
}
.hpv-data__percent {
    position: relative;
    width: 12.7em;
    aspect-ratio: 1;
    background: linear-gradient(223.3deg, #FFDE00 13.84%, #84F38D 50.05%, #0DFBF0 86.25%);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
}
.hpv-data__percent::before {
    content: "";
    position: absolute;
    width: 90%;
    height: 90%;
    aspect-ratio: 1;
    background: var(--primary-700);
    border-radius: 50%;
    z-index: 0;
}
.hpv-data__percent span{
    position: relative;
    font-weight: 900;
    font-size: 5.8em;
    line-height: 1.2;
    color: var(--neutral-900);
    z-index: 1;
}
.hpv-data__percent::after{
    content: "%";
    position: absolute;
    aspect-ratio: 1;
    font-size: 2em;
    font-weight: 700;
    color: var(--neutral-100);
    background: var(--neutral-900);
    border-radius: 50%;
    padding: 0.2em;
    line-height: 1.3;
    right: -30%;
    top: 30%;
    transform: translate(-50%, -50%);
}
.icon-female {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5em;
}
.icon-female svg {
    width: auto;
    height: 3.4em;
}
.icon-female span {
    font-weight: 700;
    font-size: 2em;
    line-height: 1.3;
    text-align: center;
    color: var(--neutral-900);  
}

.group-question__right {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: fit-content;
}

.group-question__content {
    display: flex;
    flex-direction: column;
    gap: 2em;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    position: absolute;
    width: 100%;
}

.group-question-bg {
    height: inherit;
    width: 100%;
}

.group-question-bg img {
    height: 100%;
    object-fit: cover;
    width: 100%;
}

.group-question-bg picture {
    height: 100%;
    object-fit: cover;
}

.group-tick {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1em;
    cursor: pointer;
}

.btn-tick-prevent {
    position: relative;
    display: block;
    width: fit-content;
    transition: all 250ms ease-in-out;
    text-transform: uppercase;
    background-color: #ffffffb5;
    color: var(--primary-500);
    padding: 1em 2em;
    border: 2px solid var(--primary-500);
    border-radius: 2em;
    font-weight: 500;
    line-height: 1.1;
    text-align: center;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
    overflow: hidden;
    animation: scaleAnimation 1.5s infinite ease-in-out;
    animation-delay: 1s; /* Initial delay */
}
@keyframes scaleAnimation {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.03);
    }
    75% {
        transform: scale(1.03);
    }
}
.btn-tick-prevent::before {
    content: "";
    position: absolute;
    width: 100px;
    height: 100%;
    background-image: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 30%,
      rgba(255, 255, 255, 0.9),
      rgba(255, 255, 255, 0) 70%
    );
    top: 0;
    left: -100px;
    opacity: 0.6;
    animation: shine 2s ease-out infinite;
}
@keyframes shine {
    0% {
      left: -100px;
    }
  
    60% {
      left: 100%;
    }
  
    to {
      left: 100%;
    }
  }
  

.btn-tick-prevent:hover {
    background-color: var(--primary-500);
    color: var(--neutral-900);
}

.btn-tick-prevent.active {
    background-color: var(--primary-500);
    color: var(--neutral-900);
    border: 2px solid var(--primary-500);
    animation: none;
    transform: scale(1.03);
}

.btn-tick-prevent span {
    font-weight: 700;
    font-size: 1.6em;
    line-height: 1.2;
}

.title-moh-banner {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: var(--neutral-100);
    gap: 0.8em;
}

.title-moh-banner .logo-byt-moh {
    width: 4.8em;
    aspect-ratio: 1;
}

.title-moh-banner__sub {
    font-weight: 700;
    font-size: 1.6em;
    line-height: 1.3;
    margin-top: 0.3em;
    text-transform: uppercase;
    font-family: Unbounded;
}
.tagline-moh-banner {
    width: 45.1em;
    aspect-ratio: 451 / 91;
}
.tagline-moh-banner img{
    width: 100%;
    object-fit: cover;
}

.title-moh-banner__main {
    font-weight: 700;
    font-size: 2.8em;
    line-height: 1.1;
    padding: 0.3em 0.8em;
    background-color: var(--secondary-500);
    border-radius: 2em;
    text-transform: uppercase;
}

.tick-border {
    width: 7em;
    aspect-ratio: 181 / 179;
}

.line-tick {
    position: absolute;
    z-index: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: none;
}

.line-tick .line-moh {
    width: 114em;
    /* aspect-ratio: 1676 / 438; */
    aspect-ratio: 1986 / 464;
    left: 1em;
    position: relative;
    top: -10em;
    clip-path: inset(0 100% 0 0);
    transition: clip-path 1s ease-in-out;
}

.line-tick .hand-img {
    position: relative;
    top: -4.9em;
    left: -8em;
    width: 24.9em;
    aspect-ratio: 290 / 188;
    opacity: 0;
    transition: opacity 0.5s ease-in-out 0.7s;
}
.section-5 .group-question__left {
    padding: 5em 15em 5em 2em;
}