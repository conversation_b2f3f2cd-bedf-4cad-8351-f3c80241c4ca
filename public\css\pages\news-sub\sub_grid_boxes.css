.sub-grid {
    display: flex;
    flex-direction: column;
    gap: 1em;
    margin-top: 2em;
    font-size: 0.625vw;
}
.sub-grid__wrapper {
    width: 100%;
    height: fit-content;
    display: flex;
    gap: 2em;
}
.sub-grid__left {
    position: relative;
    width: 70%;
}
.sub-grid__bottom {
    width: 100%;
    position: absolute;
    display: flex;
    justify-content: space-between;
    align-items: end;
    padding: 3em;
    bottom: 0;
    font-size: 0.625vw;
    gap: 1em;
    background: linear-gradient(to top, black, transparent);
    border-radius: 0 0 1em 1em;
}
.sub-grid__bottom-title {
    font-size: 2.6em;
    font-weight: 600;
    color: rgb(255, 255, 255);
}
.sub-grid__bottom-meta {
    display: flex;
    align-items: center;
    font-size: 0.9em;
}
.sub__support-icon {
    width: 1.368em;
    aspect-ratio: 1;
    filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(8%) hue-rotate(156deg) brightness(102%) contrast(105%);
}
.sub__support-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.bottom-meta_support {
    display: flex;
    gap: 2em;
}
.bottom-meta_support-item {
    font-size: 1.2em;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5em;
    color: rgb(255 255 255);
}
.sub-grid__bottom-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 1em;
}
.sub-grid__right {
    width: 30%;
    gap: 2em;
    display: flex;
    flex-direction: column;
}
.sub-grid-right-item{
    display: flex;  
    font-size: 0.625vw;
}
.sub-grid-right-thumb{
    width: 100%;
    position: relative;
    aspect-ratio: 16 / 9;
}
.sub-overlay-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.35);
    z-index: 1;
    border-radius: 1em;
}
.sub-grid-right-thumb__link {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 1em;
}
.sub-grid__thumb-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 1em 0 0 1em;
}
.sub-grid-right-content {
    position: absolute;
    display: flex;
    flex-direction: column;
    font-size: 0.5vw;
    bottom: 0;
    z-index: 2;
    padding: 2em 2em 1em 2em;
    gap: 1em;
}
.sub-grid-right-content__title {
    color: #f6f6f6;
    font-size: 2em;
    font-weight: 500;
}
.sub-grid-right__bottom {
    font-size: 1.3em;
}
.sub-grid-right__support{
    display: flex;
    align-items: center;
    gap: 2em;
}
.sub-grid-right__support-item {
    display: flex;
    align-items: center;
    gap: 0.3em;
}
.sub-grid-right__support-item span{
    color: rgb(255 255 255);
    font-style: italic;
}
@media (max-width: 768px) {
    .sub-grid__wrapper {
        width: 100%;
        height: fit-content;
        display: flex;
        flex-direction: column;
        gap: 2em;
    }
    .sub-grid__left {
        position: relative;
        width: 100%;
    }
    .sub-grid__right {
        width: 100%;
        display: flex;
        flex-direction: row;
        gap: 2em;
    }
    .sub-grid-right-item {
        width: 100%;
    }
}

@media (max-width: 575px) {
    .sub-grid__wrapper {
        width: 100%;
        height: fit-content;
        display: flex;
        flex-direction: column;
        gap: 2em;
    }
    .sub-grid__left {
        position: relative;
        width: 100%;
    }
    .sub-grid__right {
        width: 100%;
        display: flex;
        flex-direction: row;
        gap: 2em;
    }
    .sub-grid-right-item {
        width: 100%;
    }
}















