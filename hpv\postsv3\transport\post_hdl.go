package transport

import (
	"context"
	"errors"
	"fmt"
	"webgo/hpv/common/errs"
	"webgo/hpv/postsv3/transport/requests"
	"webgo/hpv/postsv3/transport/responses"
	"webgo/pkg/gos/templates"
	"webgo/views/v3/posts"

	"github.com/gofiber/fiber/v2"
)

type PostUsc interface {
	DetailPostUsc(ctx context.Context, param *requests.DetailPostReq) (*responses.DetailPostResp, error)
}

type postHdl struct {
	usc PostUsc
}

func NewPostHdl(usc PostUsc) *postHdl {
	return &postHdl{
		usc: usc,
	}
}

/**
 * Detail Post /:slug-:id.html
 */
func (h *postHdl) DetailPostHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var param requests.DetailPostReq
		param.Slug = c.Params("slug")
		if err := param.Validate(); err != nil {
			return c.Redirect("/page-404", fiber.StatusTemporaryRedirect)
		}

		data, err := h.usc.DetailPostUsc(c.Context(), &param)
		if err != nil {
			if errors.Is(err, errs.ErrValidateDetailSlugNotMatch) && data.Post != nil && data.Post.Slug != "" {
				pathRedirect := fmt.Sprintf("/%s-%s.html", data.Post.Slug, param.ID)
				return c.Redirect(pathRedirect, fiber.StatusMovedPermanently)
			}
			er := c.Redirect("/page-404", fiber.StatusTemporaryRedirect)
			return er
		}

		if data == nil {
			return nil
		}

		return templates.Render(c, posts.DetailV3(data.Post.Seo, data.Post, data.PostListCategory, data.Options))

	}
}
