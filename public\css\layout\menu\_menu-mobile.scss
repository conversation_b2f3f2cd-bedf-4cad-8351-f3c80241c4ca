.popup-menu-mobile {
   position: fixed;
   left: 0;
   top: 0;
   right: 0;
   bottom: 0;
   z-index: 45;
   transition: all 250ms ease-in-out;

   .overlay {
      position: absolute;
      z-index: 0;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      background-color: #3333336a;
   }

   .container-menu-mobile {
      position: absolute;
      z-index: 2;
      left: 20px;
      top: 20px;
      max-width: 30em;
      background: linear-gradient(130.73deg, rgba(255, 255, 255, 0.8352) -15%, rgba(189, 255, 219, 0.96) 110%);


      width: fit-content;
      background-color: white;
      border-radius: var(--radius-md);
      overflow: hidden;

      // padding: 15px;
      display: flex;
      flex-direction: column;
      // gap: 10px;

      color: var(--neutral-400);

      .logos {
         width: 100%;
         justify-content: center;
         display: flex;
         padding: 1.5em;
         background-color: var(--neutral-900);

         img {
            height: 3.6em
         }
      }

      .nav-menu {
         display: flex;
         flex-direction: column;
         gap: 5px;
         padding: 1.5em 1em;

         &__item {
            font-size: 15px;
            color: var(--primary-500);
            display: inline;

            a {
               padding: 10px 12px;
               width: 100%;
               display: inline-block;
            }

            &:hover {
               background: linear-gradient(269.45deg, rgba(32, 133, 233, 0.11) -10.77%, var(--primary-500) 77.84%);
               border-radius: 16px;
               backdrop-filter: blur(4px);
               color: var(--neutral-900);
            }

            &.is-male:hover {
               background: linear-gradient(269.45deg, rgba(32, 133, 233, 0.11) -10.77%, var(--third-500) 77.84%);
               border-radius: 16px;
               backdrop-filter: blur(4px);
               color: var(--neutral-900);
            }

            &.is-female:hover {
               background: linear-gradient(269.45deg, rgba(255, 229, 15, 0.11) -10.77%, var(--secondary-500) 77.84%);
               border-radius: 16px;
               backdrop-filter: blur(4px);
               color: var(--neutral-100);
            }
         }

      }

      .popup-menu-mobile__footer {
         display: block;
         padding: 1.5em 2em;
         position: relative;

         &:after {
            content: "";
            background: #6D6D6D8C;
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            height: 1px;
            width: 50%;
         }

         .content-menu-footer {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            gap: 1em;

            span {
               line-height: 1.1;
            }

            .policy {
               display: flex;
               flex-direction: column;
               justify-content: center;
               align-items: center;
               line-height: 1.4;
               font-weight: 600;
            }
         }
      }
   }
}