package errs

import (
	"net/http"
	"webgo/pkg/sctx/core"

	"github.com/gofiber/fiber/v2"
)

func WriteErrorResponse(c *fiber.Ctx, err error) error {
	if errSt, ok := err.(core.StatusCodeCarrier); ok {
		return c.Status(errSt.StatusCode()).JSON(errSt)
	}
	return c.Status(http.StatusInternalServerError).JSON(core.ErrInternalServerError.WithError(err.Error()))
}

func ReturnErrsForApi(ctx *fiber.Ctx, msgErr interface{}) error {
	return WriteErrorResponse(ctx, core.ErrBadRequest.WithDetail("msg", msgErr))
}

func ReturnErrForApi(ctx *fiber.Ctx, msgErr string) error {
	return WriteErrorResponse(ctx, core.ErrBadRequest.WithError(msgErr))
}

func ReturnDataTableError(ctx *fiber.Ctx, msgErr string) error {
	return ctx.Status(http.StatusBadRequest).JSON(fiber.Map{
		"draw":            0,
		"recordsTotal":    0,
		"recordsFiltered": 0,
		"data":            []interface{}{},
		"error":           msgErr,
	})
}

func ReturnDataTableErrors(ctx *fiber.Ctx, msgErr []*string) error {
	var errs []string
	for _, err := range msgErr {
		if err != nil {
			errs = append(errs, *err)
		}
	}

	return ctx.Status(http.StatusBadRequest).JSON(fiber.Map{
		"draw":            0,
		"recordsTotal":    0,
		"recordsFiltered": 0,
		"data":            []interface{}{},
		"error":           errs,
	})
}
