.border {
    border: 1px solid black;
}

.main-menu .nav-menu>ul {
    justify-content: center;
}

.main-menu {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.menu-header {
    padding-left: 1%;
    padding-right: 1%;
}

.main-menu {
    background-color: var(--neutral-900);
}

.menu-header.is-sticky {
    background-color: transparent;
}

.container-video-fluid {
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.container-video-fluid.visible {
    opacity: 1;
}

@media (max-width: 1099px) {
    body {
        max-height: none;
        min-height: 100svh;
        overflow-y: auto;
    }

    .root-container {
        max-height: none;
        min-height: 100svh;
    }

    .menu-header,
    .menu-header.is-sticky {
        background: var(--neutral-900);
    }

    .main-menu {
        box-shadow: none;
    }
}

/* ============ SECTION 1 BANNER ==============
===============================================*/

.banner-kv-moh {
    position: relative;
    display: flex;
    width: 100%;
    height: calc(100svh - 95px);
    justify-content: center;
    font-size: 1rem;
}

.bg-moh-ldp {
    width: 100%;
    height: calc(100svh - 30px);
    object-fit: cover;
    position: absolute;
}

.bg-moh-ldp img {
    width: 100%;
    height: inherit;
    object-fit: cover;
}

.bg-moh-ldp picture {
    width: 100%;
    height: inherit;
    object-fit: cover;
}

.top-banner-moh {
    position: absolute;
    top: 0%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 0.31vw;
    gap: 1em;
}

.title-moh-ldp {
    display: flex;
    flex-direction: column;
    text-align: center;
    align-items: center;
    margin-top: 2em;
    font-size: 1em;
    z-index: 1;
}

.title-moh__sub {
    font-weight: 700;
    font-size: 3.2em;
    line-height: 1.1;
}

.title-moh__main {
    font-weight: 400;
    font-size: 2.6em;
    line-height: 1.1;
    margin-top: 0.5em;
}

.logo-byt-moh {
    width: 23em;
}

.logo-byt-moh img {
    width: 100%;
}

.tagline-moh {
    width: 108em;
    margin-top: 0;
    aspect-ratio: 1362 / 194;
    z-index: 1;
}

.tagline-moh img {
    width: 100%;
    object-fit: cover;
}

.bottom-kv-moh {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
}

.bottom-kv-moh img {
    width: 100%;
    object-fit: cover;
}

/* RESPONSIVE SECTION 1 BANNER */
@media only screen and (min-width: 1600px) {
    .top-banner-moh {
        font-size: 0.3vw;
        top: 0%;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .top-banner-moh {
        font-size: 0.24vw;
        top: 0%;
    }

    .tagline-moh {
        width: 120em;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .banner-kv-moh {
        font-size: 0.6rem;
    }

    .top-banner-moh {
        font-size: 0.23vw;
        top: 0%;
    }

    .bg-moh-ldp {
        height: 100vh;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .top-banner-moh {
        font-size: 0.29vw;
        top: 0%;
    }

    .fast-action-controls {
        display: none;
    }
}

@media only screen and (min-width: 769px) and (max-width: 992px) {
    .top-banner-moh {
        font-size: 0.6vw;
        top: 7%;
    }

    .fast-action-controls {
        display: none;
    }

    .banner-kv-moh {
        height: calc(100svh - 120px);
    }
}

@media (min-width: 576px) and (max-width: 769px) {
    .fast-action-controls {
        display: none;
    }

    .top-banner-moh {
        font-size: 0.5vw;
        top: 16%;
    }

    .banner-kv-moh {
        height: calc(100vh - 95px);
    }
}

@media (max-width: 575px) {
    .fast-action-controls {
        display: none;
    }

    .logo-byt-moh {
        width: 23vw;
        aspect-ratio: unset;
    }

    .top-banner-moh {
        font-size: 0.6vw;
        top: 3%;
    }

    .tagline-moh {
        width: 90vw;
    }

    /* .banner-kv-moh {
        height: 100%;
    } */
    .bottom-kv-moh {
        display: block;
    }

    .bg-moh-ldp {
        width: 100%;
        height: calc(100vh - 40vw);
        object-fit: cover;
        position: absolute;
    }

    .banner-kv-moh {
        height: calc(100svh - 155px);
    }
}

/* ============ SECTION 2 AVERAGE COUNT==============
=====================================================*/
.content-title {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #FFD700;
    border-radius: 3.5em;
}

.content-title__yellow {
    font-size: 3em;
    font-weight: bold;
    color: black;
    z-index: 0;
    padding: 0.5em 1em 0.5em 2em;
}

.content-title__teal {
    position: relative;
    font-size: 3em;
    font-weight: bold;
    color: white;
    background-color: #008080;
    align-items: center;
    justify-content: center;
    padding: 0.5em 2em;
    border-radius: 2em;
    z-index: 1;
    left: 3px;
}

sup {
    font-size: 0.6em;
    /* Số 1 sẽ nhỏ hơn */
    vertical-align: super;
    /* Đặt số 1 lên cao */
}


.icon-female {
    display: flex;
    justify-content: center;
    align-items: center;
    /* gap: 0.5em; */
}

.icon-female svg {
    width: auto;
    height: 3.4em;
}

.icon-female span {
    font-weight: 700;
    font-size: 2em;
    line-height: 1.3;
    text-align: center;
    color: var(--neutral-100);
}

.section-container-2 {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding-top: 10%;
    overflow: hidden;
    background: var(--neutral-900);
}

.section-container-2 .tick-border {
    position: relative;
    left: -6vw;
    width: 10em;
    aspect-ratio: 181 / 179;
}

.content-wrapper {
    width: fit-content;
    height: fit-content;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    gap: 4em;
    font-size: 0.7vw;
}

.content-average {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    gap: 7em;
}

.content-title-v2 img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.content-title-v2 {
    width: 108em;
}

.count-protection {
    position: relative;
    width: 100%;
    height: 76svh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: linear-gradient(181.56deg, #FFFFFF 34.41%, #EEFFF0 98.94%);
    margin-top: 11%;
}

.count-content {
    position: relative;
    width: fit-content;
    height: fit-content;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    flex-direction: row;
    font-size: 1.1vw;
    bottom: 25%;
}

.count-content-1 {
    font-size: 2.5em;
    font-weight: 900;
    color: rgba(1, 131, 129, 1);
}

.count-content-2 {
    font-size: 10em;
    font-weight: 900;
    background: linear-gradient(308.18deg, #FFDE00 -22.33%, #009885);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 0.9;
}

.count-content::before {
    content: "";
    position: absolute;
    width: 10em;
    height: 100%;
    background-image: linear-gradient(100deg,
            rgba(255, 255, 255, 0) 30%,
            rgba(255, 255, 255, 0.9),
            rgba(255, 255, 255, 0) 70%);
    top: 0;
    left: -100px;
    opacity: 0.6;
    animation: shine2 2.5s ease-in-out infinite;
}

@keyframes shine2 {
    0% {
        left: -100px;
    }

    60% {
        left: 65%;
    }

    100% {
        left: 100%;
    }
}

.count-content-3 {
    font-size: 2.5em;
    font-weight: 900;
    color: rgba(1, 131, 129, 1);
}

.count-content-h3 {
    position: relative;
    font-size: 2.5em;
    font-weight: 500;
    color: var(--neutral-100);
    bottom: 25%;
}

.family-obj {
    width: 30em;
    position: absolute;
    top: auto;
    left: -1%;
    bottom: 9vw;
}

.family-obj img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.aura-obj img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.aura-obj {
    position: absolute;
    width: 70%;
    top: 16%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 0;
}

.model-obj {
    position: absolute;
    width: 36em;
    right: 0;
    bottom: 21%;
}

.model-obj img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.bottom-block {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.bottom-block {
    height: auto;
    position: absolute;
    bottom: 4%;
    z-index: 2;
    margin: 0;
    padding: 0;
}

.btn-tick-prevent {
    position: relative;
    display: block;
    width: fit-content;
    transition: all 250ms ease-in-out;
    text-transform: uppercase;
    background-color: #ffffffb5;
    color: var(--primary-500);
    padding: 1em 2em;
    border: 2px solid var(--primary-500);
    border-radius: 2em;
    font-weight: 500;
    line-height: 1.1;
    text-align: center;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
    overflow: hidden;
    animation: scaleAnimation 1.5s infinite ease-in-out;
    animation-delay: 1s;
    /* Initial delay */
}

@keyframes scaleAnimation {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.03);
    }

    75% {
        transform: scale(1.03);
    }
}

.btn-tick-prevent::before {
    content: "";
    position: absolute;
    width: 100px;
    height: 100%;
    background-image: linear-gradient(120deg,
            rgba(255, 255, 255, 0) 30%,
            rgba(255, 255, 255, 0.9),
            rgba(255, 255, 255, 0) 70%);
    top: 0;
    left: -100px;
    opacity: 0.6;
    animation: shine 2s ease-out infinite;
}

@keyframes shine {
    0% {
        left: -100px;
    }

    60% {
        left: 100%;
    }

    to {
        left: 100%;
    }
}

*/ .btn-tick-prevent:hover {
    background-color: var(--neutral-900);
    color: var(--primary-900);
}

.btn-tick-prevent.active {
    background-color: var(--primary-500);
    color: var(--neutral-900);
    border: 2px solid var(--primary-500);
    animation: none;
    transform: scale(1.03);
}

.btn-tick-prevent span {
    font-weight: 700;
    font-size: 1.6em;
    line-height: 1.2;
}

.section-container-2 .btn-tick-prevent {
    position: relative;
    bottom: 0;
    left: 0;
    font-size: 0.8vw;
    border: unset;
    padding: 1.5em 3em;
    border-radius: 3em;
}

.white-shape-banner {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    aspect-ratio: 3840 / 664;
}

.section-container-2 .line-tick .line-moh {
    width: 100%;
    aspect-ratio: 3840 / 652;
    left: 0;
    position: relative;
    top: -7vw;
    transition: clip-path 1s ease-in-out;
}

.section-container-2 .group-tick {
    z-index: 2;
}

.section-container-2 .btn-tick-prevent::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 4px;
    border-radius: 3em;
    background: linear-gradient(180deg, #FEE000 0%, rgba(254, 224, 0, 0.2) 36.5%, rgba(70, 193, 178, 0) 100%);
    -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
    z-index: -1;
}

.way-protection {
    width: 90%;
    max-width: 90%;
    display: flex;
    flex-direction: column;
    text-align: center;
    align-items: center;
    font-size: 0.45vw;
    background: linear-gradient(0deg, rgba(132, 243, 158, 0.24) 0%, rgba(132, 243, 184, 0) 47.12%);
    padding: 3em;
    border-radius: 3rem;
    margin-top: 4vw;
}

.way-protection-title {
    font-size: 4em;
    font-weight: 600;
    position: relative;
    display: inline-block;
    padding: 1em 2em;
    margin-bottom: 1em;
}

.way-protection-title::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 0.35em;
    border-radius: 3em;
    /* background: linear-gradient(180deg, #FEE000 0%, rgba(254, 224, 0, 0.2) 36.5%, rgba(70, 193, 178, 0) 100%); */
    background: linear-gradient(180deg, rgba(70, 193, 178, 0.8) 0%, rgba(254, 224, 0, 0.2) 63.5%, rgba(254, 224, 0, 0) 100%);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
    z-index: 0;
}

/* .way-protection-title::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -5px;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, #f5dc54, #a4eb83, #00f0ff);
    border-radius: 2px;
  } */
.way-protection-list {
    width: 100%;
    display: flex;
    justify-content: space-between;
    gap: 4em;
    flex-wrap: wrap;
}

.way-protection-list__item {
    display: flex;
    align-items: center;
    gap: 2em;
    text-align: left;
}

.way-protection-list__item span {
    font-size: 3em;
    font-weight: 600;
}

.way-protection-list__item img {
    width: 8.5em;
    height: 8.5em;
}

.count-protection-note {
    font-size: 1.5em;
    font-weight: 400;
    text-align: center;
    margin: 0;
    left: 52%;
    position: absolute;
    bottom: -1vw;
    transform: translateX(-50%);
    white-space: nowrap;
}

.count-protection-note br {
    display: none;
}

/* RESPONSIVE SECTION 2 AVERAGE COUNT*/
@media only screen and (min-width: 1600px) {
    .section-container-2 {
        padding-top: 3%;

    }

    .family-obj {
        width: 24%;
        top: auto;
        left: 0;
        bottom: 6vw;
    }

    .model-obj {
        width: 20%;
        right: 0;
        bottom: 10vw;
    }

    .count-content-h3 {
        font-size: 4em;
    }

    .count-protection-note {
        font-size: 1.5em;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .content-wrapper {
        font-size: 0.7vw;
    }

    .section-question-moh {
        font-size: 0.9rem;
    }

    .group-question__left {
        width: 55%;
    }

    .section-container-2 .tick-border {
        width: 7vw;
        left: -6%;
    }

    .section-container-2 .line-tick .line-moh {
        top: -7vw;
    }

    .family-obj {
        width: 24%;
        top: auto;
        left: 0;
        bottom: 6vw;
    }

    .model-obj {
        width: 20%;
        right: 0;
        bottom: 10vw;
    }

    .count-protection-note {
        font-size: 1.5em;
        margin: 0;
        left: 58%;
        bottom: -1vw;
        transform: translateX(-50%);
        white-space: nowrap;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .section-question-moh {
        font-size: 0.75rem;
    }

    .group-question__left {
        width: 53%;
    }

    .section-container-2 .line-tick .line-moh {
        top: -7vw;
    }

    .family-obj {
        width: 24%;
        top: auto;
        left: 0;
        bottom: 6vw;
    }

    .model-obj {
        width: 20%;
        right: 0;
        bottom: 10vw;
    }

    .banner-kv-moh {
        /* height: 95svh; */
        height: calc(100svh - 90px);
    }

    .content-wrapper {
        font-size: 0.6vw;
    }

    .count-protection-note {
        font-size: 1.4em;
        margin: 0;
        left: 58%;
        bottom: -3vw;
        transform: translateX(-50%);
        white-space: nowrap;
    }
}

@media only screen and (min-width: 991px) and (max-width: 1199px) {
    .family-obj {
        width: 24%;
        top: auto;
        left: 0;
        bottom: 6vw;
    }

    .model-obj {
        width: 20%;
        right: 0;
        bottom: 10vw;
    }

    .count-protection {
        height: 58svh;
    }

    .count-content-h3 {
        font-size: 2em;
    }

    .section-question-moh {
        font-size: 0.7rem;
    }

    .section-container-2 .line-tick .line-moh {
        top: -7.3vw;
    }

    .count-protection-note {
        font-size: 1.4em;
        margin: 0;
        left: 50%;
        bottom: -3vw;
        transform: translateX(-50%);
        white-space: nowrap;
    }

    .count-protection-note br {
        display: block;
    }
}

@media only screen and (min-width: 769px) and (max-width: 992px) {
    .section-question-moh {
        font-size: 1rem;
    }

    .group-question {
        display: flex;
        flex-direction: column-reverse;
        height: auto;
        aspect-ratio: 416 / 442;
        width: 80%;
        margin: 0 auto;
    }

    .group-question__disc {
        font-weight: 500;
        line-height: 1.1;
        color: var(--neutral-900);
        text-align: center;
    }

    .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        padding-top: 10svh;
    }

    .section-5 .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        padding-top: 10svh;
    }

    .group-question__right {
        position: relative;
        margin-bottom: -14%;
    }

    .group-tick {
        margin-top: 3em;
        font-size: 0.9em;
    }

    .line-tick .line-moh {
        width: 114em;
        aspect-ratio: 1986 / 464;
        left: -1em;
        position: relative;
        top: -11em;
        clip-path: inset(0 100% 0 0);
    }

    .line-tick .hand-img {
        position: relative;
        top: -4.5em;
        width: 29em;
        left: -20em;
        aspect-ratio: 290 / 188;
        opacity: 0;
        transition: opacity 0.5s ease -in-out 0.8s;
    }

    .line-tick {
        width: max-content;
        font-size: 0.8em;
    }

    .group-question__content {
        gap: 5svh;
    }

    .section-container-2 .line-tick .line-moh {
        top: -7vw;
    }

    .section-container-2 .line-tick {
        width: 100%;
    }

    .bottom-block {
        bottom: 3%;
    }

    .section-container-2 .tick-border {
        left: -4%;
    }

    .section-container-2 .btn-tick-prevent {
        font-size: 0.9vw;
        left: -3%;
    }

    .count-content {
        bottom: 3vw;
    }

    .count-content-h3 {
        font-size: 1.7em;
        bottom: 3vw;
    }

    .count-protection {
        margin-top: 0;
        height: 45svh;
    }

    .family-obj {
        width: 24%;
        top: auto;
        left: 0;
        bottom: 5vw;
    }

    .model-obj {
        width: 19%;
        right: 0;
        bottom: 10vw;
    }

    .aura-obj {
        width: 70%;
        top: 40%;
        left: 50%;
    }

    .count-protection-note {
        font-size: 1.2em;
        margin: 0;
        left: 50%;
        bottom: -3vw;
        transform: translateX(-50%);
        white-space: nowrap;
    }

    .count-protection-note br {
        display: block;
    }
}

@media (min-width: 575px) and (max-width: 769px) {
    .group-question__content {
        gap: 5svh;
        font-size: 1.2em;
    }

    .bottom-kv-moh {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
    }

    .tagline-moh {
        width: 83.2em;
        margin-top: -0;
        aspect-ratio: 2144 / 868;
    }

    .section-question-moh {
        font-size: 0.9rem;
    }

    .group-question {
        display: flex;
        flex-direction: column-reverse;
        height: auto;
        aspect-ratio: 416 / 442;
        width: 90%;
        margin: 0 auto;
    }

    .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        font-size: 1em;
        padding-top: 10svh;
    }

    .section-5 .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        padding-top: 10svh;
    }

    .group-question__right {
        position: relative;
        margin-bottom: -14%;
    }

    .group-tick {
        font-size: 1.1em;
    }

    .line-tick .line-moh {
        width: 114em;
        aspect-ratio: 1986 / 464;
        left: 3em;
        position: relative;
        top: -11em;
        clip-path: inset(0 100% 0 0);
    }

    .line-tick .hand-img {
        position: relative;
        top: -3.5em;
        width: 26em;
        left: -24em;
        aspect-ratio: 290 / 188;
        opacity: 0;
        transition: opacity 0.5s ease -in-out 0.8s;
    }

    .line-tick {
        width: max-content;
    }

    .group-question__content {
        gap: 5svh;
    }

    .section-container-2 .line-tick .line-moh {
        top: -7vw;
    }

    .section-container-2 .line-tick {
        width: 100%;
    }

    .bottom-block {
        bottom: 2%;
    }

    .section-container-2 .tick-border {
        width: 7em;
        left: -4%;
    }

    .section-container-2 .btn-tick-prevent {
        font-size: 0.9vw;
    }

    .count-content {
        font-size: 1.2vw;
        bottom: 7vw;
    }

    .count-content-h3 {
        font-size: 1.7em;
        bottom: 6vw;
    }

    .count-protection {
        margin-top: 0;
        height: 38svh;
    }

    .family-obj {
        width: 24%;
        top: auto;
        left: 0;
        bottom: 6vw;
    }

    .model-obj {
        width: 21%;
        right: 0;
        bottom: 10vw;
    }

    .aura-obj {
        width: 70%;
        top: 30%;
        left: 50%;
    }

    .content-wrapper {
        font-size: 0.95vw;
    }

    .content-average {
        gap: 4em;
        flex-direction: column;
    }

    .group-question__fact {
        width: 100%;
        white-space: wrap;
    }

    .group-question__fact br {
        display: none;
    }

    .content-wrapper {
        font-size: 0.8vw;
    }

    .content-average {
        gap: 4em;
        flex-direction: column;
    }

    .section-container-2 .group-question__fact {
        width: 85%;
        white-space: wrap;
        font-size: 2.5em;
        font-weight: 400;
        text-align: center;
    }

    .section-container-2 .group-question__fact br {
        display: none;
    }

    .hpv-data {
        font-size: 1.3em;
    }

    .count-protection-note {
        font-size: 1.1em;
        margin: 0;
        left: 51%;
        bottom: -3vw;
        transform: translateX(-50%);
        white-space: nowrap;
    }

    .count-protection-note br {
        display: block;
    }
}

@media (max-width: 575px) {
    .section-question-moh {
        font-size: 0.7rem;
        margin: 0 auto 2svh;
    }

    .section-question-moh.section-2 {
        padding: 0;
    }

    .group-question {
        display: flex;
        flex-direction: column-reverse;
        height: auto;
        /* aspect-ratio: 416 / 442; */
        width: 100%;
        margin: 0 auto;
        border-radius: 0;
        border-bottom-left-radius: 20px;
        border-bottom-right-radius: 20px;
    }

    .group-question__left {
        font-size: 0.85em;
        width: 100%;
        height: 30svh;
        padding: 1em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        gap: 2svh;
        padding-top: 5svh;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: -15%;
    }

    .section-5 .group-question__left {
        width: 100%;
        padding: 1em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 3svh;
        padding-top: 7svh;
    }

    .group-question__right {
        position: relative;
        width: 100%;
        height: 50svh;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .group-question-bg picture {
        height: inherit;
        object-fit: cover;
        width: 100%;
    }

    .group-tick {
        font-size: 1.1em;
    }

    .line-tick .line-moh {
        width: 80em;
        aspect-ratio: 1676 / 438;
        left: -3em;
        position: relative;
        top: -8em;
    }

    .line-tick .hand-img {
        position: relative;
        top: -3em;
        left: -14em;
        width: 21em;
        aspect-ratio: 290 / 188;
    }

    .group-question__content {
        gap: 5svh;
        top: 45%;
    }

    .group-question__btn {
        margin-top: 0;
        font-size: 1.2em;
    }

    .section-container-2 .line-tick .line-moh {
        top: -5.3vw;
    }

    .section-container-2 .line-tick {
        width: 100%;
    }

    .bottom-block {
        bottom: 0%;
    }

    .section-container-2 .tick-border {
        width: 11vw;
        left: 6%;
    }

    .section-container-2 .btn-tick-prevent {
        font-size: 1.3vw;
        left: 8%;
    }

    .aura-obj {
        width: 70%;
        top: 30%;
    }

    .count-content {
        bottom: 7vw;
    }

    .count-content-h3 {
        font-size: 1.7em;
        bottom: 6vw;
    }

    .count-protection .family-obj {
        width: 30%;
        top: auto;
        left: -1%;
        bottom: 3vw;
    }

    .count-protection .model-obj {
        width: 24%;
        right: -2%;
        bottom: 7vw;
    }

    .count-protection {
        margin-top: 10%;
        height: 24svh;
    }

    .count-protection .line-tick {
        bottom: -1.7vw;
    }

    .count-content-h3 {
        font-size: 2vw;
    }

    .section-container-2 {
        margin-top: 0;
        padding-top: 0;
    }

    .content-wrapper {
        font-size: 0.95vw;
    }

    .content-average {
        gap: 4em;
        flex-direction: column;
    }

    .group-question__fact {
        width: 100%;
        white-space: wrap;
    }

    .group-question__fact br {
        display: none;
    }

    .content-wrapper {
        font-size: 0.95vw;
    }

    .content-average {
        gap: 4em;
        flex-direction: column;
    }

    .section-container-2 .group-question__fact {
        width: 85%;
        white-space: wrap;
        font-size: 3em;
        font-weight: 400;
        text-align: center;
    }

    .section-container-2 .group-question__fact br {
        display: none;
    }

    .way-protection {
        width: fit-content;
        max-width: 85%;
        font-size: 0.8vw;
        background: none;
        margin-top: 10em;
    }

    .way-protection-title {
        font-size: 3.5em;
        margin-bottom: 0.5em;
        width: 100%;
    }

    .way-protection-list {
        width: 100%;
        flex-direction: column;
        background: linear-gradient(0deg, rgba(132, 243, 158, 0.24) 0%, rgba(132, 243, 184, 0) 47.12%);
        text-align: center;
        border-radius: 4em;
        padding: 0 6em 3em;
        font-size: 0.9em;
    }

    .count-protection-note {
        font-size: 5px;
        margin: 0;
        left: 65%;
        bottom: -4vw;
        transform: translateX(-50%);
        white-space: nowrap;
    }

    .count-protection-note br {
        display: block;
    }

    .way-protection-list__item br {
        display: none;
    }

    .way-protection-list__item span {
        white-space: nowrap;
    }

    .icon-female span {
        font-size: 3em;
    }
}

@media (max-width: 376px) {
    .section-question-moh {
        font-size: 0.6rem !important;
    }

    .line-tick .hand-img {
        position: relative;
        top: -2em;
        left: -17em;
        width: 22em;
        aspect-ratio: 290 / 188;
    }

    .group-tick {
        font-size: 1.1em;
    }

    .count-protection .line-tick {
        bottom: -15%;
    }

    .count-protection {
        margin-top: 5%;
        height: 25svh;
    }

    .icon-female span {
        font-size: 3em;
    }

    .section-container-2 .group-question__fact {
        width: 100%;
        white-space: wrap;
        font-size: 2.5em;
        font-weight: 400;
        text-align: center;
        padding: 0 24px;
    }
}

/* ============ SECTION 3 VIDEO EMBED ==============
=====================================================*/

.container-video-embed {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-top: 5%;
    gap: 5em;
    padding-top: 10%;
    overflow: hidden;
}

.video-embed__title {
    width: 68vw;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1vw;
}

.video-embed__title img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.aura-obj-left {
    position: absolute;
    width: 45%;
    left: -3%;
    top: 0;
    z-index: 3;
}

.aura-obj-left img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.aura-obj-right {
    position: absolute;
    width: 17%;
    right: -3%;
    top: 14%;
    z-index: 3;
}

.aura-obj-right img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.container-video-fluid {
    width: 90%;
    margin: 0 auto;
    padding: 0 5%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 5;
}

.fluid__wrapper {
    width: 100%;
    aspect-ratio: 16 / 9;
    position: relative;
}

.fluid__wrapper iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 1em;
}

@media (max-width: 576px) {
    .container-video-embed {
        gap: 2em;
    }

    .container-video-fluid {
        width: 100%;
        padding: 0;
    }

    .fluid__wrapper iframe {
        border-radius: 0;
    }

    .aura-obj-left {
        left: -8%;
        top: 8px;
    }

    .aura-obj-right {
        width: 19%;
        right: -2%;
        top: 5%;
    }
}

/* ============ SECTION 4 CAMPAIN INFO ==============
=====================================================*/

.container-section-3 {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    background-repeat: no-repeat;
    padding-left: 5vw;
    padding-right: 5vw;
    padding-top: 15svh;
    font-size: 1rem;
    background: linear-gradient(170.14deg, #FFFFFF 20.09%, #E9FFEB 75%);
}

.wrapper-section-3 {
    display: flex;
    justify-content: center;
    gap: 2vw;
}

.wrapper-textbox {
    flex: 4;
    display: flex;
    flex-direction: column;
    /* justify-content: center; */
    padding-left: 5vw;
    padding-top: 2em;
}

.container-section-3 .text-box {
    width: 1%;
    height: fit-content;
    position: relative;
    display: table;
}

.container-section-3 .title {
    display: block;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    overflow: hidden;
    position: relative;
    white-space: nowrap;
    color: #009885;
    font-weight: 700;
    font-size: 3.6em;
    line-height: 1.1;
}

.container-section-3 .description {
    color: #333;
    margin: 4svh 0 4svh;
    font-weight: 400;
    font-size: 1.8em;
    line-height: 1.2;
    text-align: justify;
}

.wrapper-section-3 .detail-campain__cta {
    margin: unset;
    text-transform: none;
}

.container-section-3 .detail-image-banner {
    position: relative;
    flex: 6;
    right: 0;
    bottom: 0;
    aspect-ratio: 1623 / 1101;
    ;
}

.container-section-3 .img-people {
    width: inherit;
    object-fit: cover;
}

.container-section-3 .arrow-vector {
    transform: scale(0.4);
}

.container-section-3 .border-arrow {
    width: 22px;
    height: 22px;
    background-color: #ffffff32;
    border-radius: 50%;
    display: flex;
    justify-content: center;
}

.detail-campain__cta {
    display: block;
    width: fit-content;
    transition: all 250ms ease-in-out;
    text-transform: uppercase;
    background-color: var(--primary-500);
    color: var(--neutral-900);
    margin: 5svh auto 0;
    padding: 1em 2em;
    border-radius: 3.2em;
    font-weight: 500;
    line-height: 1.1;
    text-align: center;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}

.detail-campain__cta:hover {
    background-color: var(--primary-700);
    transform: translateY(-2px);
}

.detail-campain__cta span {
    font-weight: 700;
    font-size: 1.6em;
    line-height: 1.4;
}

.detail-campain__cta img {
    width: 2.4em;
    margin-left: 10px;
    transform: translateX(-5px);
}

/* RESPONSIVE SECTION CAMPAIGN INFO*/
@media only screen and (min-width: 1600px) {
    .wrapper-textbox {
        font-size: 0.6vw;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .container-section-3 {
        font-size: 0.95em;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .container-section-3 {
        font-size: 0.85em;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .container-section-3 {
        font-size: 0.75em;
    }

    .container-section-3 .description {
        margin: 2svh 0 2svh;
    }

    .wrapper-textbox {
        flex: 4;
        padding-left: 5vw;
        padding-top: 2em;
    }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
    .wrapper-section-3 {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .wrapper-textbox {
        display: flex;
        font-size: 1.1em;
        align-items: center;
        justify-content: center;
        padding-left: 0;
    }

    .container-section-3 .cta-btn {
        max-width: 13em;
        position: absolute;
        bottom: -17.5em;
        z-index: 2;
    }
}

@media (min-width: 576px) and (max-width: 769px) {
    .wrapper-section-3 {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .wrapper-textbox {
        display: flex;
        font-size: 1.1em;
        align-items: center;
        justify-content: center;
        padding: 0;
    }

    .container-section-3 .cta-btn {
        max-width: 13em;
        position: absolute;
        bottom: -17.5em;
        z-index: 2;
    }

    .container-section-3 .description {
        margin: 4svh 0 2.5svh;
    }

}

@media (max-width: 576px) {
    .wrapper-section-3 {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .wrapper-textbox {
        display: flex;
        font-size: 0.8em;
        align-items: center;
        justify-content: center;
        padding: 0;
    }

    .container-section-3 .cta-btn {
        font-size: 0.8em;
        max-width: 15em;
        position: absolute;
        bottom: -3.2em;
        padding: 0.3em 1em;
        z-index: 2;
    }

    .container-section-3 .detail-image-banner {
        height: auto;
        margin-top: 2vw;
    }

    .video-embed__title {
        width: 75vw;
    }

    .container-section-3 .description {
        margin: 4svh 0 2.5svh;
    }
}

@media (max-width: 375px) {
    .wrapper-textbox {
        display: flex;
        font-size: 0.6em;
        align-items: center;
        justify-content: center;
        padding: 0;
    }
}


/* ============ SECTION 5 DETAIL SWIPER ==============
=====================================================*/

.section-detail-campain {
    width: 100%;
    height: 100svh;
    font-size: 1rem;
    overflow: hidden;
}

.detail-campain__group {
    width: inherit;
    height: inherit;
    position: relative;
    display: flex;
}

.detail-campain__left {
    position: relative;
    height: 100%;
    width: 70vw;
}

.detail-campain__right {
    width: 30vw;
    background: linear-gradient(322.87deg, #E3FFFC 0%, #F6FFF6 100%);
    display: flex;
    align-items: center;
}

.logo-hpv {
    width: 22.2em;
    aspect-ratio: 222 / 70;
}

.logo-msd {
    width: 14.85em;
    aspect-ratio: 1485 / 620;
}

.group-logos-moh img {
    width: inherit;
    object-fit: cover;
}

.detail-campain__content {
    position: relative;
    width: 20vw;
    height: 80%;
    left: 42%;
    transform: translateX(-50%);
    font-size: 0.42vw;
}

.detail-campain__content .text-highlight {
    display: block;
    font-weight: 800;
    font-size: 2.2em;
    text-align: justify;
    margin-top: 0.5em;
    background: linear-gradient(93.36deg, #009885 0.59%, #05A8AE 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.group-question__disc {
    font-weight: 500;
    font-size: 3em;
    line-height: 1.1;
    color: var(--neutral-900);
    text-align: center;
}

.detail-campain__title {
    font-weight: 700;
    font-size: 3.2em;
    color: var(--primary-500);
}

.detail-campain__content p {
    font-weight: 400;
    font-size: 2em;
    line-height: 1.3;
    text-align: justify;
    color: #2F2F2F;
    margin-top: 3svh;
}

.swiper-container-main,
.swiper-container-thumbs {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
}

.swiper-container-main {
    height: 100%;
}

.swiper-container-thumbs {
    position: absolute;
    left: 50%;
    height: 100%;
    box-sizing: border-box;
    padding: 3% 0;
    width: 85%;
    overflow: hidden;
    transform: translateX(-50%);
}

.group-swiper-detail {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
}

.gallery__wrapper {
    display: flex;
}

.gallery__slide {
    aspect-ratio: 16 / 9;
}

.gallery__slide,
.gallery__thumb {
    background: #fff;
    text-align: center;
    font-size: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: cover;
    background-position: center;
}

.gallery__slide img,
.gallery__thumb img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gallery__thumb {
    width: 25%;
    height: 100%;
    opacity: 0.8;
    transition: 0.2s linear;
    transform: scale(0.5);
}

.gallery__thumb.swiper-slide-thumb-active {
    opacity: 1;
    transform: scale(1.2);
}

.gallery__button {
    color: #fff;
}

.group-bottom-swiper-thumb {
    width: 100%;
    /* margin: 0 10%; */
    position: absolute;
    bottom: 0;
    left: 0;
    height: 20%;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0.04%, #000000 100%);
    z-index: 1;
}

.detail-campain__left .swiper-button-prev:after,
.swiper-rtl .swiper-button-next:after {
    content: none;
}

.detail-campain__left .swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
    content: none;
}

.detail-campain__left .swiper-button-prev,
.swiper-rtl .swiper-button-next {
    left: var(--swiper-navigation-sides-offset, 3%);
    right: auto;
    width: 1.7vw;
}

.detail-campain__left .swiper-button-next,
.swiper-rtl .swiper-button-prev {
    right: var(--swiper-navigation-sides-offset, 3%);
    left: auto;
    width: 1.7vw;
}

.gallery__button--prev img,
.gallery__button--next img {
    width: 100%;
    object-fit: cover;
}

.gallery__controls--extra {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.6em;
    position: absolute;
    bottom: 0;
    margin: 0 auto;
    width: 100%;
}

.gallery__btn--prev-custom,
.gallery__btn--next-custom {
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
    width: 3em;
}

.swiper-pagination-bullets.swiper-pagination-horizontal.gallery__pagination--custom {
    width: fit-content;
}

.gallery__pagination--custom {
    display: flex;
    gap: 8px;
}

.gallery__bullet {
    width: 2em;
    height: 2em;
    background-color: #aaa;
    border-radius: 50%;
    opacity: 0.5;
    transition: 0.3s;
    cursor: pointer;
}

.gallery__bullet:hover {
    opacity: 0.8;
}

.gallery__bullet--active {
    background-color: var(--primary-500);
    opacity: 1;
    width: 4em;
    border-radius: 1em;
}

.gallery__pagination--custom {
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: center;
}

.gallery__btn--prev-custom img,
.gallery__btn--next-custom img,
.gallery__btn--prev-custom img {
    width: 100%;
    object-fit: cover;
}

.gallery__slide--video {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    height: 100%;
    /* padding-top: 2svh; */
    background: #000;
    /* Optional: padding bên trong */
    box-sizing: border-box;
}

.gallery__slide--video iframe {
    aspect-ratio: 16 / 9;
    width: 100%;
    max-height: 80%;
    border: 0;
    margin-top: 1em;
}

.youtube-thumb {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.youtube-thumb__img {
    width: 100%;
    height: auto;
    display: block;
}

.youtube-thumb__play-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6vw;
    height: 6vw;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.youtube-thumb__play-btn svg {
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.play-button {
    all: unset;
    cursor: pointer;
    display: inline-block;
}

.hat-tag {
    position: absolute;
    top: 10%;
    left: 5%;
    width: 25%;
    aspect-ratio: 752 / 220;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
}

.hat-tag img {
    width: 100%;
    object-fit: cover;
}

.moh-policy {
    font-weight: 400;
    font-size: 1em;
    line-height: 1.5;
    margin-top: 5em;
    margin-left: 3em;
}

/* RESPONSIVE SECTION DETAIL SWIPPER*/

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .detail-campain__group {
        font-size: 0.76em;
    }

    .container {
        padding-right: 0.75rem;
        padding-left: 0.75rem;
    }

    .detail-campain__content {
        font-size: 0.43vw;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .detail-campain__group {
        font-size: 0.7em;
    }

    .detail-campain__content {
        width: 21.5vw;
        font-size: 0.41vw;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .section-detail-campain {
        height: calc(100svh - 60px);
        font-size: 1rem;
    }

    .swiper-container-thumbs {
        padding: 4% 0;
    }

    .youtube-thumb__play-btn {
        width: 8vw;
        height: 8vw;
    }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
    .section-detail-campain {
        height: 100%;
        font-size: 1rem;
    }

    .detail-campain__left,
    .detail-campain__right {
        width: 100vw;
    }

    .detail-campain__group {
        flex-direction: column;
    }

    .swiper-container-thumbs {
        padding: 2% 0;
    }

    .detail-campain__content {
        position: relative;
        width: 100vw;
        height: auto;
        left: 50%;
        transform: translateX(-50%);
        font-size: 0.62vw;
        padding: 8svh;
    }

    .gallery__controls--extra {
        display: flex;
        gap: 1.6em;
        position: absolute;
        bottom: auto;
        margin: 0 auto;
        width: fit-content;
        height: fit-content;
        top: 2svh;
        left: 50%;
        transform: translateX(-50%);
    }

    .group-swiper-detail {
        background: var(--neutral-100);
    }

    .container-section-3 {
        padding-top: 3svh;
    }

    .youtube-thumb__play-btn {
        width: 12vw;
        height: 12vw;
    }
}

@media (min-width: 576px) and (max-width: 768px) {
    .section-detail-campain {
        height: 100%;
        font-size: 1rem;
    }

    .detail-campain__left,
    .detail-campain__right {
        width: 100vw;
    }

    .detail-campain__group {
        flex-direction: column;
    }

    .swiper-container-thumbs {
        padding: 2% 0;
    }

    .detail-campain__content {
        position: relative;
        width: 100vw;
        height: auto;
        left: 50%;
        transform: translateX(-50%);
        font-size: 0.75vw;
        padding: 8svh;
    }

    .gallery__controls--extra {
        display: flex;
        gap: 1.6em;
        position: absolute;
        bottom: auto;
        margin: 0 auto;
        width: fit-content;
        height: fit-content;
        top: 3svh;
        left: 50%;
        transform: translateX(-50%);
    }

    .group-swiper-detail {
        background: var(--neutral-100);
    }

    .youtube-thumb__play-btn {
        width: 14vw;
        height: 14vw;
    }
}

@media (max-width: 575px) {
    .section-detail-campain {
        height: 100%;
        font-size: 1rem;
    }

    .section-detail-campain .nowrap {
        white-space: unset;
    }

    .detail-campain__left,
    .detail-campain__right {
        width: 100vw;
    }

    .detail-campain__group {
        flex-direction: column;
    }

    .swiper-container-thumbs {
        padding: 2% 0;
    }

    .detail-campain__content {
        position: relative;
        width: 100vw;
        height: auto;
        left: 50%;
        transform: translateX(-50%);
        font-size: 1.5vw;
        padding: 4svh;
        padding-top: 8svh;
    }

    .detail-campain__title {
        font-size: 2.6em;
    }

    .gallery__controls--extra {
        display: flex;
        gap: 1.6em;
        position: absolute;
        bottom: auto;
        margin: 0 auto;
        width: fit-content;
        height: fit-content;
        top: 2svh;
        left: 50%;
        transform: translateX(-50%);
    }

    .group-swiper-detail {
        background: var(--neutral-100);
    }

    .gallery__thumbs-slider .gallery__wrapper {
        display: none;
    }

    .detail-campain__left .swiper-button-next,
    .swiper-rtl .swiper-button-prev {
        right: var(--swiper-navigation-sides-offset, 3%);
        left: auto;
        width: 5vw;
        top: -150%;
    }

    .detail-campain__left .swiper-button-prev,
    .swiper-rtl .swiper-button-next {
        left: var(--swiper-navigation-sides-offset, 3%);
        right: auto;
        width: 5vw;
        top: -150%;
    }

    .gallery__slide--video {
        padding-top: 0;
    }

    .gallery__slide--video iframe {
        margin-top: 0;
    }

    .gallery__slide--video iframe {
        max-height: 100%;
    }

    .container-section-3 {
        padding-top: 6svh;
    }

    .youtube-thumb__play-btn {
        width: 14vw;
        height: 14vw;
    }
}

/* ============ SECTION 6 DOUBLE SWIPER ==============
=====================================================*/

/* Tab styles */
.tabs {
    display: flex;
    justify-content: center;
    margin-top: 5em;
    gap: 1em;
}

.tab {
    padding: 1em 2em;
    cursor: pointer;
    text-transform: uppercase;
    font-weight: bold;
    border-radius: 2em;
    color: var(--neutral-100);
    background-color: var(--neutral-700);
    align-items: center;
}

.tab p {
    font-size: 1.4em;
    line-height: 1.2;
    margin: 0;
    font-weight: 600;
}

.tab:hover {
    background-color: #f0f0f0;
}

.tab.active {
    background-color: rgba(0, 152, 133, 1);
    color: var(--neutral-900);
}

/* Custom Navigation */
.control-navigate-pagination {
    display: flex;
    position: relative;
    justify-content: center;
    width: fit-content;
    margin: auto;
    gap: 1em;
    margin-top: 1.5em;
    align-items: center;
}

.control-navigate-pagination .swiper-pagination {
    bottom: -0.8%;
}

.control-navigate-pagination .swiper-pagination.bg-transparent {
    /* background-color: rgba(128, 128, 128, 0.3); */
    border-radius: 2em;
    width: fit-content;
    padding: 1em;
    bottom: -4em !important;
}

.control-navigate-pagination .swiper-pagination-bullet {
    width: 1.5em;
    height: 1.5em;
    background-color: #aaa;
    opacity: 1;
}

.control-navigate-pagination .swiper-pagination-bullet-active {
    width: 4em;
    /* transition: width .5s; */
    border-radius: 2em;
    background: #05A8AE;
    border: 1px solid transparent;
}

.control-navigate-pagination .swiper-pagination,
.control-navigate-pagination .swiper-button-prev,
.control-navigate-pagination .swiper-button-next {
    position: static;
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-navigate-pagination .swiper-button-prev,
.control-navigate-pagination .swiper-button-next {
    width: 3.5em;
    height: 3.5em;
    margin: 0;
    background: rgba(217, 217, 217, 0.43);
    padding: 1em !important;
    line-height: 2em;
    border-radius: 50%;
    color: #05A8AE;
    flex: none;
}

.control-navigate-pagination .swiper-button-prev::after,
.control-navigate-pagination .swiper-button-next::after {
    font-size: 1.5em !important;
    -webkit-text-stroke-width: 0.1em;
}

.control-navigate-pagination .swiper-button-prev:hover,
.control-navigate-pagination .swiper-button-next:hover {
    background-color: #05A8AE;
    color: white;
}

.control-navigate-pagination .btn-play,
.control-navigate-pagination .btn-pause {
    height: 2.5em;
    width: 2.5em;
    display: none;
    justify-content: center;
    background: rgba(217, 217, 217, 0.43);
    border-radius: 2em;
    align-items: center;
    font-size: 1.4em;
    color: #05A8AE;
}

.control-navigate-pagination .btn-play.active,
.control-navigate-pagination .btn-pause.active {
    display: flex;
}

.control-navigate-pagination .btn-play svg {
    margin-right: -0.2em;
}

.swiper-wrapper {
    height: 100%;
}

.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right {
    background-image: none;
}

/* Swiper styles */
section.result {
    font-size: 1rem;
    min-height: 10em;
}

section.result .slide-result {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 4em;
    margin-top: 1em;
}

section.result .card {
    display: flex;
    height: 60%;
    max-width: 100%;
    border-radius: 1.5em;
    /* overflow: hidden; */
    cursor: pointer;
    position: relative;
    color: black;
    transition: 0.3s ease-out;
    background-color: hsl(0, 0%, 100%);
    background: linear-gradient(206.68deg, #FFFBD4 0%, #FFF187 100%);
    background-size: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    border: 1px solid var(--neutral-900);
    flex-direction: column;
    justify-content: space-between;
    aspect-ratio: 16 / 9;
    transform: scale(0.8) !important;
}

section.result .card::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 1.5em;
    background: linear-gradient(180deg, rgba(107, 217, 229, 0.56) 0%, rgba(254, 224, 0, 0.2) 36.5%, rgba(70, 193, 178, 0) 100%);
    -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
    z-index: -1;
}

section.result .card.swiper-slide-active.card::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 1em;
    left: 1em;
    right: 0;
    bottom: 0;
    border-radius: 1.5em;
    background: linear-gradient(180deg, rgba(107, 217, 229, 0.56) 0%, rgba(254, 224, 0, 0.2) 36.5%, rgba(70, 193, 178, 0) 100%);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
    z-index: -1;
    background: var(--primary-500);
    padding: 5em;
}

section.result .card img {
    position: absolute;
    -o-object-fit: contain;
    object-fit: contain;
    bottom: 0;
    right: 0;
    opacity: 1;
    border-radius: 1.5em;
    transition: opacity 0.2s ease-out;
}

section.result .card .img-bg-card1,
section.result .card .img-bg-card1 {
    z-index: -1;
}

section.result .card .img-bg-card1 {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

section.result .card .img-bg-card2 {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

section.result .card .card-content {
    position: relative;
    z-index: 10;
    background-color: rgb(110 110 110 / 76%);
    width: -moz-fit-content;
    max-width: 100%;
    padding: 2em;
    border-radius: 0 0 1.5em 1.5em;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
}

section.result .card .card-content .card-text {
    transition: opacity 0.3s ease-out;
    font-size: 1.6em;
    text-align: justify;
    line-height: 1.25;
    color: var(--neutral-900);
}

section.result .card .card-content .card-text small {
    font-size: 0.7em;
    line-height: 1;
}

section.result .card.swiper-slide-active {
    width: 100%;
    max-height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transform: scale(1) !important;
}

section.result .card.swiper-slide-active h2 {
    width: 90%;
    border-end-end-radius: 0rem;
    border-bottom-left-radius: 0rem;
    text-transform: none;
}

section.result .card.swiper-slide-active .img-bg-card2 {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

section.result .card.swiper-slide-active .img-bg-card1 {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

section.result .card.swiper-slide-active .card-text {
    opacity: 1;
    transition: opacity 0.5s 0.1s ease-in;
    display: block;
}

section.result .card.active img {
    transition: opacity 0.3s ease-in;
    opacity: 1;
}

section.result .card-2 {
    background: linear-gradient(318.84deg, #D4F2B0 -18.61%, #F4FBB6 64.31%);
}

section.result .card-3 {
    background: linear-gradient(33.28deg, #BCEBB7 -7.49%, #F4F8C8 76.72%);
}

section.result .card-4 {
    background: linear-gradient(206.72deg, #FCF398 0%, #FAF7D2 100%);
}

section.result .swiper-3d .swiper-wrapper {
    height: 50em;
    align-items: center;
}

section.result .swiper {
    padding: 0 2.5em;
}

@media (max-width: 1599px) {
    .slide-result {
        font-size: 0.9rem;
    }

    section.result .card .img-bg-card1 {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

@media (max-width: 1399px) {
    .slide-result {
        font-size: 0.8rem;
    }

    section.result .card .img-bg-card1 {
        height: 100%;
        width: 100%;
        object-fit: cover;
    }
}

@media (max-width: 1200px) {
    .slide-result {
        font-size: 0.75rem;
    }
}

@media (max-width: 991px) {
    .slide-result {
        font-size: 0.9rem;
    }
}

@media (max-width: 767px) {
    section.result {
        font-size: 0.6rem;
    }

    section.result .card.swiper-slide-active {
        height: 45em;
    }

    section.result .card.swiper-slide-active .card-text {
        font-size: 1.4em;
    }
}

@media (max-width: 575px) {
    .tabs {
        font-size: 2vw;
    }
}

/* == SECTION 7 BANNER CTA & LOGO & POLICY =======
=================================================*/
/* logo byt vtv*/
.companion {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 4em;
    margin: 5em auto;
    font-size: 0.4vw;
}

.companion__title {
    font-weight: 700;
    font-size: 3em;
    color: var(--neutral-900);
    background: var(--primary-400);
    padding: 0.5em 2em;
    text-align: center;
    border-radius: 2em;
}

.companion__logo {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2em;
}

.byt-logo {
    width: 16em;
    aspect-ratio: 1;
}

.vtv-logo {
    width: 33em;
}

.byt-logo img,
.vtv-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* banner CTA */
.group-question {
    position: relative;
    width: 100%;
    height: 25em;
    border-radius: 20px;
    overflow: hidden;
}

.group-question__left {
    width: 56%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding: 2em 15em 2em 2em;
    background: linear-gradient(90deg, #029986 0%, #3ABAA8 72.16%, rgba(74, 194, 179, 0) 100%);
    z-index: 1;
    position: relative;
}

.group-question__btn {
    display: block;
    width: fit-content;
    transition: all 250ms ease-in-out;
    text-transform: uppercase;
    background-color: var(--secondary-500);
    color: var(--neutral-100);
    margin-top: 1em;
    padding: 1em 2em;
    border-radius: 3.2em;
    font-weight: 500;
    line-height: 1.1;
    text-align: center;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}

.group-question__fact {
    width: fit-content;
    height: fit-content;
    font-weight: 500;
    font-size: 1.5em;
    line-height: 1.2;
    text-align: left;
    color: var(--neutral-100);
    display: block;
    line-height: 1.5;
}

.group-question__btn span {
    font-weight: 700;
    font-size: 1.6em;
    line-height: 1.4;
}

.group-question__btn img {
    width: 2.4em;
    margin-left: 10px;
    transform: translateX(-5px);
    filter: brightness(0) saturate(100%) invert(0%) sepia(87%) saturate(7449%) hue-rotate(242deg) brightness(111%) contrast(90%);
}

.hpv-data {
    display: flex;
    gap: 1em;
}

.hpv-data__female,
.hpv-data__male {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2em;
}

.hpv-data__male {
    padding-left: 2em;
}

.hpv-data__male::before {
    display: none;
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 90%;
    aspect-ratio: 1;
    background: var(--neutral-500);
    border-radius: 50%;
    z-index: 0;
}

.hpv-data__percent {
    position: relative;
    width: 12.7em;
    aspect-ratio: 1;
    background: linear-gradient(223.3deg, #FFDE00 13.84%, #84F38D 50.05%, #0DFBF0 86.25%);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
}

.hpv-data__percent::before {
    content: "";
    position: absolute;
    width: 90%;
    height: 90%;
    aspect-ratio: 1;
    background: var(--neutral-900);
    border-radius: 50%;
    z-index: 0;
}

.hpv-data__percent span {
    position: relative;
    font-weight: 900;
    font-size: 4.6em;
    line-height: 1.2;
    color: var(--neutral-100);
    z-index: 1;
}

.hpv-data__percent::after {
    content: "%";
    position: absolute;
    aspect-ratio: 1;
    font-size: 2em;
    font-weight: 700;
    color: var(--neutral-100);
    background: var(--primary-200);
    border-radius: 50%;
    padding: 0.2em;
    line-height: 1.3;
    right: -30%;
    top: 30%;
    transform: translate(-50%, -50%);
}

.group-question__right {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: fit-content;
}

.group-question__content {
    display: flex;
    flex-direction: column;
    gap: 2em;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    position: absolute;
    width: 100%;
}

.group-question-bg {
    height: inherit;
    width: 100%;
}

.group-question-bg img {
    height: 100%;
    object-fit: cover;
}

.group-question-bg picture {
    height: 100%;
    object-fit: cover;
}

.group-tick {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1em;
    cursor: pointer;
}

.btn-tick-prevent-cta {
    position: relative;
    display: block;
    width: fit-content;
    transition: all 250ms ease-in-out;
    text-transform: uppercase;
    background-color: #ffffffb5;
    color: var(--primary-500);
    padding: 1em 2em;
    border: 2px solid var(--primary-500);
    border-radius: 2em;
    font-weight: 500;
    line-height: 1.1;
    text-align: center;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
    overflow: hidden;
    animation: scaleAnimation 1.5s infinite ease-in-out;
    animation-delay: 1s;
}

.btn-tick-prevent-cta::before {
    content: "";
    position: absolute;
    width: 100px;
    height: 100%;
    background-image: linear-gradient(120deg, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0) 70%);
    top: 0;
    left: -100px;
    opacity: 0.6;
    animation: shine 2s ease-out infinite;
}

@keyframes shine {
    0% {
        left: -100px;
    }

    60% {
        left: 100%;
    }

    to {
        left: 100%;
    }
}

#section-5 .btn-tick-prevent-cta:hover {
    background-color: var(--primary-500);
    color: var(--neutral-900);
}

.btn-tick-prevent-cta.active {
    background-color: var(--primary-500);
    color: var(--neutral-900);
    border: 2px solid var(--primary-500);
    animation: none;
    transform: scale(1.03);
}

.btn-tick-prevent-cta span {
    font-weight: 700;
    font-size: 1.6em;
    line-height: 1.2;
}

.title-moh-banner {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: var(--neutral-100);
    gap: 0.8em;
}

.title-moh-banner__sub {
    font-weight: 700;
    font-size: 1.6em;
    line-height: 1.3;
    margin-top: 0.3em;
    text-transform: uppercase;
    font-family: Unbounded;
}

.tagline-moh-banner {
    width: 45.1em;
    aspect-ratio: 451 / 91;
}

.tagline-moh-banner img {
    width: 100%;
    object-fit: cover;
}

.title-moh-banner__main {
    font-weight: 700;
    font-size: 2.8em;
    line-height: 1.1;
    padding: 0.3em 0.8em;
    background-color: var(--secondary-500);
    border-radius: 2em;
    text-transform: uppercase;
}

.line-tick {
    position: absolute;
    z-index: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: none;
}

.line-tick .line-moh {
    width: 114em;
    /* aspect-ratio: 1676 / 438; */
    aspect-ratio: 1986 / 464;
    left: 1em;
    position: relative;
    top: -10em;
    clip-path: inset(0 100% 0 0);
    transition: clip-path 1s ease-in-out;
}

.line-tick .hand-img {
    position: relative;
    top: -4.9em;
    left: -8em;
    width: 24.9em;
    aspect-ratio: 290 / 188;
    opacity: 0;
    transition: opacity 0.5s ease-in-out 0.7s;
}

.section-5 .group-question__left {
    padding: 5em 15em 5em 2em;
}

.section-question-moh.section-5 {
    margin: 0 auto;
    padding-top: 10svh;
    padding-bottom: 2svh;
    padding-left: 2svh;
    padding-right: 2svh;
    background: linear-gradient(180deg, #FFFFFF 0%, #EBFFED 80%);
}

.section-question-moh .tick-border {
    width: 7em;
    aspect-ratio: 181 / 179;
}



.group-question {
    position: relative;
    width: 100%;
    height: 25em;
    border-radius: 20px;
    overflow: hidden;
}

.group-question__left {
    width: 56%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding: 2em 15em 2em 2em;
    background: linear-gradient(90deg, #029986 0%, #3ABAA8 72.16%, rgba(74, 194, 179, 0) 100%);
    z-index: 1;
    position: relative;
}

.group-question__btn {
    display: block;
    width: fit-content;
    transition: all 250ms ease-in-out;
    text-transform: uppercase;
    background-color: var(--secondary-500);
    color: var(--neutral-100);
    margin-top: 1em;
    padding: 1em 2em;
    border-radius: 3.2em;
    font-weight: 500;
    line-height: 1.1;
    text-align: center;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}

.group-question__btn:hover {
    background-color: var(--secondary-700);
    transform: translateY(-2px);
}

.group-question__disc {
    font-weight: 700;
    font-size: 3em;
    line-height: 1.1;
    color: var(--neutral-900);
    text-align: center;
}

.group-question__fact {
    width: 30%;
    height: fit-content;
    font-weight: 400;
    font-size: 1.4em;
    line-height: 1.2;
    text-align: left;
    color: var(--neutral-100);
    display: block;
    word-wrap: break-word;
    line-height: 1.5;
    white-space: nowrap;
}

.group-question__btn span {
    font-weight: 700;
    font-size: 1.6em;
    line-height: 1.4;
}

.group-question__btn img {
    width: 2.4em;
    margin-left: 10px;
    transform: translateX(-5px);
    filter: brightness(0) saturate(100%) invert(0%) sepia(87%) saturate(7449%) hue-rotate(242deg) brightness(111%) contrast(90%);
}


@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .section-question-moh {
        font-size: 0.9rem;
    }

    .group-question__left {
        width: 55%;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .section-question-moh {
        font-size: 0.75rem;
    }

    .group-question__left {
        width: 53%;
    }
}

@media only screen and (min-width: 991px) and (max-width: 1199px) {
    .section-question-moh {
        font-size: 0.7rem;
    }

    /* .group-question__left {
        width: 100%;
    } */
}

@media only screen and (min-width: 769px) and (max-width: 992px) {
    .section-question-moh {
        font-size: 1rem;
    }

    .group-question {
        display: flex;
        flex-direction: column-reverse;
        height: auto;
        aspect-ratio: 416 / 442;
        width: 80%;
        margin: 0 auto;
    }

    .group-question__disc {
        font-weight: 500;
        line-height: 1.1;
        color: var(--neutral-900);
        text-align: center;
    }

    .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        padding-top: 10svh;
    }

    .section-5 .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        padding-top: 10svh;
    }

    .group-question__right {
        position: relative;
        margin-bottom: -14%;
    }

    .group-tick {
        margin-top: 3em;
        font-size: 0.9em;
    }

    .line-tick .line-moh {
        width: 114em;
        aspect-ratio: 1986 / 464;
        left: -1em;
        position: relative;
        top: -11em;
        clip-path: inset(0 100% 0 0);
    }

    .line-tick .hand-img {
        position: relative;
        top: -4.5em;
        width: 29em;
        left: -16em;
        aspect-ratio: 290 / 188;
        opacity: 0;
        transition: opacity 0.5s ease -in-out 0.8s;
    }

    .line-tick {
        width: max-content;
        font-size: 0.8em;
    }

    .group-question__content {
        gap: 5svh;
    }

    .companion {
        font-size: 0.6vw;
    }
}

@media (min-width: 575px) and (max-width: 769px) {
    .content-average {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        gap: 2em
    }

    .group-question__fact {
        width: 54%;
        font-size: 2.3em;
        text-align: center;
    }

    .group-question__fact br {
        display: none;
    }

    .group-question__content {
        gap: 5svh;
        font-size: 1.2em;
    }

    .hpv-data__male::before {
        display: block;
    }

    .hpv-data__male {
        padding-left: 5em;
    }

    .hpv-data {
        gap: 5em;
    }

    .bottom-kv-moh {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        display: none;
    }

    .tagline-moh {
        width: 83.2em;
        margin-top: -0;
        aspect-ratio: 2144 / 868;
    }

    .section-question-moh {
        font-size: 0.9rem;
    }

    .group-question {
        display: flex;
        flex-direction: column-reverse;
        height: auto;
        aspect-ratio: 416 / 442;
        width: 90%;
        margin: 0 auto;
    }

    .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        font-size: 1em;
        padding-top: 10svh;
    }

    .section-5 .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        padding-top: 10svh;
    }

    .group-question__right {
        position: relative;
        margin-bottom: -14%;
    }

    .group-tick {
        font-size: 1em;
    }

    .line-tick .line-moh {
        width: 114em;
        aspect-ratio: 1986 / 464;
        left: 3em;
        position: relative;
        top: -11em;
        clip-path: inset(0 100% 0 0);
    }

    .line-tick .hand-img {
        position: relative;
        top: -3.5em;
        width: 26em;
        left: -24em;
        aspect-ratio: 290 / 188;
        opacity: 0;
        transition: opacity 0.5s ease -in-out 0.8s;
    }

    .line-tick {
        width: max-content;
    }

    .group-question__content {
        gap: 5svh;
    }

    .companion {
        font-size: 0.8vw;
    }
}

@media (max-width: 575px) {
    .content-average {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        gap: 2em
    }

    .group-question__fact {
        width: 54%;
        font-size: 2.3em;
        text-align: center;
    }

    .group-question__fact br {
        display: none;
    }

    .group-question__content {
        gap: 5svh;
        font-size: 1.2em;
    }

    .hpv-data__male::before {
        display: block;
    }

    .hpv-data__male {
        padding-left: 5em;
    }

    .hpv-data {
        gap: 5em;
    }

    .companion {
        font-size: 0.8vw;
    }

    .section-question-moh {
        font-size: 0.7rem;
        margin: 0 auto 2svh;
    }

    .section-question-moh.section-2 {
        padding: 0;
    }

    .group-question {
        display: flex;
        flex-direction: column-reverse;
        height: auto;
        width: 100%;
        margin: 0 auto;
        border-radius: 20px;
    }

    .section-5 .group-question__left {
        font-size: 0.85em;
        width: 100%;
        height: 22svh;
        padding: 1em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        gap: 2svh;
        padding-top: 5svh;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: -9%;
    }

    #section-5 .group-question__right {
        position: relative;
        width: 100%;
        height: 40svh;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .group-question-bg picture {
        height: inherit;
        object-fit: cover;
        width: 100%;
    }

    #section-5 .group-tick {
        font-size: 0.7rem;
    }

    .line-tick .line-moh {
        width: 80em;
        aspect-ratio: 1676 / 438;
        left: -5em;
        top: -7em;
    }

    .line-tick .hand-img {
        top: -4em;
        left: -7em;
        width: 21em;
        aspect-ratio: 290 / 188;
    }

    .group-question__content {
        gap: 5svh;
        top: 45%;
    }

    .group-question__btn {
        margin-top: 0;
        font-size: 1.2em;
    }

    #section-5 .group-question-bg {
        height: auto;
        width: 100%;
        object-fit: cover;
    }
}

@media (max-width: 374px) {
    .content-average {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        gap: 2em
    }

    .group-question__fact {
        width: 54%;
        font-size: 2.3em;
        text-align: center;
    }

    .group-question__fact br {
        display: none;
    }

    .group-question__content {
        gap: 5svh;
        font-size: 1.2em;
    }

    .hpv-data__male::before {
        display: block;
    }

    .hpv-data__male {
        padding-left: 5em;
    }

    .hpv-data {
        gap: 5em;
    }

    .section-question-moh {
        font-size: 0.6rem !important;
    }

    .line-tick .hand-img {
        position: relative;
        top: -2em;
        left: -17em;
        width: 22em;
        aspect-ratio: 290 / 188;
    }

    .group-tick {
        font-size: 0.9em;
    }
}

/* ============ FOOTER ==============
=====================================*/

.footer-moh {
    font-size: 1rem;
    color: #373737;
}

.footer-moh__content {
    padding: 5em 0 5em;
    background-color: var(--primary-500);
}

.footer-moh__content .footer-moh__line-2 {
    display: block;
    font-weight: 300;
    font-size: 1.2em;
    line-height: 1.1;
    text-align: center;
}

.footer-moh__content .footer-moh__line-1 {
    display: block;
    font-weight: 700;
    font-size: 1.4em;
    line-height: 2;
    text-align: center;
}

.footer-moh__content .direction {
    margin-top: 1em;
    font-weight: 600;
    font-size: 1em;
    line-height: 1.1;
    text-align: center;
}

.nowrap {
    white-space: nowrap;
}

@media (max-width: 575px) {
    .footer-moh {
        font-size: 0.7rem;
        padding-bottom: 60px;
    }

    .footer-moh__content .footer-moh__line-1 {
        font-size: 2.4em;
    }

    .footer-moh__content {
        padding: 3em 5em 5em;
    }

    .footer-moh__content .direction {
        font-size: 1.5em;
    }

    .footer-moh__content .footer-moh__line-2 br {
        display: none;
    }
}

@media (min-width: 576px) and (max-width: 768px) {
    .footer-moh {
        font-size: 0.8rem;
        padding-bottom: 60px;
    }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
    .footer-moh {
        font-size: 0.85rem;
        padding-bottom: 60px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .footer-moh {
        font-size: 0.8rem;
        padding-bottom: 60px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .footer-moh {
        font-size: 0.85rem;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .footer-moh {
        font-size: 0.9rem;
    }
}

@media (min-width: 1100px) {
    .root-container {
        gap: 0;
    }
}