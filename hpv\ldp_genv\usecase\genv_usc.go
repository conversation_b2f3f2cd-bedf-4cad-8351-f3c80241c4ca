package usecase

import (
	"context"
	"webgo/hpv/common/enums"
	"webgo/hpv/entity"
	"webgo/hpv/ldp_genv/transport/responses"
	"webgo/pkg/gos/utils"
)

type GenVCategoryRepo interface {
	FirstCategoryRepo(ctx context.Context, filter *utils.Filters) (*entity.CategoryEntity, error)
}

type genvUsc struct {
	categoryRepo GenVCategoryRepo
}

func NewGenvUsc(categoryRepo GenVCategoryRepo) *genvUsc {
	return &genvUsc{
		categoryRepo: categoryRepo,
	}
}

/**
 * usc for hdl genv
 */
func (usc *genvUsc) GetGenvUsc(ctx context.Context) (*responses.GenvResp, error) {
	filter := &utils.Filters{
		Conds: &map[string]any{
			"slug":   enums.CATEGORY_WEAREGENV,
			"status": enums.STATUS_ACTIVE,
		},
	}

	category, err := usc.categoryRepo.FirstCategoryRepo(ctx, filter)
	if err != nil {
		return nil, err
	}

	return &responses.GenvResp{
		Views: utils.FormatNumberWithDot(category.Views),
		Seo:   category.Seo,
	}, nil
}
