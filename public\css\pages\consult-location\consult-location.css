.banner-details {
  flex: 1;
  flex-grow: 1.6;
  display: none;
}
.banner-details .swiper-banner-details .banner-item {
  border-radius: var(--radius-sm);
  overflow: hidden;
}
.banner-details .swiper-banner-details .banner-item__mobile {
  display: block;
  width: 100%;
  max-height: 25svh;
}
.banner-details .swiper-banner-details .banner-item__desktop {
  display: none;
}

@media (min-height: 1530px) {
  .banner-details {
    display: block;
  }
}

@media (min-width: 1100px) {
  .root-container {
      gap: 0.7vw;
      overflow-y: hidden;
      max-height: 100svh;
  }
}
@media (min-width: 976px) {
  .banner-details {
    height: 100%;
  }
  .banner-details .swiper-banner-details {
    height: 100%;
    border-radius: var(--radius-sm);
    overflow: hidden;
  }
  .banner-details .swiper-banner-details .banner-item__mobile {
    display: none;
  }
  .banner-details .swiper-banner-details .banner-item__desktop {
    display: block;
  }
  .banner-details .swiper-banner-details .banner-item img {
    height: 100%;
    -o-object-fit: cover;
       object-fit: cover;
  }
}
.dropdown {
  position: relative;
  display: inline-block;
}
.dropdown .dropdown-toggle {
  font-size: 1.35rem;
  padding: 1em;
  background-color: white;
  color: rgb(30, 30, 30);
  border: none;
  cursor: pointer;
  font-weight: 500;
  padding-right: 3em;
  border-radius: 5em;
}
.dropdown .dropdown-toggle .icon {
  position: absolute;
  right: 0.3em;
  font-size: 2em;
  pointer-events: none;
  top: 20%;
  color: var(--neutral-500);
}
.dropdown .dropdown-toggle .select-value {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.dropdown .dropdown-menu {
  display: none;
  position: absolute;
  background-color: white;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  min-width: -moz-fit-content;
  min-width: fit-content;
  z-index: 1;
  border-radius: 5px;
  max-height: 450px;
  overflow-y: auto;
  padding: 0;
  flex-direction: column;
}
.dropdown .dropdown-item {
  font-size: 1.5em;
  padding: 0.4em 1em;
  display: block;
  cursor: pointer;
  white-space: nowrap;
}
.dropdown .dropdown-item:last-child {
  border-bottom: none;
}
.dropdown .dropdown-item:hover {
  background-color: var(--neutral-800);
}

.form-consultant {
  min-height: -moz-fit-content;
  min-height: fit-content;
  gap: 0;
  background-image: linear-gradient(125deg, var(--primary-300) 20%, var(--primary-500) 80%);
  border-radius: var(--radius-sm);
  display: flex;
  flex-direction: column;
}
.form-consultant .header-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  background-color: transparent;
  color: white;
}
.form-consultant .header-label__subtitle {
  font-size: 1.5em;
  font-weight: 400;
  padding: 0.8em;
  display: none;
}
.form-consultant .header-label__title {
  display: flex;
  text-transform: uppercase;
  padding: 0.8em;
  padding-bottom: 0;
  font-size: 2em;
  font-weight: 700;
  text-align: center;
}
.form-consultant .form-input {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 1.5em;
  flex-grow: 1;
  width: 100%;
  padding: 2em;
  border-radius: var(--radius-sm);
}
.form-consultant .form-input .input-control {
  flex: auto;
  width: 100%;
}
.form-consultant .form-input .input-control#address {
  width: 100%;
  flex: auto;
}
.form-consultant .form-input .dropdown {
  width: 100%;
}
.form-consultant .form-input .dropdown button {
  text-align: left;
}
.form-consultant .form-input .inline-control {
  display: flex;
  gap: 1.5em;
  flex-direction: row;
}
.form-consultant .form-input .inline-control .input-control {
  flex: 1;
  width: 50%;
}
.form-consultant .form-input .inline-control .input-control .dropdown,
.form-consultant .form-input .inline-control .input-control .dropdown-toggle,
.form-consultant .form-input .inline-control .input-control .dropdown-menu {
  width: 100%;
  text-align: left;
}
.form-consultant .form-input button,
.form-consultant .form-input input {
  font-size: 1.6em;
  background-color: white;
  color: rgb(30, 30, 30);
  border: none;
  cursor: pointer;
  font-weight: 500;
  border-radius: 5em;
  width: 100%;
  padding: 1em 1.8em;
}
.form-consultant .form-input button:focus-visible,
.form-consultant .form-input input:focus-visible {
  outline: none;
}
.form-consultant #action-submit {
  text-align: center;
}
.form-consultant #action-submit #btn-find-now {
  margin: auto;
  transition: all 250ms ease-in-out;
  text-transform: uppercase;
  background-color: var(--secondary-500);
  color: var(--primary-700);
  font-size: 1.6em;
  font-weight: 600;
}
.form-consultant #action-submit #btn-find-now:hover {
  background-color: var(--secondary-600);
}

@media (max-width: 1099px) {
  .form-consultant {
    background-color: transparent;
    padding: 0;
    gap: 1em;
  }
  .form-consultant .header-label__subtitle {
    display: block;
  }
  .form-consultant .form-input {
    gap: 0.8em;
    padding: 1em;
    background: none;
  }
  .form-consultant .form-input .input-control {
    flex: 1;
  }
  .form-consultant .form-input button,
  .form-consultant .form-input input {
    border-radius: var(--radius-sm);
    font-size: 1.35em;
  }
  .form-consultant #action-submit #btn-find-now {
    font-size: 1.35em;
  }
}
@media (max-height: 960px) and (min-width: 1100px) {
  .form-consultant {
    font-size: 0.85rem;
  }
  .form-consultant .form-input .input-control {
    flex: 1;
    width: auto;
  }
}
.consultant-locations-list {
  overflow-y: auto;
  overflow-x: visible;
  position: relative;
  flex: 1;
}
.consultant-locations-list .consultant-locations {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1em;
}
.consultant-locations-list .consultant-locations .location {
  display: flex;
  flex-direction: row;
  border-radius: var(--radius-sm);
  background-color: white;
  cursor: pointer;
  transition: all 200ms ease-in;
  flex: auto;
  background-color: #ebfffd;
  max-height: 22em;
}
.consultant-locations-list .consultant-locations .location:hover {
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  background-color: var(--forth-500);
}
.consultant-locations-list .consultant-locations .location:hover * {
  color: white !important;
}
.consultant-locations-list .consultant-locations .location:hover .location__thumb img {
  transform: scale(1.05);
}
.consultant-locations-list .consultant-locations .location .location__thumb {
  position: relative;
  transform: scale(1);
  overflow: hidden;
  width: 38%;
  border-top-left-radius: var(--radius-sm);
  border-bottom-left-radius: var(--radius-sm);
}
.consultant-locations-list .consultant-locations .location .location__thumb img {
  transition: transform 200ms ease-in;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.consultant-locations-list .consultant-locations .location .location__info {
  padding: 1.6em;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  flex: 1;
}
.consultant-locations-list .consultant-locations .location .location__info__name {
  font-weight: 600;
  font-size: 1.6em;
  text-transform: uppercase;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5em;
}
.consultant-locations-list .consultant-locations .location .location__info__address, .consultant-locations-list .consultant-locations .location .location__info__phone {
  display: flex;
  gap: 1em;
  align-items: center;
  margin-top: 0.5em;
  width: 100%;
}
.consultant-locations-list .consultant-locations .location .location__info__address .address__icon,
.consultant-locations-list .consultant-locations .location .location__info__address .phone__icon, .consultant-locations-list .consultant-locations .location .location__info__phone .address__icon,
.consultant-locations-list .consultant-locations .location .location__info__phone .phone__icon {
  font-size: 1.8em;
  color: var(--neutral-400);
}
.consultant-locations-list .consultant-locations .location .location__info__address .address__text,
.consultant-locations-list .consultant-locations .location .location__info__address .phone-number__text, .consultant-locations-list .consultant-locations .location .location__info__phone .address__text,
.consultant-locations-list .consultant-locations .location .location__info__phone .phone-number__text {
  color: var(--neutral-400);
  font-weight: 300;
  font-size: 1.12em;
  font-style: normal;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}
.consultant-locations-list .consultant-locations .location .location__info__address .phone-number__text, .consultant-locations-list .consultant-locations .location .location__info__phone .phone-number__text {
  font-weight: 600;
}
.consultant-locations-list .loading {
  position: sticky;
  top: 50%;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  height: -moz-fit-content;
  height: fit-content;
  transform: translateY(-50%);
  z-index: 99;
}
.consultant-locations-list .empty-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  height: -moz-fit-content;
  height: fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

@media (max-width: 1099px) {
  .consultant-locations-list {
    max-height: calc(100svh - 395px);
    min-height: 260px;
    overflow-y: auto;
    height: 100%;
  }
  .consultant-locations-list .consultant-locations {
    flex-direction: row;
    flex-wrap: wrap;
  }
  .consultant-locations-list .consultant-locations .location {
    flex-direction: row;
    min-height: 120px !important;
    background-color: var(--primary-300);
    min-width: 420px;
    flex: 1;
  }
  .consultant-locations-list .consultant-locations .location:nth-child(2n) {
    background-color: var(--primary-800);
  }
  .consultant-locations-list .consultant-locations .location .location__info {
    padding: 1.2em;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
  }
  .consultant-locations-list .consultant-locations .location .location__thumb {
    max-width: 35%;
    max-height: 100% !important;
    width: 35%;
    display: block;
    border-radius: var(--radius-sm);
  }
  .consultant-locations-list .consultant-locations .location .location__info__name {
    font-size: 1.5em;
    color: white;
    font-weight: 500;
  }
  .consultant-locations-list .consultant-locations .location .location__info__address .address__text,
  .consultant-locations-list .consultant-locations .location .location__info__address .phone-number__text,
  .consultant-locations-list .consultant-locations .location .location__info__phone .address__text,
  .consultant-locations-list .consultant-locations .location .location__info__phone .phone-number__text {
    -webkit-line-clamp: 3;
    color: white;
    letter-spacing: 0.02em;
    line-height: 1.5;
  }
  .consultant-locations-list .consultant-locations .location .location__info__address .address__icon,
  .consultant-locations-list .consultant-locations .location .location__info__address .phone__icon,
  .consultant-locations-list .consultant-locations .location .location__info__phone .address__icon,
  .consultant-locations-list .consultant-locations .location .location__info__phone .phone__icon {
    display: none;
  }
}
@media (max-width: 768px) {
  .consultant-locations-list .consultant-locations .location {
    min-width: 320px;
  }
}
@media (max-width: 628px) {
  .consultant-locations-list {
    max-height: calc(100svh - 425px);
  }
}
.locations-details {
  display: flex;
  flex-direction: column;
  border-radius: var(--radius-sm);
  overflow: hidden;
  transition: all 200ms ease-in;
  gap: 0.5em;
  background-color: var(--primary-900);
  color: white;
  flex: none;
  width: 100%;
  font-size: 0.525vw;
  height: fit-content;
  min-height: -moz-fit-content;
}
.locations-details.mobile {
  background-color: transparent;
  padding: 0;
}
.locations-details.mobile .thumb {
  display: none;
}
.locations-details.mobile .location_info {
  padding: 0;
  margin-top: 1em;
}
.locations-details .thumb {
  aspect-ratio: 4.5/2;
  max-width: 100%;
  max-height: 100%;
  overflow: hidden;
  border-top-left-radius: 1em;
  border-top-right-radius: 1em;
  min-height: 150px;
}
.locations-details .thumb img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.locations-details .location_info {
  display: flex;
  flex-direction: column;
  padding: 1.5em;
  gap: 1em;
}
.locations-details .location-details__name {
  font-weight: 600;
  font-size: 1.6em;
  text-transform: uppercase;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5em;
}
.locations-details .location-details__info {
  font-style: normal;
  display: flex;
  gap: 1em;
  flex-direction: column;
}
.locations-details .location-details__info .info__address {
  display: flex;
  gap: 1em;
  align-items: center;
  font-weight: 400;
}
.locations-details .location-details__info .info__address .address__icon {
  font-size: 1.8em;
}
.locations-details .location-details__info .info__address .address__text {
  font-size: 1.2em;
}
.locations-details .location-details__info .info__contact {
  display: flex;
  gap: 1em;
  flex-wrap: wrap;
  justify-content: space-between;
  font-weight: 400;
}
.locations-details .location-details__info .info__contact .contact__working-hour,
.locations-details .location-details__info .info__contact .contact__phone-number {
  display: flex;
  gap: 1em;
  align-items: center;
}
.locations-details .location-details__info .info__contact .phone-number__icon,
.locations-details .location-details__info .info__contact .working-hour__icon {
  font-size: 1.8em;
}
.locations-details .location-details__info .info__contact .working-hour__text,
.locations-details .location-details__info .info__contact .phone-number__text {
  font-size: 1.2em;
}
.locations-details .actions {
  display: flex;
  gap: 1em;
  justify-content: center;
}
.locations-details .actions a,
.locations-details .actions button {
  margin: auto;
  transition: all 250ms ease-in-out;
  text-transform: uppercase;
  background-color: var(--forth-500);
  color: white;
  font-size: 1.6em;
  font-weight: 600;
  width: 100%;
  text-align: center;
  border-radius: 5em;
  padding: 0.6em 1em;
  border: none;
  cursor: pointer;
}
.locations-details .actions a:hover,
.locations-details .actions button:hover {
  filter: brightness(1.2);
}
.locations-details .actions a svg,
.locations-details .actions button svg {
  font-size: 1.3em;
}
.locations-details .actions .btn-go-back {
  display: none;
  color: var(--primary-800);
  background-color: white;
}
.locations-details .actions .btn-go-back:hover {
  color: white;
  background-color: var(--primary-400);
}
.locations-details .actions .btn-consult-now {
  display: none;
  color: var(--primary-800);
  background-color: var(--primary-100);
}
.locations-details .actions .btn-consult-now:hover {
  color: white;
  background-color: var(--primary-500);
}

:is(.location-list-details) .locations-details {
  display: flex;
}

@media (max-width: 1099px) {
  :is(.location-list-details) .locations-details {
    display: none;
  }
  :is(.modal-consultant-location-details) .locations-details {
    display: flex;
  }
  :is(.modal-consultant-location-details) .locations-details {
    display: inline-flex;
  }
  .locations-details {
    height: -moz-fit-content;
    height: fit-content;
    font-size: 0.85rem;
    padding: 0;
    width: 100%;
    flex: auto;
  }
  .locations-details .thumb {
    display: none;
  }
  .locations-details .location_info {
    padding: 0;
  }
  .locations-details .location-details__info {
    font-size: 1.3em;
  }
  .locations-details .location-details__name {
    margin-top: 0.5em;
    font-size: 2.5em;
  }
  .locations-details .actions {
    font-size: 1rem;
    flex-wrap: wrap;
    gap: 1em;
  }
  .locations-details .actions .btn-directions {
    width: 100%;
  }
  .locations-details .actions .btn-go-back,
  .locations-details .actions .btn-consult-now {
    flex: 1;
    display: none;
  }
}
@media (max-height: 830px) and (min-width: 1100px) {
  :is(.location-list-details) .locations-details {
    flex-direction: row;
    flex-grow: 0.8;
  }
  .locations-details .location_info {
    justify-content: space-between;
  }
  .locations-details .thumb {
    min-width: 30%;
    flex: auto;
  }
}
@media (max-width: 1099px) {
  .locations-details .actions .btn-consult-now {
    display: block;
  }
  .locations-details .actions .btn-go-back {
    display: block;
  }
}
.google-map {
  border-radius: var(--radius-sm);
  overflow: hidden;
  position: relative;
  /* flex-grow: 1; */
  width: 100%;
  height: 100%;
  flex: auto;
}
.google-map .loading {
  position: sticky;
  top: 50%;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  height: -moz-fit-content;
  height: fit-content;
  transform: translateY(-50%);
  z-index: 99;
}
.google-map iframe {
  width: 100% !important;
  height: 100% !important;
}

@media (max-width: 1099px) {
  .google-map {
    width: 100%;
    height: 50svh;
    max-height: 500px;
    flex: auto;
  }
  .google-map iframe {
    min-width: 400px;
  }
}
@media (max-height: 720px) and (min-width: 1100px) {
  .google-map {
    display: block;
  }
}
.consult-location-partner {
  background-image: linear-gradient(125deg, var(--primary-300) 20%, var(--primary-500) 80%);
  border-radius: var(--radius-sm);
  flex: 1;
  min-height: 10em;
  display: flex;
  flex-direction: column;
}
.consult-location-partner .partner-title {
  display: flex;
  text-transform: uppercase;
  padding: 0.8em;
  padding-bottom: 0;
  font-size: 2em;
  font-weight: 700;
  text-align: center;
  color: white;
  justify-content: center;
}
.consult-location-partner .location-list {
  padding: 2em;
  border-radius: var(--radius-sm);
  background-image: linear-gradient(125deg, var(--primary-300) 20%, var(--primary-500) 80%);
  grid-template-columns: repeat(auto-fill, minmax(40%, 1fr));
  grid-template-rows: minmax(50%, 50%);
  gap: 1em;
  flex-grow: 1;
}
.consult-location-partner .location-list .location-item {
  display: flex;
  align-items: center;
}

/* @media (max-height: 830px) and (min-width: 1100px) {
  .location-list {
    grid-template-columns: repeat(auto-fill, minmax(40%, 1fr));
    grid-template-rows: minmax(50%, 50%);
    gap: 1em;
  }
  .location-list .location-item img {
    width: 15em;
    height: auto;
    margin: auto;
  }
} */
@media (max-height: 750px) and (min-width: 1100px) {
  .form-consultant .form-input {
    padding: 1em;
  }
  .consult-location-partner .location-list {
    grid-template-columns: repeat(auto-fill, minmax(20%, 1fr));
    grid-template-rows: minmax(50%, 100%);
    padding: 1em;
    gap: 1em;
  }
}
@media (max-width: 1099px) {
  :is(.form-and-partner) .consult-location-partner {
    display: none;
  }
}
@media (max-height: 830px) and (min-width: 1100px) {
  .consult-location-partner {
    font-size: 0.8rem;
  }
}
.main-consultant-content {
  display: flex;
  flex-direction: row;
  flex: auto;
  gap: 1.5em;
  max-height: calc(100svh - 196px);
  min-height: 400px;
  overflow-y: auto;
}
.main-consultant-content .col-1\/3 {
  flex: auto;
  width: 33%;
  display: flex;
  flex-direction: column;
  gap: 1em;
}
.main-consultant-content .thumb-consultant-locations-details {
  display: block;
  aspect-ratio: 3/2;
  transform: scale(1);
  overflow: hidden;
}
.main-consultant-content .thumb-consultant-locations-details img {
  transition: transform 200ms ease-in;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.main-consultant-content .modal-consultant-location-details {
  display: flex;
  flex-direction: column;
  gap: 1em;
}
.main-consultant-content .modal-consultant-location-details .overlay {
  display: none;
}
.main-consultant-content .form-and-partner {
  display: flex;
  gap: 1.5em;
  flex-direction: column;
  justify-content: space-between;
  flex-grow: 1;
  height: 100%;
}
.main-consultant-content .modal-consultant-location-details_content {
  position: relative;
  height: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  border-radius: 1.5em;
  gap: 1.5em;
}
.main-consultant-content .modal-consultant-location-details_content .btn-close-modal {
  width: 1.2em;
  height: 1.2em;
  border-radius: 50%;
  text-align: center;
  line-height: 1em;
  font-weight: 400;
  cursor: pointer;
  font-size: 2em;
  color: var(--primary-500);
  transition: all 250ms ease-in-out;
  border: 2px solid;
  z-index: 1;
  background-color: white;
  box-shadow: 0 0 8px var(--primary-800);
  display: none;
  position: absolute;
  top: 0.2em;
  right: 0.2em;
}

@media (max-width: 1099px) {
  .main-consultant-content {
    flex-direction: column;
    gap: 1.5em;
    max-height: calc(100svh - 170px);
    min-height: auto;
  }
  .main-consultant-content .col-1\/3 {
    width: 100%;
    flex: none;
  }
  .main-consultant-content .modal-consultant-location-details {
    display: none;
    align-items: center;
    justify-content: center;
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 99;
    transition: all 250ms ease-in-out;
    font-size: 1.2rem;
  }
  .main-consultant-content .modal-consultant-location-details .overlay {
    display: none;
    position: absolute;
    z-index: -1;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(26, 26, 26, 0.64);
    opacity: 0;
    -webkit-backdrop-filter: blur(8px);
            backdrop-filter: blur(8px);
  }
  .main-consultant-content .modal-consultant-location-details .btn-close-modal {
    display: block;
  }
  .main-consultant-content .modal-consultant-location-details .google-map,
  .main-consultant-content .modal-consultant-location-details .locations-details {
    opacity: 0;
    transform-origin: top bottom;
    transition: all 250ms ease-in-out;
    transform: scale(0.3);
  }
  .main-consultant-content .modal-consultant-location-details.active {
    display: flex;
  }
  .main-consultant-content .modal-consultant-location-details.active .overlay {
    opacity: 1;
    display: block;
  }
  .main-consultant-content .modal-consultant-location-details.active .google-map,
  .main-consultant-content .modal-consultant-location-details.active .locations-details {
    transform: scale(1);
    opacity: 1;
  }
  .main-consultant-content .modal-consultant-location-details.active .modal-consultant-location-details_content {
    opacity: 1;
  }
  .main-consultant-content .modal-consultant-location-details .modal-consultant-location-details_content {
    background-color: var(--primary-900);
    opacity: 0;
    height: -moz-fit-content;
    height: fit-content;
    width: 95%;
    gap: 1.3em;
    max-width: 50em;
    padding: 1.5em;
    max-height: 95%;
  }
}
@media (min-height: 1200px) {
  .main-consultant-content {
    max-height: 1000px;
  }
}/*# sourceMappingURL=consult-location.css.map */