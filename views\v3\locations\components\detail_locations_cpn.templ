package components

	
import (
    "webgo/pkg/gos/templates"
    "webgo/hpv/locations/transport/responses"
)
templ DetailLocationsCpn(loc responses.Locations) {

    <div class="col-1/3 modal-consultant-location-details">
        <div class="overlay"></div>
        <div class="modal-consultant-location-details_content">

            <!-- Location Info Details -->
            <div class="locations-details" id="locations-details">

                <div class="thumb">
                
                    <img src={ templates.ImgURL( loc.Img ) } alt={ loc.Name }>
                </div>

                <div class="location_info">
                    <h3 class="location-details__name">
                        { loc.Name }
                    </h3>

                    <address class="location-details__info">
                        <div class="info__address">
                            @iconSvgCop("address-solid-icon", "address__icon")
                            <p class="address__text">{ loc.Address }</p>
                        </div>

                        <div class="info__contact">
                            <div class="contact__working-hour">
                                @iconSvgCop("clock-solid-icon", "working-hour__icon")
                                <p class="working-hour__text">{ loc.WorkTime }</p>
                            </div>

                            <div class="contact__phone-number">
                                @iconSvgCop("phone-solid-icon", "phone-number__icon")
                                <p class="phone-number__text">{ loc.Phone }</p>
                            </div>
                        </div>
                    </address>

                    <div class="actions">
                        if (loc.LinkMap != nil) {
                           <a href={ templates.SafeURL(*loc.LinkMap) } target="_blank" title={ loc.Name } rel="noopener noreferrer" class="btn-directions">
                                Chỉ Đường
                            </a>
                        }
                        else {
                            <a href={ templates.SafeURL("https://maps.google.com/maps?q=" + loc.Lat + "," + loc.Lng) } target="_blank" title={ loc.Name } rel="noopener noreferrer" class="btn-directions">
                                Chỉ Đường
                            </a>
                        }

                        <button class="btn-go-back">Quay lại</button>
                        <button class="btn-consult-now">Tư vấn ngay</button>
                    </div>
                </div>
            </div>

            <!-- Close Modal -->
            <span class="btn-close-modal">×</span>

            <!-- Google Map -->
            <div class="google-map" id="google-map">
                <div class="loading" style="display: none;">
                    <div class="msd-loader-container">
                        <div class="msd-loader"></div>
                    </div>
                </div>

                if (*loc.Map != "") {
                    @templ.Raw( *loc.Map )
                }      
                else {
                    <iframe
                        src="https://maps.google.com/maps?q={{$loc.Lat}},{{$loc.Lng}}&hl=vn&z=17&amp;output=embed"
                        width="100%"
                        height="100%"
                        style="border:0;"
                        allowfullscreen="true"
                        loading="lazy"
                        referrerpolicy="no-referrer-when-downgrade">
                    </iframe>
                }
            </div>
        </div>
    </div>
}


templ iconSvgCop(id string, class string) {
    <svg class={class} xmlns:xlink="http://www.w3.org/1999/xlink">
        <use xlink:href={ "#" + id } />
    </svg>
}