package entity

import (
	"webgo/hpv/common/gocommon"
	"webgo/pkg/sctx/core"
)

type ProviceEntity struct {
	core.SQLModel
	ProvinceId int64  `json:"province_id"`
	CountryId  int64  `json:"country_id"`
	Title      string `json:"title"`
	Code       string `json:"code"`
	Status     int    `json:"status"`
	Ordering   int64  `json:"ordering"`
}

func (ProviceEntity) TableName() string {
	return "hpv.provinces"
}

func (l *ProviceEntity) Mask() {
	l.SQLModel.Mask(gocommon.MASK_TYPE_PROVINCE)
}

func (l *ProviceEntity) MaskFieldProvinceId() {
	l.SQLModel.MaskField(l.ProvinceId, gocommon.MASK_TYPE_PROVINCE)
}
