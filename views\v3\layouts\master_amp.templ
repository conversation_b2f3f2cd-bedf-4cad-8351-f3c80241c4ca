package layouts

import "webgo/hpv/entity"
import "webgo/views/v3/partials"
import "webgo/pkg/gos/templates"
import "webgo/views/v3/partials/menus"
import "webgo/pkg/sctx/configs"

templ MasterAMP(seo *entity.Seo, heads []templ.Component,footer templ.Component, scripts ...templ.Component) {
    <!DOCTYPE html>
    <html lang="en">
        <head>
            <meta charset="UTF-8">
            <link rel="canonical" href="https://hpv.vn">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <link rel="icon" href={templates.AssetURL("/asset/favicon.ico")} type="image/x-icon" />
            <meta name="google-site-verification" content="PVN1jbQd9YNTj71nERcs7lQWrrrxq3mZUsuvYhEr1yE" />
            @partials.Seo(seo)
            <link rel="stylesheet" href={ templates.AssetURL("/asset/css/main.css") }>
            <link rel="stylesheet" href={ templates.AssetURL("/asset/css/layout/menu/menu-2.css") }>
            <link rel="stylesheet" href={ templates.AssetURL("/asset/css/components/fast-action-controls.css") }>
            <script async src="https://cdn.ampproject.org/v0.js"></script>
            <style amp-boilerplate>body{visibility:hidden}</style>	
            // <link rel="stylesheet" href={ templates.AssetURL("/asset/css/components/modal_pop_up_moh.css") }>
            if configs.AppEnv == configs.AppDev{
                <meta name="robots" content="noindex">
            }
            if len(heads) > 0 {
                for _, head := range heads {
                    @head
                }
            }
            @partials.ScriptTracking()   
        </head>
        <body class="root-container">
            @menus.Headers()

            { children... }

            if footer != nil {
                @footer
            } else {
                @partials.FooterDefault()
            }

            // @partials.ModalPopupMOH()
            @partials.HPVSvgIcon()
            @partials.HPVFastActionControls()         
            @partials.HPVModalLocations() 
            @partials.HPVModalStart()
            
            // @partials.ScriptMOHBanner()
            @partials.JSMain()
            
            if len(scripts) > 0 {
                for _, script := range scripts {
                    @script
                }
            }                 
            
        </body>
    </html>
}