package mapping

import (
	"webgo/hpv/entity"
	"webgo/hpv/locations/transport/responses"
)

func MapperDistrictEntityToKeyValue(districts []*entity.DistrictEntity) *[]responses.DistrictValueLabelResp {
	if districts == nil {
		return nil
	}
	var datas []responses.DistrictValueLabelResp
	for _, dis := range districts {
		dis.MaskFieldDistrictId()
		datas = append(datas, responses.DistrictValueLabelResp{
			Value: dis.FakeId.String(),
			Label: dis.Title,
		})
	}

	return &datas
}
