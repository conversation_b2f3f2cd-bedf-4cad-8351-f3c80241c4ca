package usecase

import (
	"context"
	"errors"
	"webgo/hpv/common/enums"
	"webgo/hpv/entity"
	"webgo/hpv/femalesv3/mapping"
	"webgo/hpv/femalesv3/transport/responses"
	"webgo/pkg/gos/utils"
	"webgo/pkg/sctx/core"
)

type FemaleV3CategoryRepo interface {
	FirstCategoryRepo(ctx context.Context, filter *utils.Filters) (*entity.CategoryEntity, error)
}

type FemaleOptionValueRepo interface {
	FindOptionValueRepo(ctx context.Context, filter *utils.Filters) ([]*entity.OptionValue, error)
}

type femaleV3Usc struct {
	categoryRepo FemaleV3CategoryRepo
	optionRepo FemaleOptionValueRepo
}

func NewFemaleV3Usc(categoryRepo FemaleV3CategoryRepo, optionRepo FemaleOptionValueRepo) *femaleV3Usc {
	return &femaleV3Usc{
		categoryRepo: categoryRepo,
		optionRepo: optionRepo,
	}
}

/**
 * usc for hdl female v3
 */
func (usc *femaleV3Usc) GetFemaleV3Usc(ctx context.Context) (*responses.FemaleV3Resp, error) {
	filter := &utils.Filters{
		Conds: &map[string]interface{} {
			"id":   enums.CATEGORY_FEMALE,
			"status": enums.STATUS_ACTIVE,
		},
	}
	
	category, err := usc.categoryRepo.FirstCategoryRepo(ctx, filter)
	if err != nil {
		return nil, err
	}

	options, err := usc.getOptionValueFemale(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	return &responses.FemaleV3Resp{
		Seo:   		category.Seo,
		Options:    options,
	}, nil

}

func (usc *femaleV3Usc) getOptionValueFemale(ctx context.Context) (map[string]string, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"option_group_id": enums.CATEGORY_FEMALE,
			"status":          enums.STATUS_ACTIVE,
		},
		Columns: &[]string{
			"key",
			"content",
		},
	}

	options, err := usc.optionRepo.FindOptionValueRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return mapping.MapperOptionValueToMapFemale(options), nil
}


