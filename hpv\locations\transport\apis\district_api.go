package apis

import (
	"context"
	"webgo/hpv/common/errs"
	"webgo/hpv/locations/transport/requests"
	"webgo/hpv/locations/transport/responses"

	"github.com/gofiber/fiber/v2"
)

type ApiDistrictUsc interface {
	WithProvinceApiDistrictUsc(ctx context.Context, provinceId int64) (*[]responses.DistrictValueLabelResp, error)
}

type districtApi struct {
	usc ApiDistrictUsc
}

func NewDistrictApi(usc ApiDistrictUsc) *districtApi {
	return &districtApi{
		usc: usc,
	}
}

/**
 * list distruct by province_id
 */
func (a *districtApi) WithProvinceDistrictApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.PayloadEditReq
		if err := c.BodyParser(&payload); err != nil {
			return errs.ReturnErrForApi(c, err.Error())
		}

		if err := payload.Validate(); err != nil {
			return errs.ReturnErrForApi(c, err.Error())
		}

		data, err := a.usc.WithProvinceApiDistrictUsc(c.Context(), payload.ID)
		if err != nil {
			return errs.ReturnErrForApi(c, err.Error())
		}

		return c.JSON(fiber.Map{
			"data": data,
		})
	}
}
