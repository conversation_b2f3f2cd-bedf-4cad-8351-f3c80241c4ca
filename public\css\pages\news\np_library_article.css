.library-wrapper {
max-width: 100vw;
padding: 2em;
background: rgba(246, 248, 250, 1);
margin-top: 2em;
border-radius: 1vw;
font-size: 0.652vw;
}
.lib-grid-box{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1em;
}
.lib-grid-box__title {
    font-size: 2.4em;
    font-weight: 700 ;
    color: var(--primary-500);
}
.lib-grid-box__seemore {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1em;
    cursor: pointer;
    color: #7c7c7c;
    font-size: 1.4em;
    font-weight: 700;
}

.lib-grid-box__seemore-icon{
    width: 1.3em;
    aspect-ratio: 1;
}
.lib-grid-box__seemore-icon img{
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.library-grid {
    display: grid;
    grid-template-columns: 40vw 1fr;
    gap: 3em;
}
/* left article */
.library-featured {
    background: #fff;
    border-radius: 1em;
    overflow: hidden;
    box-shadow: 0 0 0.5px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
}
.library-featured-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.library-featured-content {
    padding: 1em;
}
.library-featured-title {
    font-size: 2em;
    font-weight: bold;
    margin-bottom: 0.5em;
}
.library-featured-desc {
    font-size: 1.6em;
    color: var(--neutral-100);
    margin-bottom: 1em;
}
/* right article */
.library-articles {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 3em;
}
.library-featured-cont-img{
    position: relative;
    width: 100%;
    aspect-ratio: 4 / 3;
    overflow: hidden;
}
.library-featured:hover .library-featured-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transform: scale(1.05);
    transition: transform 0.3s linear;
}

.lib--col {
    display: flex;
    flex-direction: column;
    gap: 1em;
}
.liba__grid--col {
    border-radius: 1em;
    background-color: #ffffff;
    gap: 0em;
    font-size: 0.652vw;
    border-top-left-radius: 1em;
    border-top-right-radius: 1em;
    overflow: hidden;
}
.liba__grid--col:hover .lib__thumb-img {
    transform: scale(1.05);
    transition: transform 0.3s linear;
}

.lib__thumb--col {
    position: relative;
    width: 100%;
    aspect-ratio: 4 / 3;
    height: 65%;
}
.lib__thumb-img{
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-top-left-radius: 1em;
    border-bottom-left-radius: 1em;
}
.ana__grid--row:hover .ana__thumb-img--row{
    transform: scale(1.05);
    transition: transform 0.3s linear;
}

.ana__thumb-img--row {
    border-top-left-radius: 1em;
    border-top-right-radius: 1em;
    border-bottom-left-radius: 0;
}
.lib__content{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 1em;
    gap: 1em;
    height: 35%;
}
.lib__title {
    font-size: 1.4em;
    font-weight: 600;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 4.05em;
    line-height: 1.4;
}
.lib__bottom{
    font-size: 0.8em;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.lib__support{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2em;
}
.lib__support-item{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5em;
    color: rgb(var(--gray));
}
.lib__support-item span {
    font-size: 1.2em;
    color: var(--txt-lightgray);
    font-style: italic;
}
.gender-article-meta .nitem__support-item {
    font-size: 1.2em;
}
/* Responsive */
@media (max-width: 768px) {
    .lib-grid-box__title {
        font-size: 1.569rem;
    }
    .library-grid {
        gap: 2em;
        display: flex;
        flex-direction: column;
    }
    .library-articles {
        gap: 1.5em;
        display: flex;
        flex-direction: column;
    }
    .lib__thumb.lib__thumb--col {
        display: none;
    }
    .liba__grid--col {
        border-radius: 0;
        gap: 0em;
        font-size: 1.5vw;
        border-top: 1px solid var(--neutral-500);
    }
    .library-featured {
        font-size: 1.25vw;
    }
    .library-wrapper {
        padding: 4em;
    }
    .lib-grid-box__seemore {
        font-size: 1.5vw;
    }
}
@media (max-width: 575px) {
    .library-articles {
        gap: 0.5em;
    }
    .library-grid {
        gap: 1em;
    }
    .liba__grid--col {
        font-size: 2.5vw
    }
    .lib-grid-box {
        font-size: 1.5vw;
    }
    .library-featured {
        font-size: 2vw;
    }
    .lib-grid-box__seemore {
        font-size: 2vw;
    }
}


