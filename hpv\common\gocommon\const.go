package gocommon

const (
	MASK_TYPE_USER               = 1
	MASK_TYPE_ROLE               = 2
	MASK_TYPE_PERMISSION         = 3
	MASK_TYPE_TAG                = 4
	MASK_TYPE_HISTORY            = 5
	MASK_TYPE_TRASH              = 6
	MASK_TYPE_HOUSEHOLD          = 7
	MASK_TYPE_HOUSEHOLD_GROUP    = 8
	MASK_TYPE_HOUSEHOLD_TYPE     = 9
	MASK_TYPE_HOUSEHOLD_MATERIAL = 10
	MASK_TYPE_HOUSEHOLD_SIZE     = 11

	//mask hpv
	MASK_TYPE_LOCATION_TYPE = 100
	MASK_TYPE_LOCATION      = 101
	MASK_TYPE_PROVINCE      = 102
	MASK_TYPE_DISTRICT      = 103

	DEFAULT_IMG       = "/default.jpg"
	DEFAULT_IMG_ADMIN = "/user-dummy-img.jpg"

	CREATE_FOLDER_TRUE bool = true
	LIMIT_10                = 10

	STATUS_ACTIVE   = 1
	STATUS_INACTIVE = 2
	STATUS_DELETE   = 3
	STATUS_ARCHIVE  = 4

	PHONE_09 string = "09"
	PHONE_08 string = "08"
	PHONE_07 string = "07"
	PHONE_05 string = "05"
	PHONE_03 string = "03"

	FORMAT_ID_DATE           = "20060102150405.000"
	FORMAT_ID_DATE_TO_SECOND = "20060102150405"

	PRIVATE   = 1
	UNPRIVATE = 0

	YYYY_MM_DD_HH_MM    = "2006-01-02 15:04"
	YYYY_MM_DD_HH_MM_SS = "2006-01-02 15:04:05"
	YYYY_MM_DD          = "2006-01-02"
	YYYYMMDD            = "20060102"

	ROLE_NAME_ADMIN = "ADMIN"

	JOB_POSTQA_STATUS = "job postqa status schedule"
	JOB_DELETE_TRASH  = "job delete trash schedule"
)

var (
	STATUS_CREATE = map[int]string{
		STATUS_ACTIVE:   "Active",
		STATUS_INACTIVE: "Inactive",
	}
	STATUS_FULL = map[int]string{
		STATUS_ACTIVE:   "Active",
		STATUS_INACTIVE: "Inactive",
		STATUS_DELETE:   "Deleted",
		STATUS_ARCHIVE:  "Archive",
	}

	STATUS_LIST = map[int][]string{
		STATUS_ACTIVE:   {"Active", "badge-soft-success"},
		STATUS_INACTIVE: {"Inactive", "badge-soft-info"},
		STATUS_DELETE:   {"Deleted", "badge-soft-dark"},
		STATUS_ARCHIVE:  {"Archive", "badge-soft-dark"},
	}

	CHECK_PHONE = []string{
		PHONE_09,
		PHONE_08,
		PHONE_07,
		PHONE_05,
		PHONE_03,
	}
)
