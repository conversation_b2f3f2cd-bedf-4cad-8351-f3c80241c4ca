package locations

import (
	"webgo/hpv/locations/transport/responses"
    "webgo/views/v3/locations/components"
	"webgo/pkg/gos/templates"
	"webgo/views/v3/layouts"
)

templ Locations(result *responses.LocationsResp) {
	@layouts.Master(result.Seo, []templ.Component{head()}, footerLocations(result), locationsPageJS()) {
		<main class="container container-content" id="main-container">
			<div class="main-consultant-content" id="body-main-content">
                if result.Provinces != nil && len(*result.Provinces) > 0 {
                   	@components.SearchLocationsCpn(*result.Provinces)
                }

                if result.LocationsNew != nil && len(*result.LocationsNew) > 0 {
                    {{ data := *result.LocationsNew }}

		            @components.ListLocationsCpn(data)

                    @components.DetailLocationsCpn(data[0])
                }
			</div>
		</main>
	}
}

templ head() {
	<link rel="stylesheet" href={ templates.AssetURL("/asset/css/pages/consult-location/page-locations.css") }/>
}

func footerLocations(result *responses.LocationsResp) templ.Component {
	if banner, ok := result.Options["footer-locations"]; ok {
		return templ.Raw(banner)
	}
	return nil
}

templ locationsPageJS() {
	<script type="text/javascript" src={ templates.AssetURL("/asset/js/pages/selected-cus.js") } defer></script>
	<script type="text/javascript" src={ templates.AssetURL("/asset/js/pages/locations.js") } defer></script>
	<script>
        const customForm = document.querySelector(".custom-form");
        const customSelects = document.querySelectorAll(".custom-form__select");

        function initCustomDropdown(selectEl) {
            const options = Array.from(selectEl.options);
            const wrapper = document.createElement("div");
            wrapper.className = "custom-dropdown";
            selectEl.parentNode.insertBefore(wrapper, selectEl.nextSibling);

            // Create dropdown elements
            const select = document.createElement("div");
            select.className = "custom-dropdown__select";
            select.textContent = options[0].textContent;

            const menu = document.createElement("div");
            menu.className = "custom-dropdown__menu";

            const search = document.createElement("input");
            search.className = "custom-dropdown__search";
            search.placeholder = "Search...";

            const menuInner = document.createElement("div");
            menuInner.className = "custom-dropdown__menu-inner";

            // Build options
            options.forEach(option => {
                const item = document.createElement("div");
                item.className = "custom-dropdown__item";
                item.dataset.value = option.value;

                // Giữ nguyên data-id
                if (option.dataset.id) {
                    item.dataset.id = option.dataset.id;
                }

                item.textContent = option.textContent;
                menuInner.appendChild(item);
            });

            // Assemble structure
            menu.appendChild(search);
            menu.appendChild(menuInner);
            wrapper.appendChild(select);
            wrapper.appendChild(menu);

            // Hide original select
            selectEl.style.display = "none";

            // Add event listeners
            select.addEventListener("click", () => toggleMenu(menu));
            search.addEventListener("input", (e) => filterOptions(e.target.value, menuInner));
            menuInner.addEventListener("click", (e) => handleItemSelect(e, select, selectEl, menu));
            document.addEventListener("click", (e) => closeOnOutsideClick(e, wrapper, menu));
        }

        function toggleMenu(menu) {
            menu.classList.toggle("custom-dropdown__menu--open");
            menu.querySelector(".custom-dropdown__search").focus();
        }

        function filterOptions(searchTerm, menuInner) {
            const items = menuInner.querySelectorAll(".custom-dropdown__item");
            const searchLower = searchTerm.toLowerCase();

            items.forEach(item => {
                const matches = item.textContent.toLowerCase().includes(searchLower);
                item.style.display = matches ? "block" : "none";
            });
        }

        function handleItemSelect(e, selectDisplay, originalSelect, menu) {
            const item = e.target.closest(".custom-dropdown__item");
            if (!item) return;

            selectDisplay.textContent = item.textContent;
            originalSelect.value = item.dataset.value;

            // Cập nhật data-id cho select gốc (nếu cần sử dụng)
            if (item.dataset.id) {
                originalSelect.dataset.id = item.dataset.id;
            }

            menu.classList.remove("custom-dropdown__menu--open");

            // Update selected state
            menu.querySelectorAll(".custom-dropdown__item").forEach(i =>
                i.classList.remove("custom-dropdown__item--selected"));
            item.classList.add("custom-dropdown__item--selected");
        }

        function closeOnOutsideClick(e, dropdownWrapper, menu) {
            if (!dropdownWrapper.contains(e.target)) {
                menu.classList.remove("custom-dropdown__menu--open");
            }
        }

        // Initialize all custom selects
        if (customSelects.length) {
            customSelects.forEach(select => initCustomDropdown(select));
        }

        if (customForm) {
            customForm.addEventListener("submit", e => e.preventDefault());
        }
    </script>
}
