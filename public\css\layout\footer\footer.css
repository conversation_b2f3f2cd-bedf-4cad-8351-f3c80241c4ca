.footer-container {
  text-align: center;
  color: var(--primary-500);
  margin-top: 0px;
  display: block;
}

.footer__content {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: center;
  color: var(--neutral-200);
  padding: 5em;
  text-align: center;
  align-items: center;
}
.footer__content .code-footer {
  display: inline-block;
}
.footer__content a,
.footer__content h3 {
  font-size: 1.5em;
  font-weight: 500;
  display: inline-block;
  color: var(--neutral-300);
}
.footer__content h3 {
  font-weight: 300;
}
.footer__content a {
  font-size: 1.5em;
  font-weight: 500;
  color: var(--neutral-300);
}
.footer__content a:hover {
  color: var(--primary-500);
}
.footer__content .direction {
  display: flex;
  align-items: center;
  gap: 1em;
}

@media (max-width: 1099px) {
  .footer-container {
    display: none;
  }
  .footer__content .code-footer {
    display: contents;
  }
  .footer__content a,
  .footer__content h3 {
    font-size: 1em;
  }
  .footer__content .direction {
    margin-top: 1em;
  }
}/*# sourceMappingURL=footer.css.map */