.slide-posts .posts {
  font-size: 1rem;
}

.post-item {
  border-radius: var(--radius-sm);
  background-color: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  text-align: center;
  font-weight: 400;
}
.post-item__title {
  text-transform: uppercase;
  font-size: 1.2em;
  padding: 0.8em 1em;
  color: var(--primary-500);
  line-height: 1.3;
  height: 6em;
}
.post-item__title span {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5em;
}
.post-item__des {
  text-transform: capitalize;
  font-size: 1.3em;
  margin: 0.8em 1em;
  font-weight: 300;
  height: 4.5em;
}
.post-item__avatar {
  aspect-ratio: 3/2;
}
.post-item__avatar img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.post-item__tile, .post-item__des {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5em;
}
.post-item__action {
  margin-bottom: 1em;
}
.post-item__action a {
  color: var(--primary-500);
  display: inline-block;
  padding: 0.7em 1.2em;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);
  background-color: var(--secondary-500);
  cursor: pointer;
  border-radius: var(--radius-md);
  font-size: 1.2em;
  transition: all 0.3s ease-in;
}
.post-item__action a:hover {
  background-color: var(--third-700);
}
.slide-posts .posts.swiper .swiper-pagination {
  position: relative;
  margin-top: 2.2em;
}
.slide-posts .posts.swiper .swiper-pagination .swiper-pagination-bullet-active {
  background-color: var(--secondary-500);
}
.slide-posts .posts.swiper .swiper-button-next,
.slide-posts .posts.swiper .swiper-button-prev {
  background-color: transparent;
  width: 4.5em;
  height: 4.5em;
  border-radius: 50%;
  transition: all 0.2s ease-in;
}
.slide-posts .posts.swiper .swiper-button-next::after,
.slide-posts .posts.swiper .swiper-button-prev::after {
  font-size: 2.5em;
  color: var(--secondary-500);
}
.slide-posts .posts.swiper .swiper-button-next:hover,
.slide-posts .posts.swiper .swiper-button-prev:hover {
  background-color: rgba(255, 255, 255, 0.7);
  box-shadow: 1px 1px 2px rgba(51, 51, 51, 0.3058823529);
}

.posts.swiper.is-blue .post-item__title {
  color: white;
  background-color: var(--third-500);
}
.posts.swiper.is-blue .post-item__action a {
  background-color: var(--third-700);
  color: white;
}
.posts.swiper.is-blue .post-item__action a:hover {
  background-color: var(--third-600);
}
.posts.swiper.is-blue .swiper-pagination .swiper-pagination-bullet-active {
  background-color: var(--third-500);
}
.posts.swiper.is-blue .swiper-button-next::after,
.posts.swiper.is-blue .swiper-button-prev::after {
  color: var(--third-500);
}

.posts.swiper.is-blue .post-item__title {
  color: white;
  background-color: var(--third-500);
}
.posts.swiper.is-blue .post-item__action a {
  background-color: var(--third-700);
  color: white;
}
.posts.swiper.is-blue .post-item__action a:hover {
  background-color: var(--third-600);
}
.posts.swiper.is-blue .swiper-pagination .swiper-pagination-bullet-active {
  background-color: var(--third-500);
}
.posts.swiper.is-blue .swiper-button-next::after,
.posts.swiper.is-blue .swiper-button-prev::after {
  color: var(--third-500);
}

.is-yellow .slide-posts {
  background-color: transparent;
}
.is-yellow .posts.swiper .post-item__title {
  color: var(--primary-500);
  background-color: var(--secondary-500);
}
.is-yellow .posts.swiper .post-item__action a {
  background-color: var(--secondary-500);
  color: var(--primary-500);
}
.is-yellow .posts.swiper .post-item__action a:hover {
  background-color: var(--secondary-600);
}
.is-yellow .posts.swiper .swiper-pagination .swiper-pagination-bullet-active {
  background-color: var(--secondary-500);
}
.is-yellow .posts.swiper .swiper-button-next::after,
.is-yellow .posts.swiper .swiper-button-prev::after {
  color: var(--secondary-500);
}

.slide-posts#mobile-slide-posts {
  padding: 0.8em;
  display: block;
}

@media (min-width: 1100px) {
  .slide-posts#mobile-slide-posts {
    display: none;
  }
}
.slide-posts#mobile-slide-posts {
  padding: 0.8em;
  display: block;
}

@media (min-width: 1100px) {
  .slide-posts#mobile-slide-posts {
    display: none;
  }
}/*# sourceMappingURL=mobile-slide-posts.css.map */