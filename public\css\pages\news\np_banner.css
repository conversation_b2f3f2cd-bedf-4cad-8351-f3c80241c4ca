.np-banner-head {
    position: relative;
    width: 100%;
    height: 100%;
    margin: 1vw 0 3vw 0;
}

.np-banner-head__wrapper {
    width: 100%;
    aspect-ratio: 3044 / 864;
}

.np-banner-head__wrapper img {
    width: 100%;
    height: inherit;
    object-fit: cover;
}

@media screen and (max-width: 768px) {
    .np-banner-head__wrapper {
        aspect-ratio: 880 / 529;
    }
    .np-banner-head__wrapper {
        padding: 0 !important;
        max-width: 100% !important;
    }
}

.main-menu .nav-menu>ul {
    justify-content: center;
}

.main-menu {
    background-color: var(--neutral-900);
    padding-left: 1%;
    padding-right: 1%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.menu-header.is-sticky {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.menu-header.is-sticky .main-menu {
    box-shadow: none;
}

.fast-action-controls {
    font-size: 0.7vw;
}

.fast-action-controls .action-item {
    border-radius: 0.5em;
    height: 3em;
}

/* <PERSON><PERSON><PERSON> scroll lên đầu */
.scroll-top {
    position: fixed;
    bottom: 1vw;
    right: 6vw;
    width: 4em;
    height: 4em;
    background: #ffffff;
    border: solid 1px #b0b0b0;
    border-radius: 0.5em;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
}

.scroll-top.show {
    opacity: 1;
    visibility: visible;
}

.scroll-top:hover {
    background: #ccc;
}

.scroll-top .icon-up {
    width: 24px;
    height: 24px;
}

/* main */
.footer__content {
    background: var(--primary-500);
    padding: 2vw 0;
    margin-top: 3vw;
    font-size: 0.625vw;
}

.menu-header {
    transition: none;
}

.footer__content a:hover {
    color: var(--neutral-100);
}

@media (max-width: 1099px) {
    .fast-action-controls {
        display: none;
    }

    body {
        max-height: auto;
        min-height: 100svh;
        overflow-y: auto;
    }

    .footer__content {
        padding-bottom: 75px;
    }

    .scroll-top {
        bottom: 8vw;
    }
}

@media (max-width: 768px) {
    .footer__content {
        font-size: 1vw;
}
}

@media (max-width: 575px) {
    .main-menu {
        border-radius: 0;
    }

    .footer__content {
        padding-bottom: 75px;
    }

    .footer__content {
        font-size: 2vw;
    }
    .scroll-top {
        bottom: 15vw;
    }
}