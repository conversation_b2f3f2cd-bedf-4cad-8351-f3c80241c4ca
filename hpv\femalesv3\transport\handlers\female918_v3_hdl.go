package handlers

import (
	"context"
	"errors"
	"webgo/hpv/femalesv3/transport/responses"
	"webgo/pkg/gos/templates"
	"webgo/pkg/sctx/core"
	"webgo/views/v3/females"

	"github.com/gofiber/fiber/v2"
)

type Female918V3PostUsc interface {
	ListFemale918V3Usc(ctx context.Context) (*responses.Female918V3Resp, error)
}

type female918V3Hdl struct {
	usc Female918V3PostUsc
}

func NewFemale918V3Hdl(usc Female918V3PostUsc) *female918V3Hdl {
	return &female918V3Hdl{
		usc: usc,
	}
}

/***
 * 
 * Page: Female 918
 */

func (h *female918V3Hdl) ListFemale918V3Hdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		datas, err := h.usc.ListFemale918V3Usc(c.Context())
		if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
			return c.Redirect("/page-404")
		}
			
		return templates.Render(c, females.Female918(datas))
	}
}