package usecase

import (
	"context"
	"errors"
	"webgo/hpv/common/enums"
	"webgo/hpv/entity"
	"webgo/hpv/mohV3/mapping"
	"webgo/hpv/mohV3/transport/responses"
	"webgo/pkg/gos/utils"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/core"

	"gorm.io/gorm/clause"
)

type MohPostRepo interface {
	FindPostRepo(ctx context.Context, filter *utils.Filters) ([]*entity.PostEntity, error)
}

type CategoryRepoMoh interface {
	FirstCategoryRepo(ctx context.Context, filter *utils.Filters) (*entity.CategoryEntity, error)
}

type MohOptionValueRepo interface {
	FindOptionValueRepo(ctx context.Context, filter *utils.Filters) ([]*entity.OptionValue, error)
}

type mohUsc struct {
	repo         MohPostRepo
	optionRepo   MohOptionValueRepo
	logger       sctx.Logger
	categoryRepo CategoryRepoMoh
}

func NewMohUsc(repo MohPostRepo, optionRepo MohOptionValueRepo, categoryRepo CategoryRepoMoh, logger sctx.Logger) *mohUsc {
	return &mohUsc{
		repo:       repo,
		optionRepo: optionRepo,
		categoryRepo: categoryRepo,
		logger:     logger,
	}
}

/**
 * List moh usc
 */
func (usc *mohUsc) ListMohUsc(ctx context.Context) (*responses.MohListResp, error) {
	postMohNew, err := usc.postWithFilterCategory(ctx, []int64{enums.CATEGORY_TIN_TUC}, enums.LIMIT_10, enums.ORDERBY_ID_DESC)
	if err != nil {
		return nil, err
	}

	options, err := usc.getOptionValueMoh(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	seo, err := usc.getCategoryMoh(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	return &responses.MohListResp{
		MohNew:  postMohNew,
		Options: options,
		Seo:     seo,
	}, nil
}

/**
 * post with categoryId
 *
 * @param ctx
 * @param categoryId
 * @param limit
 * @param orderBy
 *
 * @return []responses.MohItem
 * @return error
 */
func (usc *mohUsc) postWithFilterCategory(ctx context.Context, categoryIds []int64, limit int, orderBy string) ([]responses.MohItem, error) {
	conds := map[string]interface{}{
		"status": enums.POST_STATUS_PUBLISH,
	}
	if len(categoryIds) > 0 {
		subQuery := "SELECT post_id FROM cms.categories_posts WHERE category_id IN (?)"
		conds["id IN (?)"] = clause.Expr{SQL: subQuery, Vars: []interface{}{categoryIds}}
	}

	filter := utils.Filters{
		Conds: &conds,
		Columns: &[]string{
			"id",
			"page_path",
			"title",
			"img",
			"description",
			"views",
		},
	}
	if orderBy != "" {
		filter.OrderBy = &[]string{orderBy}
	} else {
		filter.OrderBy = &[]string{enums.ORDERBY_ID_DESC}
	}

	if limit > 0 {
		filter.PageSize = limit
	} else {
		filter.PageSize = enums.LIMIT_10
	}

	posts, err := usc.repo.FindPostRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return mapping.MapperPostToMohItemResq(posts), nil
}

/**
 * get option value
 *
 */
func (usc *mohUsc) getOptionValueMoh(ctx context.Context) (map[string]string, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"option_group_id": enums.OPTION_GROUP_MOH,
			"status":          enums.STATUS_ACTIVE,
		},
		Columns: &[]string{
			"key",
			"content",
		},
	}

	options, err := usc.optionRepo.FindOptionValueRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return mapping.MapperOptionValueToMapMoh(options), nil
}

/**
 * get seo Moh
 */
func (usc *mohUsc) getCategoryMoh(ctx context.Context) (*entity.Seo, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"id":     enums.CATEGORY_CHIEN_DICH_TOAN_QUOC,
			"status": enums.STATUS_ACTIVE,
		},
		Columns: &[]string{
			"seo",
		},
	}

	category, err := usc.categoryRepo.FirstCategoryRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return category.Seo, nil
}
