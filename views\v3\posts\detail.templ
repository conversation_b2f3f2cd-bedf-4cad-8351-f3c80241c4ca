package posts

import "webgo/views/v3/posts/components"
import "webgo/hpv/postsv3/transport/responses"
import "webgo/pkg/gos/templates"
import "webgo/hpv/entity"
import "webgo/views/v3/layouts"

templ DetailV3(seo *entity.Seo, detail *responses.PostDetail, postList *[]responses.PostListCategory, options map[string]string) {
    @layouts.Master(seo,[]templ.Component{head()}, footerDetail(options), scriptDetail()) {
        <main class="container container-detail">
           <ol class="breadcrumb mb-2">
                <li class="breadcrumb-item">
                    <a href="javascript: void(0);" title="Blog">Blog</a>
                </li>
                <li class="breadcrumb-item">
                    { detail.CategoryName }
                </li>
            </ol>     
            <h1 class="title-detail">{ detail.Title }</h1> 
            @components.ArticleMeta(detail)
            if detail != nil {
                @components.ListContent(detail)                
            }
            if postList != nil && len(*postList) > 0 {
                @components.PostListCategory(postList)                
            }

            <div id="to_top" class="scroll-top" title="Lên đầu trang">
                <svg class="icon-up" width="800px" height="800px" viewBox="0 0 24 24" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                d="M12 3C12.2652 3 12.5196 3.10536 12.7071 3.29289L19.7071 10.2929C20.0976 10.6834 20.0976 11.3166 19.7071 11.7071C19.3166 12.0976 18.6834 12.0976 18.2929 11.7071L13 6.41421V20C13 20.5523 12.5523 21 12 21C11.4477 21 11 20.5523 11 20V6.41421L5.70711 11.7071C5.31658 12.0976 4.68342 12.0976 4.29289 11.7071C3.90237 11.3166 3.90237 10.6834 4.29289 10.2929L11.2929 3.29289C11.4804 3.10536 11.7348 3 12 3Z"
                fill="#757575" />
                </svg>
            </div>

        </main>
    }
}

templ head() {
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/post/post.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/post/post_responsive.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/news/np_banner.css")}>
}

templ scriptDetail() {
    <script>
        document.querySelectorAll(".btn-tick-prevent, .tick-border").forEach((button) => {
            button.addEventListener("click", function (e) {
                const parent = this.closest(".group-tick");
                if (parent) {
                    const line = parent.querySelector(".line-moh");
                    const hand = parent.querySelector(".hand-img");
                    const btn = parent.querySelector(".btn-tick-prevent");

                    if (line) line.style.clipPath = "inset(0 0 0 0)";
                    if (hand) hand.style.opacity = "1";
                    if (btn) btn.classList.add("active");
                }
            });
        });
        const tickLdpBtn = document.getElementById("btn-tick-border-prevent-ldp-moh");
        const preventLdpBtn = document.getElementById("btn-tick-prevent-ldp-moh");

        if (tickLdpBtn && preventLdpBtn) {
            [tickLdpBtn, preventLdpBtn].forEach((btn) => {
                btn.addEventListener("click", function () {

                    setTimeout(() => {
                        window.location.href = "https://hpv.vn/chien-dich-toan-quoc";
                    }, 1500);
                });
            });
        }
    </script>
    <script>
    // copy link
    document.getElementById("copy-link").addEventListener("click", function () {
      const currentUrl = window.location.href;
  
      navigator.clipboard.writeText(currentUrl)
        .then(() => {
          const toast = document.getElementById("copyToast");
          toast.classList.add("show");

          setTimeout(() => {
            toast.classList.remove("show");
          }, 2000);
        })
        .catch(err => {
          console.error("Lỗi sao chép: ", err);
        });
    });
    </script>
    <script>
        // facebook share
    const currentURL = window.location.href;
    const pageTitle = document.title;

    document.getElementById("fb-share-icon").addEventListener("click", function(e) {
        e.preventDefault();
        const facebookShareURL = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(currentURL)}`;
        window.open(facebookShareURL, "_blank", "width=600,height=400");
    });
    </script>
    <script>
        // mail share
        document.getElementById("mail-share-btn").addEventListener("click", function(e) {
        e.preventDefault();
        const subject = `Check this out: ${pageTitle}`;
        const body = `Hey,\n\nI thought you might be interested in this page:\n${currentURL}`;
        const mailtoLink = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
        window.location.href = mailtoLink;
        });
    </script>  
}

func showBannerMoh(options map[string]string, key string) templ.Component{
    if banner, ok := options[key]; ok {
        return templ.Raw(banner)
    }
    return templ.Raw("")
}

templ footerDetail(options map[string]string){
    if banner, ok := options["footer-detail-opt"]; ok {
        @templ.Raw(banner)
    }
    else {
        @components.FooterDetailDefault()
    }
}