package components

import "webgo/pkg/gos/templates"

templ MohDetailSwipper() {
    <section class="section-detail-campain" id="section-detail-campain">
        <div class="detail-campain__group">
            <div class="detail-campain__left">
                <div class="group-swiper-detail">
                    <div style="--swiper-navigation-color: #fff; --swiper-pagination-color: #fff"
                        class="gallery__main-slider swiper-container-main">
                        <div class="gallery__wrapper swiper-wrapper">
                            <div class="gallery__slide swiper-slide">
                                <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/gallery-1.webp")} />
                            </div>
                            <div class="gallery__slide swiper-slide">
                                <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/gallery-2.webp")} />
                            </div>
                            <div class="gallery__slide swiper-slide">
                                <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/gallery-3.webp")} />
                            </div>
                            <div class="gallery__slide gallery__slide--video swiper-slide slide-4" data-swiper-slide-index="3">
                                <div class="youtube-thumb" data-video-id="HV9jRsIjNYw">
                                    <picture>
                                        <source srcset={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/video-hat-16x9.webp")} media="(max-width: 575px)">
                                        <img class="youtube-thumb__img" src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/video-hat-1344x911.webp")} alt="background home City">
                                    </picture>
                                    <div class="youtube-thumb__play-btn">
                                        <svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="32" cy="32" r="32" fill="rgba(0, 0, 0, 0.6)" />
                                            <polygon points="26,20 48,32 26,44" fill="#FFFFFF" />
                                        </svg>
                                    </div>
                                </div>
                                <iframe class="youtube-iframe" id="player4" data-player-id="player4"
                                    src="https://www.youtube.com/embed/HV9jRsIjNYw?enablejsapi=1" 
                                    allow="autoplay; encrypted-media" allowfullscreen
                                    style="display: none;">
                                </iframe>
                            </div>
                            <div class="gallery__slide gallery__slide--video swiper-slide slide-5" data-swiper-slide-index="4">
                                <div class="youtube-thumb" data-video-id="pVcHAYIAxmM">
                                    <picture>
                                        <source srcset={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/gallery-5-16x9.webp")}
                                            media="(max-width: 575px)">
                                        <img class="youtube-thumb__img"
                                            src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/gallery-5-1344x911.webp")}
                                            alt="background home City">
                                    </picture>
                                    <div class="youtube-thumb__play-btn">
                                        <svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="32" cy="32" r="32" fill="rgba(0, 0, 0, 0.6)" />
                                            <polygon points="26,20 48,32 26,44" fill="#FFFFFF" />
                                        </svg>
                                    </div>
                                </div>
                                <iframe class="youtube-iframe" id="player5" data-player-id="player5"
                                    src="https://www.youtube.com/embed/pVcHAYIAxmM?enablejsapi=1" allow="autoplay; encrypted-media" allowfullscreen
                                    style="display: none;">
                                </iframe>
                            </div>
                            
                            <div class="gallery__slide swiper-slide">
                                <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/gallery-4.webp")} />
                            </div>
                            <div class="gallery__slide swiper-slide">
                                <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/gallery-6.webp")} />
                            </div>
                            <div class="gallery__slide swiper-slide">
                                <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/gallery-7.webp")} />
                            </div>
                        </div>

                    </div>

                    <div class="group-bottom-swiper-thumb">
                        <div thumbsSlider="" class="gallery__thumbs-slider swiper-container-thumbs">
                            <div class="gallery__wrapper swiper-wrapper">
                                <div class="gallery__thumb swiper-slide">
                                    <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/gallery-1.webp")} />
                                </div>
                                <div class="gallery__thumb swiper-slide">
                                    <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/gallery-2.webp")} />
                                </div>
                                <div class="gallery__thumb swiper-slide">
                                    <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/gallery-3.webp")} />
                                </div>
                                <div class="gallery__thumb swiper-slide">
                                    <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/video-hat-16x9.webp")} />
                                </div>
                                <div class="gallery__thumb swiper-slide">
                                    <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/gallery-5-thumb.webp")} />
                                </div>
                                <div class="gallery__thumb swiper-slide">
                                    <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/gallery-4.webp")} />
                                </div>
                                <div class="gallery__thumb swiper-slide">
                                    <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/gallery-6.webp")} />
                                </div>
                                <div class="gallery__thumb swiper-slide">
                                    <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/gallery-7.webp")} />
                                </div>
                            </div>
                        </div>
                        <div class="gallery__button gallery__button--next swiper-button-next">
                            <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/button-next.svg")} />
                        </div>
                        <div class="gallery__button gallery__button--prev swiper-button-prev">
                            <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/button-prev.svg")} />
                        </div>
                    </div>

                </div>
                <div class="hat-tag">
                    <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/hat-tag.webp")} alt="tagline Ha Anh Tuan" />
                </div>
            </div>
            <div class="detail-campain__right">
                <div class="detail-campain__content">
                    <h2 class="detail-campain__title">
                        ĐẠI SỨ CHIẾN DỊCH
                        <span class="nowrap">HÀ ANH TUẤN</span>: CHUNG TAY VÌ MỘT VIỆT NAM
                        <span class="nowrap">KHÔNG CÒN GÁNH NẶNG</span> BỞI HPV!
                    </h2>
                    <p>
                        <b>Ca sĩ Hà Anh Tuấn</b> chính thức đảm nhận vai trò <b>Đại sứ Chiến dịch Truyền thông toàn quốc “Vì một Việt Nam không gánh nặng bởi HPV”.</b>
                        Trong khuôn khổ chiến dịch, <b>ca khúc chủ đề “Một Lời Hứa”</b> được đại sứ Hà Anh Tuấn thể hiện đã truyền tải thông điệp phòng vệ HPV qua góc nhìn gần gũi: yêu thương là hành động bảo vệ bản thân và những người thương yêu.
                    </p>
                    <span class="text-highlight">Một quyết định nhỏ hôm nay có thể bảo vệ sức khoẻ cho cả thế hệ mai sau.
                        Hãy chọn phòng vệ HPV
                        cùng đại sứ Hà Anh Tuấn!</span>
                    <div class="gallery__controls--extra">
                        <div class="gallery__btn--prev-custom">
                            <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/button-color-prev.svg")} />
                        </div>
                        <div class="gallery__pagination--custom"></div>
                        <div class="gallery__btn--next-custom">
                            <img src={templates.AssetURL("/asset/images/ldp-moh-v2/gallery-detail/button-color-next.svg")} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
}

templ MohDetailSwipperScript() {
    <script>
            let fixedPlayer = null;
            let galleryPlayers = {};
            let galleryMain = null;
            let videoStarted = false;

            function onYouTubeIframeAPIReady() {
                fixedPlayer = new YT.Player('youtube-player', {
                    events: {
                        'onReady': onFixedPlayerReady
                    }
                });

                document.querySelectorAll('.youtube-iframe[data-player-id]').forEach(iframe => {
                    const id = iframe.dataset.playerId;
                    galleryPlayers[id] = new YT.Player(iframe, {
                        events: {
                            'onReady': () => console.log('Ready: ' + id),
                           'onStateChange': (event) => {
                            if (event.data === YT.PlayerState.ENDED) {
                                const playerEl = event.target.getIframe();
                                const slide = playerEl.closest('.swiper-slide');
                                const index = slide?.dataset.swiperSlideIndex;

                                if (index === "3" && galleryMain) {
                                galleryMain.slideNext();

                                if (!galleryMain.autoplay.running) {
                                    galleryMain.autoplay.start();
                                }
                                } else if (index === "4") {
                                galleryMain.slideNext(); 
                                }
                            }
                            }

                        }
                    });
                });
            }

            function onFixedPlayerReady() {
                window.addEventListener('scroll', () => {
                    const container = document.querySelector('.container-video-fluid');
                    if (!container) return;
                    const rect = container.getBoundingClientRect();
                    const inView = rect.top < window.innerHeight && rect.bottom > 0;

                    if (!videoStarted && inView) {
                        fixedPlayer.mute();
                        fixedPlayer.playVideo();
                        container.classList.add('visible');
                        videoStarted = true;
                    }
                });
            }

            window.addEventListener("DOMContentLoaded", () => {
                let galleryThumbs = null;
                let hasInitialSlideSettled = false;

                galleryMain = new Swiper(".swiper-container-main", {
                    loop: true,
                    spaceBetween: 0,
                    autoplay: false,
                    navigation: {
                        nextEl: ".gallery__button--next, .gallery__btn--next-custom",
                        prevEl: ".gallery__button--prev, .gallery__btn--prev-custom",
                    },
                    pagination: {
                        el: ".gallery__pagination--custom",
                        clickable: true,
                        bulletClass: "gallery__bullet",
                        bulletActiveClass: "gallery__bullet--active",
                        renderBullet: (index, className) => `<span class="${className}"></span>`
                    },
                    thumbs: { swiper: null },
                    on: {
                        afterInit() {
                            this.slideToLoop(3, 0); 
                        },
                        transitionEnd() {
                            if (!hasInitialSlideSettled) {
                                hasInitialSlideSettled = true;
                                return; 
                            }
                            handleAutoplayLogic(this.realIndex);
                        },
                        slideChangeTransitionEnd() {
                            if (!hasInitialSlideSettled) return; 
                            handleVideoPlayback();
                        }
                    }
                });

                function initThumbs() {
                    galleryThumbs = new Swiper(".swiper-container-thumbs", {
                        spaceBetween: 20,
                        slidesPerView: 7,
                        freeMode: true,
                        watchSlidesProgress: true,
                    });
                    galleryMain.params.thumbs.swiper = galleryThumbs;
                    galleryMain.thumbs.init();
                    galleryMain.thumbs.update();
                }

                function destroyThumbs() {
                    if (galleryThumbs) {
                        galleryThumbs.destroy(true, true);
                        galleryThumbs = null;
                        galleryMain.thumbs.swiper = null;
                    }
                }

                function handleResize() {
                    window.innerWidth >= 575 ? initThumbs() : destroyThumbs();
                }

                handleResize();
                window.addEventListener("resize", () => {
                    handleResize();
                    updateGalleryButtons();
                });

                function updateGalleryButtons() {
                    const isMobile = window.innerWidth < 575;
                    const nextBtn = document.querySelector('.gallery__button--next img');
                    const prevBtn = document.querySelector('.gallery__button--prev img');
                    if (nextBtn && prevBtn) {
                        nextBtn.src = isMobile
                            ? '/asset/images/ldp-moh-v2/gallery-detail/button-color-next.svg'
                            : '/asset/images/ldp-moh-v2/gallery-detail/button-next.svg';
                        prevBtn.src = isMobile
                            ? '/asset/images/ldp-moh-v2/gallery-detail/button-color-prev.svg'
                            : '/asset/images/ldp-moh-v2/gallery-detail/button-prev.svg';
                    }
                }
                updateGalleryButtons();

                document.querySelectorAll('.youtube-thumb__play-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        e.preventDefault();
                        const slide = btn.closest('.gallery__slide--video');
                        const iframe = slide.querySelector('iframe.youtube-iframe');
                        const playerId = iframe?.dataset.playerId;
                        const player = galleryPlayers[playerId];

                        slide.querySelector('.youtube-thumb').style.display = 'none';
                        iframe.style.display = 'block';

                        if (player) {
                            player.mute();
                            player.playVideo();
                        }
                    });
                });

                window.addEventListener('scroll', () => {
                    const swiperEl = document.querySelector('.swiper-container-main');
                    if (!swiperEl) return;

                    const rect = swiperEl.getBoundingClientRect();
                    const inView = rect.top < window.innerHeight && rect.bottom > 0;

                    if (inView) {
                        const index = galleryMain.realIndex;
                        const activeSlide = swiperEl.querySelector('.swiper-slide-active');
                        if (activeSlide && activeSlide.classList.contains('gallery__slide--video')) {
                            if (galleryMain.autoplay.running) {
                                galleryMain.autoplay.stop();
                            }
                            setTimeout(() => handleVideoPlayback(), 2000);
                        }
                    }
                });

                function handleAutoplayLogic(index) {
                    const swiperEl = document.querySelector('.swiper-container-main');
                    const rect = swiperEl.getBoundingClientRect();
                    const inView = rect.top < window.innerHeight && rect.bottom > 0;

                    const isVideoSlide = [3, 4].includes(index);

                    if (isVideoSlide && inView) {
                        if (galleryMain.autoplay.running) {
                            galleryMain.autoplay.stop();
                        }
                        setTimeout(() => handleVideoPlayback(), 2000);
                    } else {
                        if (!galleryMain.autoplay.running) {
                            galleryMain.autoplay.start();
                        }
                        pauseAllVideos();
                    }
                }

               function handleVideoPlayback() {
                    document.querySelectorAll('.swiper-slide.gallery__slide--video').forEach(slide => {
                        const iframe = slide.querySelector('iframe.youtube-iframe');
                        const playerId = iframe?.dataset.playerId;
                        const player = galleryPlayers[playerId];
                        const thumb = slide.querySelector('.youtube-thumb');
                        const isActive = slide.classList.contains('swiper-slide-active');

                        if (player && typeof player.playVideo === 'function') {
                            if (isActive) {
                                if (thumb && thumb.style.display !== 'none') {
                                    thumb.style.display = 'none';
                                    iframe.style.display = 'block';
                                }

                                if (fixedPlayer && typeof fixedPlayer.pauseVideo === 'function') {
                                    fixedPlayer.pauseVideo();
                                }

                                const tryPlay = () => {
                                    if (player.getPlayerState() !== -1) {
                                        player.mute();
                                        player.playVideo();
                                    } else {
                                        setTimeout(tryPlay, 200);
                                    }
                                };
                                tryPlay();
                            } else {
                                player.pauseVideo();
                            }
                        }
                    });
                }

                function pauseAllVideos() {
                    Object.values(galleryPlayers).forEach(player => {
                        if (player && typeof player.pauseVideo === 'function') {
                            player.pauseVideo();
                        }
                    });
                }
            });
    </script>
}