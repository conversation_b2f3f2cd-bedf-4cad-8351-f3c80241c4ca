package repository

import (
	"context"
	"webgo/hpv/entity"
	"webgo/pkg/gos/generics"
	"webgo/pkg/gos/utils"

	"gorm.io/gorm"
)

type categoryRepo struct {
	db *gorm.DB
}

func NewCategoryRepo(db *gorm.DB) *categoryRepo {
	return &categoryRepo{db: db}
}

/**
 * first Category
 */
func (r *categoryRepo) FirstCategoryRepo(ctx context.Context, filter *utils.Filters) (*entity.CategoryEntity, error) {
	tableName := entity.CategoryEntity{}.TableName()
	return generics.FirstGeneric[entity.CategoryEntity](ctx, r.db, tableName, filter)
}
