package entity

import (
	"encoding/json"
	"time"
)

type CategoryEntity struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Content     string `json:"content"`
	Slug        string `json:"slug"`

	Img     string     `json:"img"`
	Banner  string     `json:"banner"`
	ListImg *ImageList `json:"list_img"`

	Status    int    `json:"status"`
	Link      string `json:"link"`
	IsPrivate int    `json:"is_private"`

	Parent int64 `json:"parent"`
	Lft    int64 `json:"lft"`
	Rgt    int64 `json:"rgt"`
	Level  int64 `json:"level"`

	Views    int64  `json:"views"`
	PagePath string `json:"page_path"`

	Options json.RawMessage `json:"options"`
	Seo     *Seo            `json:"seo"`

	CreatedBy *int64    `json:"created_by"`
	UpdatedBy *int64    `json:"updated_by"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (CategoryEntity) TableName() string {
	return "cms.categories"
}
