package requests

import (
	"fmt"
	"strconv"
	"strings"
	"webgo/hpv/common/errs"
	"webgo/hpv/locations/transport/rules"

	"webgo/pkg/sctx/core"
	"webgo/pkg/sctx/gorules"

	"github.com/go-playground/validator/v10"
)

type SearchLocationReq struct {
	ProvinceFakeId *string `json:"province_id" validate:"omitempty,ruleFakeId"`
	DistrictFakeId *string `json:"district_id" validate:"omitempty,ruleFakeId"`
	SearchAddress  *string `json:"search_address" validate:"omitempty,searchAddress"`
}

func (req *SearchLocationReq) Validate() error {
	// thieu tra provinceId va districtId = empty
	if req != nil && req.ProvinceFakeId == nil && req.DistrictFakeId == nil && req.SearchAddress == nil {
		fmt.Printf("req: %v\n", req)

		return errs.ErrValidateEmpty
	}

	// ProvinceFakeId = "      "

	validate := validator.New()
	validate.RegisterValidation("searchAddress", rules.RuleSearchAddressNoSQLInjection)
	validate.RegisterValidation("ruleFakeId", gorules.RuleValidateFakeId)

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "ProvinceFakeId":
				return errs.ErrValidateSearchProvince
			case "DistrictFakeId":
				return errs.ErrValidateSearchDistrict
			case "SearchAddress":
				return errs.ErrValidateSearchAddress
			}
		}
	}

	if req.ProvinceFakeId != nil && *req.ProvinceFakeId != "" {
		proUID, err := core.FromBase58(*req.ProvinceFakeId)
		if err != nil {
			return errs.ErrValidateSearchProvince
		}
		proID := strconv.FormatInt(int64(proUID.GetLocalID()), 10)
		req.ProvinceFakeId = &proID
	}

	
	if req.DistrictFakeId != nil && *req.DistrictFakeId != "" {
		disUID, err := core.FromBase58(*req.DistrictFakeId)
		if err != nil {
			return errs.ErrValidateSearchDistrict
		}
		proID := strconv.FormatInt(int64(disUID.GetLocalID()), 10)
		req.DistrictFakeId = &proID
	}

	// districtId, err := core.FromBase58(req.DistrictFakeId)

	// if req.provinceId != nil {
	// 	req.ProvinceId = int64(provinceId.GetLocalID())
	// }
	// if req.provinceId != nil {
	// 	req.DistrictId = int64(districtId.GetLocalID())
	// }

	if req != nil && req.SearchAddress != nil && *req.SearchAddress != "" {
		str := strings.TrimSpace(*req.SearchAddress)
		req.SearchAddress = &str
	}

	return nil
}
