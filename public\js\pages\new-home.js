addEventListener("DOMContentLoaded", (event) => {
  addEventDetectSidebarStuck();
  setupTabsNavigation();
  setupFooterShowMore();

  initSwiper2("#male-19-26-slide-article #article-list");

  // --- FIXED SIDEBAR-MENU ---
  function addEventDetectSidebarStuck() {
    document.addEventListener("scroll", () => {
      const stickyElement = document.querySelector(
        ".container-sidebar#sidebar-pc"
      );
      if (!stickyElement) return;

      const container = stickyElement.parentElement;
      const isStuck =
        stickyElement.getBoundingClientRect().top <= 80 &&
        container.getBoundingClientRect().top < 0;

      stickyElement.classList.toggle("is-sticky", isStuck);
    });
  }

  // --- TABS NAVIGATION ---
  function setupTabsNavigation() {
    const tabs = document.querySelectorAll(".radio");
    const prevButton = document.getElementById("prev-btn");
    const nextButton = document.getElementById("next-btn");

    if (!tabs.length || !prevButton || !nextButton) return;

    let currentIndex = Array.from(tabs).findIndex((tab) => tab.checked);

    const updateTab = (index) => {
      tabs[index].checked = true;
      currentIndex = index;
    };

    prevButton.addEventListener("click", () => {
      const newIndex = (currentIndex - 1 + tabs.length) % tabs.length;
      updateTab(newIndex);
    });

    nextButton.addEventListener("click", () => {
      const newIndex = (currentIndex + 1) % tabs.length;
      updateTab(newIndex);
    });
  }

  // --- FOOTER SHOW MORE ---
  function setupFooterShowMore() {
    const contentContainers = document.querySelectorAll(".content-footer");

    contentContainers.forEach((content) => {
      const showMoreButton = document.createElement("div");
      showMoreButton.classList.add("show-more");
      showMoreButton.innerHTML = "Xem thêm &#9660";
      content.parentNode.insertBefore(showMoreButton, content.nextSibling);

      showMoreButton.addEventListener("click", () => {
        if (content.style.maxHeight) {
          content.style.maxHeight = null;
          showMoreButton.innerHTML = "Xem thêm &#9660";
        } else {
          content.style.maxHeight = content.scrollHeight + "px";
          showMoreButton.innerHTML = "Ẩn &#9650";
        }
      });
    });
  }

  // --- SWIPER INIT ---
  function initSwiper2(pathSwiper) {
    new Swiper(pathSwiper, {
      autoplay: {
        delay: 4000,
      },
      loop: true,
      slidesPerView: 2,
      spaceBetween: 8,
      breakpoints: {
        992: {
          slidesPerView: 3,
          spaceBetween: 10,
        },
        1200: {
          slidesPerView: 4,
          spaceBetween: 20,
        },
      },
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
      },
      navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
      },
    });
  }
});

// --- COMMON HPV HOME_CONTENT_HPV_INFECTION ---
function toggleActive(tagElement, type) {
  // Clear auto-rotation on manual click
  if (!isAutoClick) {
    clearInterval(loopInterval);
    loopInterval = null;
    effectTriggered = false;
  }
  const tags = document.querySelectorAll(".title-classify__tag");
  tags.forEach((tag) => tag.classList.remove("active"));

  const statistics = document.querySelectorAll(
    ".common-hpv-diseases__statistic"
  );
  statistics.forEach((stat) => {
    stat.classList.remove("active");
    stat.classList.remove("male-color", "female-color", "both-genders-color");
  });

  const images = document.querySelectorAll(".common-hpv-diseases__image");
  images.forEach((image) => image.classList.remove("active"));

  tagElement.classList.add("active");

  if (type === "male") {
    document
      .querySelectorAll(".statistic-male, .statistic-both-genders")
      .forEach((stat) => {
        stat.classList.add("active");
        stat.classList.add("male-color");
      });
    document
      .querySelector(".common-hpv-diseases__image.male")
      .classList.add("active");
  } else if (type === "female") {
    document
      .querySelectorAll(".statistic-female, .statistic-both-genders")
      .forEach((stat) => {
        stat.classList.add("active");
        stat.classList.add("female-color");
      });
    document
      .querySelector(".common-hpv-diseases__image.female")
      .classList.add("active");
  } else if (type === "both-genders") {
    document.querySelectorAll(".statistic-both-genders").forEach((stat) => {
      stat.classList.add("active");
    });
    document
      .querySelectorAll(".common-hpv-diseases__image")
      .forEach((image) => {
        image.classList.add("active");
      });
  }
}

document.getElementById("male-img").addEventListener("click", () => {
  document.getElementById("male-tag").click();
});

document.getElementById("female-img").addEventListener("click", () => {
  document.getElementById("female-tag").click();
});

function statisticToggleActive(type) {
  if (type === "male") {
    document.getElementById("male-tag").click();
  } else if (type === "female") {
    document.getElementById("female-tag").click();
  } else if (type === "both-genders") {
    document.getElementById("both-genders-tag").click();
  }
}

const section = document.querySelector(".common-hpv-diseases");
let effectTriggered = false;
let loopInterval = null;
let isAutoClick = false;

let observer = new IntersectionObserver(
  (entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting && !effectTriggered) {
        effectTriggered = true;
        activateEffect();
      }
    });
  },
  { threshold: 0.5 }
);

observer.observe(section);

function activateEffect() {
  const tags = ["male", "female", "both-genders"];
  let activeIndex = 0;

  loopInterval = setInterval(() => {
    isAutoClick = true;
    const tag = document.getElementById(`${tags[activeIndex]}-tag`);
    if (tag) {
      toggleActive(tag, tags[activeIndex]);
    }
    isAutoClick = false;

    activeIndex = (activeIndex + 1) % tags.length;
  }, 10000);
}
// --- END - COMMON HPV HOME_CONTENT_HPV_INFECTION ---