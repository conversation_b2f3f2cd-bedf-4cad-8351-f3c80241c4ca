package male918

import "webgo/pkg/gos/templates"

templ Male918Banner() {
    <section class="male-banner-main wow animate__pulse" data-wow-duration="1.5s" data-wow-delay="0s">
        <div class="description-banner-wrapper">
            <p class="description-banner-left">Không phải là bệnh nhân thật, chỉ dành cho mục đích minh họa. Nội dung này được phối hợp biên soạn bởi Hội Y học Dự phòng Việt Nam và MSD, kiểm nhận bởi Hội Y học Dự phòng Việt Nam và MSD tài trợ cho mục tiêu giáo dục. VN-GSL-01871 09032027 <br>* Gần 50% các trường hợp nhiễm HPV xảy ra ở độ tuổi 15-24 (1)</p>
            <p class="description-banner-right">(1) CDC. Human Papillomavirus <br>https://www.cdc.gov/pinkbook/hcp/table-of-contents/chapter-11-human-papillomavirus.html?CDC_AAref_Val=https://www.cdc.gov/vaccines/pubs/pinkbook/hpv.html</p>    
        </div>
        <div class="content-banner">
            <span id="find-consultant-location-banner" class="btn-cta" alt="dia-diem-tu-van">
                THAM VẤN VỚI CHUYÊN GIA Y TẾ ĐỂ DỰ PHÒNG CHO CON
            </span>
        </div>
        <div class="img-banner">
            <div class="img-banner__thumbnail">
                <div class="img-banner-background">
                    <img src={templates.AssetURL("/assetimages/male-9-18-adapt/male918-desktop.webp")}
                        srcset={templates.AssetURL("/assetimages/male-9-18-adapt/male918-mobile.webp 575w, /asset/images/male-9-18-adapt/male918-desktop.webp")}
                        sizes="(max-width: 575px) 575px, 1024px" alt="Male person in the age range of 9 to 18" />
                </div>
            </div>
        </div>
    </section>
}