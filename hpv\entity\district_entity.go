package entity

import (
	"webgo/hpv/common/gocommon"
	"webgo/pkg/sctx/core"
)

type DistrictEntity struct {
	core.SQLModel
	ProvinceId int64  `json:"province_id"`
	DistrictId int64  `json:"district_id"`
	Title      string `json:"title"`
	Code       string `json:"code"`
	Status     int    `json:"status"`
	Ordering   int64  `json:"ordering"`
}

func (DistrictEntity) TableName() string {
	return "hpv.districts"
}

func (l *DistrictEntity) Mask() {
	l.SQLModel.Mask(gocommon.MASK_TYPE_DISTRICT)
}

func (l *DistrictEntity) MaskFieldDistrictId() {
	l.SQLModel.MaskField(l.DistrictId, gocommon.MASK_TYPE_DISTRICT)
}
