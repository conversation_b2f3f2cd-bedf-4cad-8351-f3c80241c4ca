package handlers

import (
	"context"
	"errors"
	"webgo/hpv/femalesv3/transport/responses"
	"webgo/pkg/gos/templates"
	"webgo/pkg/sctx/core"
	"webgo/views/v3/females"

	"github.com/gofiber/fiber/v2"
)

type Female2745V3PostUsc interface {
	ListFemale2745V3Usc(ctx context.Context) (*responses.Female2745V3Resp, error)
}

type female2745V3Hdl struct {
	usc Female2745V3PostUsc
}

func NewFemale2745V3Hdl(usc Female2745V3PostUsc) *female2745V3Hdl {
	return &female2745V3Hdl{
		usc: usc,
	}
}

/***
 * 
 * Page: Female 2745
 */

func (h *female2745V3Hdl) ListFemale2745V3Hdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		datas, err := h.usc.ListFemale2745V3Usc(c.Context())
		if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
			return c.Redirect("/page-404")
		}
			return templates.Render(c, females.Female2745Hariwon(datas))
	}
}