package menus 

import "webgo/pkg/gos/templates"

templ menuNav() {
    <header class="menu-header" id="menu">
        <div class="container main-menu">
            <!-- Modal Search -->
            <div class="modal-search" id="modal-search">
                <div class="overlay"></div>
                <div class="search-container">
                    <div class="search-content">
                    <div class="search-control">
                        <input type="text" placeholder="Nhập từ khoá tìm kiếm" class="input-control">
                        <button class="search">
                            Tìm kiếm
                        </button>
                        <span class="close-modal">
                            <svg width="1em" height="1em" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                                <path d="M15.4516 1.32027L1.30942 15.4624M1.30943 1.32027L15.4516 15.4624"
                                stroke="currentColor">
                                </path>
                            </svg>
                        </span>
                    </div>
                    <div class="results-search">
                        <h3 class="title">T<PERSON><PERSON> kiếm nhiều nhất</h3>
                        <div class="list-results" id="list-results">
                            <!-- Search results will be injected here -->
                        </div>
                    </div>
                    </div>
                </div>
            </div>

            <!-- Button open menu mobile -->
            <div class="btn-open-menu-mobile">
                <svg xmlns:xlink="http://www.w3.org/1999/xlink">
                    <use xlink:href="#3-dots" fill="currentColor"></use>
                </svg>
            </div>
            <a href={ templates.SafeURL("/") } title="Trang chủ" class="logo ">
                <div class="logos">          
                    <img src={ templates.AssetURL("/asset/images/common/msd-hpv-logo.webp") } alt="hpv">
                </div>
            </a>
            @mainMenu()
        </div>
    </header>
}