package mapping

import (
	"webgo/hpv/entity"
	"webgo/hpv/locations/transport/responses"
)

func MapperProvinceKeyValue(provinces []*entity.ProviceEntity) *[]responses.KeyValueResp {
	if provinces == nil {
		return nil
	}

	var datas []responses.KeyValueResp
	for _, provice := range provinces {
		provice.MaskFieldProvinceId()

		datas = append(datas, responses.KeyValueResp{
			ID:   provice.FakeId,
			Name: provice.Title,
		})
	}

	return &datas
}
