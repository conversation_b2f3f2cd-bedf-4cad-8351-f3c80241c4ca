package hpv

import (
	"webgo/hpv/blogs"
	"webgo/hpv/femalesv3"
	"webgo/hpv/locations"
	"webgo/hpv/locations/composers"
	"webgo/hpv/malesv3"
	"webgo/hpv/searchpost"

	"webgo/hpv/genvv3"
	"webgo/hpv/homev3"
	page404 "webgo/hpv/page-404"

	mohv3 "webgo/hpv/mohV3"
	posts "webgo/hpv/postsv3"
	"webgo/pkg/sctx"

	"github.com/gofiber/fiber/v2"
)

func SetupRoutesWebHpvV3(app *fiber.App, serviceCtx sctx.ServiceContext) {
	compHomev3 := homev3.ComposerHomeSVC(serviceCtx)
	compBlog := blogs.ComposerBlogsSVC(serviceCtx)
	compMohV3 := mohv3.ComposerMohSVC(serviceCtx)
	compGenvV3 := genvv3.ComposerGenvV3Service(serviceCtx)

	compSearchPost := searchpost.ComposerSearchPostService(serviceCtx)

	compFemaleV3 := femalesv3.ComposerFemaleV3Service(serviceCtx)
	compMaleV3 := malesv3.ComposerMaleV3Service(serviceCtx)

	compFemale918V3 := femalesv3.ComposerFemale918V3Service(serviceCtx)
	compFemale1926V3 := femalesv3.ComposerFemale1926V3Service(serviceCtx)
	compFemale2745V3 := femalesv3.ComposerFemale2745V3Service(serviceCtx)

	compMale918V3 := malesv3.ComposerMale918V3Service(serviceCtx)
	compMale1926V3 := malesv3.ComposerMale1926V3Service(serviceCtx)
	compMale2745V3 := malesv3.ComposerMale2745V3Service(serviceCtx)

	postComp := posts.ComposerPostService(serviceCtx)

	locations := locations.ComposerLocationsService(serviceCtx)

	page404Comp := page404.ComposerPage404Service(serviceCtx)

	apiDistrictComp := composers.ComposerApiDistrictService(serviceCtx)
	compLocationApi := composers.ComposerApiLocationsService(serviceCtx)
	compMoreLocationApi := composers.ComposerMoreLocationApiService(serviceCtx)

	app.Get("/", compHomev3.ListHomeHdl()).Name("hpv.homeV3")
	app.Get("/home-pagespeed", compHomev3.ListHomePageSpeedHdl()).Name("hpv.home_pagespeed")

	app.Get("/blogs", compBlog.ListBlogHdl()).Name("hpv.blogs")
	app.Get("/chien-dich-toan-quoc", compMohV3.ListMohHdl()).Name("hpv.mohV3")
	app.Get("/wearegenv", compGenvV3.PageGenvV3Hdl()).Name("hpv.genvv3")

	app.Get("/du-phong-hpv-cho-nu", compFemaleV3.PageFemaleV3Hdl()).Name("hpv.femalev3")
	app.Get("/du-phong-hpv-cho-nam", compMaleV3.PageMaleV3Hdl()).Name("hpv.malev3")

	app.Get("/du-phong-hpv-cho-nu-tu-9-18-tuoi", compFemale918V3.ListFemale918V3Hdl()).Name("hpv.femalev3")
	app.Get("/du-phong-hpv-cho-nu-tu-19-26-tuoi", compFemale1926V3.ListFemale1926V3Hdl()).Name("hpv.femalev3")
	app.Get("/du-phong-hpv-cho-nu-tu-27-45-tuoi", compFemale2745V3.ListFemale2745V3Hdl()).Name("hpv.femalev3")

	app.Get("/du-phong-hpv-cho-nam-tu-9-18-tuoi", compMale918V3.ListMale918V3Hdl()).Name("hpv.malev3")
	app.Get("/du-phong-hpv-cho-nam-tu-19-26-tuoi", compMale1926V3.ListMale1926V3Hdl()).Name("hpv.malev3")
	app.Get("/du-phong-hpv-cho-nam-tu-27-45-tuoi", compMale2745V3.ListMale2745V3Hdl()).Name("hpv.malev3")

	app.Get("/:slug<regex(^([a-z0-9-]+)-([0-9]+)$)>.html", postComp.DetailPostHdl())
	app.Get("/dia-diem-tu-van", locations.PageLocationsV3Hdl()).Name("hpv.locationsv3")

	app.Get("/page-404", page404Comp.Page404Hdl()).Name("hpv.page404")
	
	api := app.Group("apis/v3")
	{
		api.Post("/get-post", compSearchPost.GetPostApi()).Name("hpv.postlist")
		api.Post("/search-post", compSearchPost.ListSearchPostApi()).Name("hpv.api.searchpost")

		api.Post("/district", apiDistrictComp.WithProvinceDistrictApi()).Name("apiv3.getDistrict")
		api.Post("/search-locations", compLocationApi.SearchLocationApi()).Name("apiv3.getLocation")
		api.Post("/locations/more", compMoreLocationApi.MoreLocationApi()).Name("apiv3.moreLocation")
	}

	app.Use(func(c *fiber.Ctx) error {	
		return c.Redirect("/page-404", fiber.StatusTemporaryRedirect)
	})
}
