package femalesv3

import (
	"webgo/hpv/femalesv3/transport/handlers"
	"webgo/hpv/femalesv3/usecase"
	"webgo/hpv/repository"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/component/gormc"
	"webgo/pkg/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type composerFemale1926V3 interface {
	ListFemale1926V3Hdl() fiber.Handler
}

func ComposerFemale1926V3Service(serviceCtx sctx.ServiceContext) composerFemale1926V3 {
	db := serviceCtx.MustGet(configs.KeyCompGorm).(gormc.GormComponent).GetDB()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web")
	
	repoPost := repository.NewPostRepo(db)
	categoryRepo := repository.NewCategoryRepo(db)
	optionRepo := repository.NewOptionValueRepo(db)
	
	usc := usecase.NewFemale1926V3Usc(repoPost, logger, categoryRepo, optionRepo)
	hdl := handlers.NewFemale1926V3Hdl(usc)

	return hdl
}
