package cmd

import (
	"fmt"
	"os"
	"webgo/cmd/web"

	"github.com/spf13/cobra"
)

var rootCmd = &cobra.Command{
	Use:     "webgo",
	Version: "1.0.0",
	Short:   "Start go crawler service",
	Long:    `Start go crawler service`,
}

func init() {
	rootCmd.AddCommand(web.ServerCmd)
	rootCmd.AddCommand(web.OutEnvCmd)
}

func Execute() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}
