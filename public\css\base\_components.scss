.btn-primary-outline {
   display: inline-block;
   border: 1px solid var(--primary-500);
   color: var(--neutral-200);
   transition: all 0.25s ease-in-out;
   cursor: pointer;
   text-decoration: none;
   padding: 5px 20px;
   background-color: white;
   font-size: 15px;
   text-align: center;

   &:hover {
      color: white;
      background-color: var(--primary-500);
   }

}

.btn {
   &.full-radius {
      border-radius: 50px;
   }
}

.btn-primary-animation,
.btn-dark-primary-animation {
   transition: all .4s;
   position: relative;
   overflow: hidden;
   z-index: 1;

   &:before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 0%;
      height: calc(100% + 2px);
      background-color: var(--primary-500);
      transition: all .3s;
      border-radius: 10rem;
      z-index: -1;
      left: -2px;
   }

   &:hover {
      background-color: transparent;
      border-color: var(--primary-500);
      color: white;

      &:before {
         width: calc(100% + 4px);
      }
   }
}

.btn-dark-primary-animation {
   &:before {
      background-color: var(--primary-800);
   }
}

.btn-primary-animation.is-blue {
   &:before {
      background-color: var(--third-800);
   }
}

.btn-primary-animation.is-yellow {
   &:before {
      background-color: var(--secondary-800);
   }
}




/* HTML MSD-Loader: 
<div class="msd-loader-container">
<div class="msd-loader"></div>
<div class="msd-loader-text">Loading...</div>
</div> */

.msd-loader-container {
   margin: 5.5%;
   display: flex;
   flex-direction: column;
   align-items: center;
   gap: 2.2em;
   justify-content: center;
   font-size: 1rem;
}

.msd-loader-text {
   font-size: 2.2rem;
   color: var(--primary-500);
   font-weight: 400;
}

.msd-loader {
   width: 2.5em;
   height: 2.5em;
   position: relative;
   --c: no-repeat linear-gradient(#25b09b 0 0);
   background:
      var(--c) center/100% 10px,
      var(--c) center/10px 100%;

   &:before {
      content: '';
      position: absolute;
      inset: 0;
      background:
         var(--c) 0 0,
         var(--c) 100% 0,
         var(--c) 0 100%,
         var(--c) 100% 100%;
      background-size: 1.5em 1.5em;
      animation: msd-animation 1.5s infinite cubic-bezier(0.3, 1, 0, 1);
   }
}


@keyframes msd-animation {
   33% {
      inset: -10px;
      transform: rotate(0deg)
   }

   66% {
      inset: -10px;
      transform: rotate(90deg)
   }

   100% {
      inset: 0;
      transform: rotate(90deg)
   }
}