package transport

import "github.com/gofiber/fiber/v2"

type NewsPageUsc interface {}

type newsHdl struct {
	usc NewsPageUsc
}

func NewNewsPageHdl(usc NewsPageUsc) *newsHdl {
	return &newsHdl{
		usc: usc,
	}
}

/***
 * Page: News Page
 * Link: /tin-tuc-tong-hop
 * View: views/hpv/news_page/news.html
 * Components: true
 */

func (m *newsHdl) NewsPageHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		
		return c.Render("hpv/news_page/news", fiber.Map{
		}, "hpv/layouts/master")
	}
}

