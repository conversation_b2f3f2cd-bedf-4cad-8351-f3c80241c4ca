package locations

import (
	"webgo/hpv/locations/transport"
	"webgo/hpv/locations/usecase"
	"webgo/hpv/repository"
	provinceRepo "webgo/hpv/locations/repository"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/component/gormc"
	"webgo/pkg/sctx/configs"
	"github.com/gofiber/fiber/v2"
)

type composerLocations interface {
	PageLocationsHdl() fiber.Handler
	PageLocationsV3Hdl() fiber.Handler
}

func ComposerLocationsService(serviceCtx sctx.ServiceContext) composerLocations {
	db := serviceCtx.MustGet(configs.KeyCompGorm).(gormc.GormComponent).GetDB()
	log := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web")

	repoLocation := repository.NewLocationRepo(db)
	repoProvince := provinceRepo.NewProvinceRepo(db)
	repoOption := repository.NewOptionValueRepo(db)
	repoCategory := repository.NewCategoryRepo(db)

	usc := usecase.NewLocationsUsc(repoLocation, repoProvince, repoCategory, repoOption, log)

	hdl := transport.NewLocationsHdl(usc)
	return hdl
}
