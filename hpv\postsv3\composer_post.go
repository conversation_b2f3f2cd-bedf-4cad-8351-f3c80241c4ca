package posts

import (
	"webgo/hpv/postsv3/transport"
	"webgo/hpv/postsv3/usecase"
	"webgo/hpv/repository"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/component/gormc"
	"webgo/pkg/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type composerPost interface {
	DetailPostHdl() fiber.Handler
}

func ComposerPostService(serviceCtx sctx.ServiceContext) composerPost {
	db := serviceCtx.MustGet(configs.KeyCompGorm).(gormc.GormComponent).GetDB()
	repoPost := repository.NewPostRepo(db)
	log := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web")
	optionRepo := repository.NewOptionValueRepo(db)


	usc := usecase.NewDetailPostUsc(repoPost, optionRepo, log)
	hdl := transport.NewPostHdl(usc)

	return hdl
}
