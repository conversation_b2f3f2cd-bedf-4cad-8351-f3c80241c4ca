/*======================
    404 page
=======================*/
main {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80vh;
    width: 100vw;
    overflow: hidden;
}
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col-sm-12,
.col-sm-10 {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

/* Utility classes */
.text-center {
    text-align: center !important;
}

.h2 {
    font-size: 2rem;
    font-weight: 500;
    line-height: 1.2;
}

.page_404 {
    width: 100%;
    padding: 15svh 0;
    background: #ffffff;
    font-size: 1rem;
}

.page_404 img {
    width: 100%;
}

.four_zero_four_bg {
    background-image: url(https://cdn.dribbble.com/users/722246/screenshots/3066818/404-page.gif);
    height: 65em;
    background-position: center;
    background-repeat: no-repeat;
}

.four_zero_four_bg h1 {
    font-size: 8em;
}

.four_zero_four_bg h3 {
    font-size: 8em;
}

.link_404 {
    color: #ffffff !important;
    padding: 1em 2em;
    background: var(--primary-500);
    margin: 2em 0;
    display: inline-block;
    border-radius: 1.2em;
}

.contant_box_404 {
    margin-top: -4em;
}
/* Reponsive */
@media (min-width: 340px) and (max-width: 1099px) {
    #fast-action-controls {
        display: none;
    }
}
@media (min-width: 300px) {
    .root-container {
        justify-content: flex-start;
    }
    .four_zero_four_bg {
        height: 60svh;
        background-size: 85vw;
    }
}
@media (min-width: 576px) {
    .col-sm-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .col-sm-10 {
        flex: 0 0 83.333333%;
        max-width: 83.333333%;
    }

    .col-sm-offset-1 {
        margin-left: 8.333333%;
    }
    .four_zero_four_bg {
        height: 61svh;
        background-size: 50vw;
    }
}
@media (min-width: 1099px) {
    .four_zero_four_bg {
        height: 32vw;
        background-size: 45vw;
    }
}
@media (min-width: 1599px) {
    .page_404 {
        width: 100%;
        padding: 15svh 0;
        background: #ffffff;
        font-size: 1rem;
    }
    .four_zero_four_bg {
        height: 35vw;
        background-size: 45vw;
    }
}
