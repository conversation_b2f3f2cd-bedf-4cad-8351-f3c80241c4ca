.main-menu {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    font-size: 0.625vw;
    padding: 0.5em 3em;
    border-radius: 5em;
}

.main-menu .btn-open-menu-mobile {
    cursor: pointer;
    font-size: 2.2em;
    padding: 1em 1em;
    border-radius: var(--radius-sm);
    transition: all 250ms ease-in-out;
    color: var(--primary-800);
}

.main-menu .btn-open-menu-mobile:hover {
    color: white;
    background-color: var(--primary-800);
}

.main-menu .logos {
    display: flex;
    gap: 1em;
}

.main-menu .logos img {
    height: 4em;
}

.main-menu .nav-menu {
    justify-content: space-between;
    align-items: center;
    gap: 0.4em;
    display: flex;
}

.main-menu .nav-menu>ul {
    display: none;
    gap: 1em;
    width: 100%;
    justify-content: flex-end;
}

.main-menu .nav-menu__item {
    display: inline-block;
    position: relative;
}

.main-menu .nav-menu__item>a {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5em;
    text-transform: capitalize;
    text-decoration: none;
    padding: 0.6em 1.5em;
    border: 1px solid var(--primary-800);
    border-radius: 5em;
    font-size: 1.2em;
    transition: all 0.25s ease-in-out;
    color: var(--primary-800);
    font-weight: 400;
    text-align: center;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.main-menu .nav-menu__item a:before {
    content: '';
    position: absolute;
    bottom: 0;
    left: -2px;
    width: 0%;
    height: calc(100% + 2px);
    background-color: var(--primary-800);
    transition: all 0.3s;
    border-radius: 5em;
    z-index: -1;
}

.main-menu .nav-menu__item>a:hover {
    background-color: var(--primary-800);
    border-color: var(--primary-500);
    color: white;
}

.main-menu .nav-menu__item.active a {
    color: green;
}

.nav-menu__item img {
    width: 1.2em;
}

.main-menu .nav-menu .search-control {
    padding: 8px;
    cursor: pointer;
    border-radius: 50%;
    color: var(--primary-800);
    transition: all 0.25s ease-in-out;
}

.main-menu .nav-menu .search-control svg {
    font-size: 3em;
}

.main-menu .nav-menu .search-control:hover {
    background-color: var(--primary-800);
    color: white;
}

.main-menu .btn-open-menu-mobile {
    display: block;
}

/* Sub main menu */
.nav-menu__item .sub-menu {
    display: flex;
    position: absolute;
    flex-direction: column;
    width: fit-content;
    left: 50%;
    transform: translate(-50%, 8%);
    background: linear-gradient(130.73deg, rgba(255, 255, 255, 1) 35.03%, rgba(189, 255, 219, 1) 122.8%);
    box-shadow: rgba(0, 0, 0, 0.2) 0px 18px 50px -10px;
    border-image-source: linear-gradient(52.8deg, rgba(255, 255, 255, 0.88) 16.05%, #B3F3FD 82.51%);
    padding: 1.5em 1em;
    width: max-content;
    border-radius: 2.1em;
    visibility: hidden;
    opacity: 0;
    clip: rect(0px, 200vw, 0, 0px);
    transition: clip 0.6s linear, all 0.4s linear;
    list-style: none;
    gap: 0.6em;
}
.nav-menu__item:hover>.sub-menu {
    visibility: visible;
    opacity: 1;
    clip: rect(0px, 100vw, 200vh, -100vw);
    transition: clip 0.6s linear, opacity 0.4s linear;
}
.sub-menu__item>a {
    font-size: 1.2em;
    font-weight: 400;
    display: flex;
    align-items: center;
    transition: 0.3s;
    white-space: nowrap;
    font-family: Unbounded;
    font-weight: 400;
    line-height: 1.1;
    text-align: center;
    padding: 1em;
}
.sub-menu__item {
    color: #6A6A6A;
}
.sub-menu__item:hover {
    background: linear-gradient(269.45deg, #B3F3FD -10.77%, #05A8AE 122.08%);
    border-radius: 16px;
    backdrop-filter: blur(4px);
    color: #141414;
}
.is-female .sub-menu__item:hover {
    background: linear-gradient(269.45deg, rgba(255, 229, 15, 0.11) -10.77%, #FFE400 77.84%);
}


@media (min-width: 1300px) {
    .main-menu .nav-menu {
        display: flex;
    }

    .main-menu .nav-menu>ul {
        gap: 1em;
    }

    .main-menu .nav-menu__item.active a {
        color: green;
    }
}

@media (min-width: 992px) {
    .main-menu {
        background-color: white;
        justify-content: flex-start;
    }

    .main-menu .btn-open-menu-mobile {
        display: none;
    }

    .main-menu .nav-menu {
        flex-grow: 1;
    }

    .main-menu .nav-menu ul {
        flex-grow: 1;
        display: flex;
    }

    .main-menu .nav-menu__item {
        transition: all 250ms ease-in-out;
    }

    .main-menu .nav-menu__item.is-female>a:hover {
        color: var(--neutral-100);
        background-color: var(--secondary-500);
        border-color: var(--secondary-500);
    }
    .main-menu .nav-menu__item.is-female>a:hover img {
        filter: brightness(0) saturate(100%) invert(0%) sepia(71%) saturate(7500%) hue-rotate(171deg) brightness(109%) contrast(98%);
    }

    .main-menu .nav-menu__item.is-female a:hover:before {
        background-color: var(--secondary-500);
    }

    .main-menu .nav-menu__item.is-male>a:hover {
        color: var(--neutral-900);
        background-color: var(--third-500);
        border-color: var(--third-500);
    }

    .main-menu .nav-menu__item.is-male>a:hover img {
        filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(24%) hue-rotate(114deg) brightness(108%) contrast(108%);
    }

    .main-menu .nav-menu__item.is-male a:hover:before {
        background-color: var(--third-500);
    }

    .popup-search .container-search {
        width: 400px;
    }
}

@media (max-width: 575px) {
    .main-menu {
        font-size: 1em;
    }

    .main-menu.container {
        max-width: 100% !important;
    }

    .search-content .results-search {
        max-height: calc(100svh - 33em);
        font-size: 0.8rem;
    }

    .search-content .search-control {
        max-width: 100%;
    }

    .search-content {
        font-size: 0.7rem
    }
}

/* Modal Search */
.modal-search {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 102;
    top: calc(100% + 1em);
    transition: all 200ms ease-in-out;
    display: none;
    opacity: 0;
    height: 0;
    max-height: fit-content;
}

.modal-search.active {
    display: block !important;
    opacity: 1;
    height: 100vh;
    height: auto;
}

.modal-search__container {
    width: 100%;
    opacity: 1;
    transform: translate(0, 0);
    transition: opacity 0.4s linear;
}

.modal-search__overlay {
    display: none;
    position: fixed;
    z-index: -1;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: #00000086;
}

.modal-search__content {
    display: flex;
    flex-direction: column;
    gap: 1em;
    padding: 2em 2.5em;
    position: relative;
    font-size: 1.5em;
    margin: auto;
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: 0em 2em 5em rgba(51, 51, 51, 0.3);
    max-width: 98%;
}

.modal-search__control {
    margin-top: 3em;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1em;
    width: 100%;
    max-width: 900px;
    margin: auto;
}

.modal-search__input,
.modal-search__button {
    height: 3.2em;
}

.modal-search__input {
    font-size: 1.8em;
    padding: 8px 12px;
    border-radius: var(--radius-sm);
    border: 1.5px solid var(--primary-500);
    flex-grow: 1;
}

.modal-search__input:focus {
    outline: none;
}

.modal-search__button {
    font-size: 1.8em;
    padding: 0.5em 2em;
    background-color: var(--primary-500);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    text-transform: uppercase;
    line-height: 1;
    cursor: pointer;
    transition: background-color 200ms ease-in-out;
}

.modal-search__button:hover {
    background-color: var(--primary-700);
}

.modal-search__close {
    position: absolute;
    right: 1em;
    width: 2.5em;
    height: 2.5em;
    border-radius: 50%;
    color: var(--neutral-300);
    background-color: rgba(0, 0, 0, 0.1254901961);
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2em;
    cursor: pointer;
}

.modal-search__close:hover {
    background-color: var(--primary-100);
    color: var(--primary-700);
}

.modal-search__results {
    display: flex;
    flex-wrap: wrap;
    gap: 1em;
    margin-top: 2em;
    font-size: 1.2em;
    width: 100%;
    align-content: flex-start;
    max-height: calc(100svh - 33em);
    overflow-y: auto;
}

.modal-search__title {
    font-size: 2em;
    font-weight: 400;
    text-transform: uppercase;
    width: 100%;
    text-align: center;
}

.modal-search__list {
    display: flex;
    flex-wrap: wrap;
    gap: 2.5em;
    margin: 2em 0;
    width: 100%;
    justify-content: center;
    height: fit-content;
}

.modal-search__list-item {
    min-width: 15%;
    max-width: 15em;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    font-size: 1.5em;
    border-radius: var(--radius-sm);
    box-shadow: 0 0 2.5em rgba(0, 0, 0, 0.05);
    overflow: hidden;
    flex: 1;
    transition: all 0.25s linear;
    transform: scale(1);
}

.modal-search__list-item:hover {
    transform: scale(1.05);
    box-shadow: 0 1em 1.5em rgba(0, 0, 0, 0.2);
}

.modal-search__list-item__avatar {
    aspect-ratio: 3 / 2;
}

.modal-search__list-item__avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.modal-search__list-item__description {
    padding: 1em;
    color: white !important;
    background-color: var(--primary-500);
    font-size: 0.8em;
    font-weight: 400;
    position: relative;
    z-index: 1;
    flex-grow: 1;
}

.modal-search__list-item__description a {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.5em;
    max-height: 4.5em;
}

@media (max-width: 1099px) {
    .modal-search__content {
        max-width: 100%;
    }

    .modal-search {
        top: 0;
        z-index: 99999;
    }

    .modal-search__overlay {
        display: block;
        backdrop-filter: blur(6px);
    }

    .modal-search__control {
        margin: 1.2em auto;
    }

    .modal-search__content {
        border-radius: 0;
        height: calc(100svh - 12.5em);
        padding: 1em;
    }

    .modal-search__results {
        max-height: calc(100svh - 14em);
        overflow-y: auto;
    }

    .modal-search__input,
    .modal-search__button {
        font-size: 1.5em;
    }

    .modal-search__button {
        padding: 0.5em 1em;
    }

    .modal-search__close {
        position: static;
    }
}

@media (max-width: 768px) {
    .modal-search__list-item {
        min-width: 12em;
    }
}


/* Menu Mobile */
.popup-menu-mobile {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 45;
    transition: all 250ms ease-in-out;
}

.popup-menu-mobile .overlay {
    position: absolute;
    z-index: 0;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: #3333336a;
}

.popup-menu-mobile .container-menu-mobile {
    position: absolute;
    z-index: 2;
    left: 20px;
    top: 20px;
    max-width: 30em;
    background: linear-gradient(130.73deg, rgba(255, 255, 255, 0.8352) -15%, rgba(189, 255, 219, 0.96) 110%);
    width: fit-content;
    background-color: white;
    border-radius: var(--radius-md);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    color: var(--neutral-400);
}

.container-menu-mobile .logos {
    width: 100%;
    justify-content: center;
    display: flex;
    padding: 1.5em;
    background-color: var(--neutral-900);
}

.container-menu-mobile .logos img {
    height: 3.6em;
}

.container-menu-mobile .nav-menu {
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding: 1.5em 1em;
}

.container-menu-mobile .nav-menu__item {
    font-size: 15px;
    color: #6A6A6A;
    font-weight: 400;
    display: inline;
}

.container-menu-mobile .nav-menu__item .mobile-menu>a {
    padding: 10px 12px;
    width: fit-content;
    display: inline-block;
}

.container-menu-mobile .nav-menu__item:hover .mobile-menu {
    background: linear-gradient(269.45deg, rgba(32, 133, 233, 0.11) -10.77%, var(--primary-500) 77.84%);
    border-radius: 16px;
    backdrop-filter: blur(4px);
    color: var(--neutral-900);
}

.container-menu-mobile .nav-menu__item.is-male:hover .mobile-menu {
    background: linear-gradient(269.45deg, rgba(32, 133, 233, 0.11) -10.77%, var(--third-500) 77.84%);
    border-radius: 16px;
    backdrop-filter: blur(4px);
    color: var(--neutral-900);
}

.container-menu-mobile .nav-menu__item.is-female:hover .mobile-menu {
    background: linear-gradient(269.45deg, rgba(255, 229, 15, 0.11) -10.77%, var(--secondary-500) 77.84%);
    border-radius: 16px;
    backdrop-filter: blur(4px);
    color: var(--neutral-100);
}
.container-menu-mobile .nav-menu__item.is-female:hover .toggle-btn img {
    filter: brightness(0) saturate(100%) invert(0%) sepia(5%) saturate(0%) hue-rotate(33deg) brightness(92%) contrast(106%);
}

.container-menu-mobile .nav-menu__item.is-male:hover .toggle-btn img {
    filter: brightness(0) saturate(100%) invert(100%) sepia(7%) saturate(7416%) hue-rotate(310deg) brightness(106%) contrast(111%);
}

.container-menu-mobile .popup-menu-mobile__footer {
    display: block;
    padding: 1.5em 2em;
    position: relative;
}

.container-menu-mobile .popup-menu-mobile__footer:after {
    content: "";
    background: #6D6D6D8C;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    height: 1px;
    width: 50%;
}

.container-menu-mobile .popup-menu-mobile__footer .content-menu-footer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    gap: 1em;
}

.container-menu-mobile .popup-menu-mobile__footer .content-menu-footer span {
    line-height: 1.1;
}

.container-menu-mobile .popup-menu-mobile__footer .content-menu-footer .policy {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    line-height: 1.4;
    font-weight: 600;
}

.mobile-menu {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.toggle-btn{
    display: block;
    cursor: pointer;
    margin-right: 0.5em;
}
.toggle-btn img{
    filter: brightness(0) saturate(100%) invert(41%) sepia(5%) saturate(9%) hue-rotate(329deg) brightness(95%) contrast(90%);
    width: 0.9em;
}

.child-menu-mobile {
    list-style: none;
    padding-inline-start: 1em;
}

.child-menu-mobile li {
    font-size: 0.8em;
}

.child-menu-mobile li {
    padding: 0.5em 1.5em;
}

.child-menu-mobile {
    display: none; /* Hidden by default */
    list-style: none;
    margin: 0;
    padding: 0;
}

.child-menu-mobile.open {
    display: block; /* Show when toggled */
    transition: all 0.3s ease-in-out; /* Smooth dropdown effect */
}

/* Menu Footer Mobile */
.menu-footer-mobile {
    position: fixed;
    bottom: 0;
    width: 100%;
    background-color: white;
    padding: 8px 10px;
    z-index: 43;
 }
 
 .menu-footer-mobile .list-menu-items {
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: space-around;
 }
 
 .menu-footer-mobile .list-menu-items .menu-item {
    height: fit-content;
 }
 
 .menu-footer-mobile .list-menu-items .menu-item a {
    border-radius: 50px;
    padding: 5px;
    color: white;
    font-size: 18px;
    background-color: var(--forth-500);
    display: inline-block;
    vertical-align: middle;
    width: 2.4em;
    aspect-ratio: 1;
    display: flex;
    justify-content: center;
    align-items: center;
 }
 #icon-lich{
    font-size: 30px;
 }
 #icon-chat{
    font-size: 32px;
 }
 
 .menu-footer-mobile .list-menu-items .menu-item a.active {
    background-color: var(--primary-800);
 }
 
 @media (min-width: 992px) {
    .menu-footer-mobile {
       display: none;
    }
 }
 /* Modal search */
 .modal-search#modal-search {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 102;
    top: calc(100% + 1em);
    transition: all 200ms ease-in-out;
    display: none;
    opacity: 0;
    height: 0;
    max-height: fit-content;
}

.modal-search#modal-search.active {
    display: block !important;
    opacity: 1;
    height: 100vh;
    height: auto; /* Changed calc-size(auto) which is invalid CSS */
}

.modal-search#modal-search .search-container {
    width: 100%;
    opacity: 1;
    translate: 0 0;
    transition-property: opacity;
    transition-duration: .4s;
    transition-timing-function: linear; /* Removed -o- prefix */
}

.modal-search#modal-search .search-container[style*="opacity: 0"] { /* Starting style equivalent */
    opacity: 0;
    translate: 0 25vh;
}

.modal-search#modal-search .overlay {
    display: none;
    position: fixed;
    z-index: -1;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: #00000086;
}

.search-content {
    display: flex;
    flex-direction: column;
    gap: 1em;
    padding: 2em 2.5em;
    position: relative;
    font-size: 0.652vw;
    margin: auto;
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: 0em 2em 5em rgba(51, 51, 51, 0.3);
    max-width: 98%;
}

.search-content .search-control {
    margin-top: 3em;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1em;
    width: 100%;
    max-width: 60%;
    margin: auto;
}

.search-content .search-control input,
.search-content .search-control button {
    height: 3.2em;
}

.search-content .search-control input {
    font-size: 1.8em;
    padding: 0.6em 0.9em;
    border-radius: var(--radius-sm);
    border: 1.5px solid var(--primary-500);
    flex-grow: 1;
}

.search-content .search-control input:focus {
    outline: none;
}

.search-content .search-control button {
    font-size: 1.8em;
    padding: 0.5em 2em;
    background-color: var(--primary-500);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    text-transform: uppercase;
    line-height: 1;
    cursor: pointer;
    transition: background-color 200ms ease-in-out;
}

.search-content .search-control button:hover {
    background-color: var(--primary-700);
}

.search-content .search-control span.close-modal {
    position: absolute;
    right: 1em;
    width: 2.5em;
    height: 2.5em;
    border-radius: 50%;
    color: var(--neutral-300);
    background-color: rgba(0, 0, 0, 0.1254901961);
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2em;
    cursor: pointer;
}

.search-content .search-control span.close-modal:hover {
    background-color: var(--primary-100);
    color: var(--primary-700);
}

.search-content .results-search {
    display: flex;
    flex-wrap: wrap;
    gap: 1em;
    margin-top: 2em;
    font-size: 0.652vw;
    width: 100%;
    align-content: flex-start;
    max-height: calc(100svh - 33em);
    overflow-y: auto;
}

.search-content .results-search .title {
    font-size: 2em;
    font-weight: 400;
    text-transform: uppercase;
    width: 100%;
    text-align: center;
}

.list-results {
    display: flex;
    flex-wrap: wrap;
    gap: 2.5em;
    margin: 2em 0;
    width: 100%;
    justify-content: center;
    gap: 2.2em;
    width: 100%;
    height: fit-content;
}

.list-results .result-item {
    min-width: 15%;
    max-width: 15em;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    font-size: 1.5em;
    border-radius: var(--radius-sm);
    box-shadow: 0 0 2.5em rgba(0, 0, 0, 0.05);
    overflow: hidden;
    flex: 1;
    transition: all 0.25s linear;
    transform: scale(1);
}

.list-results .result-item__avatar {
    aspect-ratio: 3 / 2;
}

.list-results .result-item__avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.list-results .result-item__des {
    padding: 1em;
    color: white !important;
    background-color: var(--primary-500);
    font-size: 0.8em;
    font-weight: 400;
    position: relative;
    z-index: 1;
    flex-grow: 1;
}

.list-results .result-item__des a {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.5em;
    max-height: 4.5em;
}

.list-results .result-item:hover {
    transform: scale(1.05);
    box-shadow: 0 1em 1.5em rgba(0, 0, 0, 0.2);
}

.search-control.is-loading {
    cursor: not-allowed;
    opacity: 0.5;
}
@media (max-width: 1099px) {
    .search-content {
        max-width: 100%;
    }

    .modal-search#modal-search {
        top: 0;
        z-index: 99999;
    }

    .modal-search#modal-search .overlay {
        display: block;
        backdrop-filter: blur(6px);
    }

    .modal-search#modal-search .search-control {
        margin: 1.2em auto;
    }

    .modal-search#modal-search .search-content {
        border-radius: 0;
        height: calc(100svh - 12.5em);
        padding: 1em;
    }

    .modal-search#modal-search .search-content .results-search {
        max-height: calc(100svh - 14em);
        overflow-y: auto;
    }

    .modal-search#modal-search .search-content input,
    .modal-search#modal-search .search-content button {
        font-size: 1.5em;
    }

    .modal-search#modal-search .search-content button {
        padding: 0.5em 1em;
    }

    .modal-search#modal-search .search-content span.close-modal {
        position: static;
    }
}

@media (max-width: 768px) {
    .modal-search#modal-search .search-content .results-search .list-results .result-item {
        min-width: 12em;
    }
}