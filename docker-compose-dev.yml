services:
  # Mysql
  mysql:
    image: mysql:8
    container_name: msd-mysql-container
    ports:
      - $MYSQL_PORT:3306
    volumes:
      - ./docker/mysql/config:/etc/mysql/conf.d
      - ./docker/mysql/database:/var/lib/mysql
    restart: always
    environment:
      MYSQL_DATABASE: $MYSQL_DATABASE
      MYSQL_ROOT_PASSWORD: $MYSQL_PASS
      MYSQL_PASSWORD: $MYSQL_PASS
    networks:
      - msd-hpv-network
networks:
  msd-hpv-network:
