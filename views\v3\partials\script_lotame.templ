package partials

templ ScriptLotame() {
    // Resource hints for performance optimization
    <link rel="preconnect" href="https://tags.crwdcntrl.net" crossorigin="anonymous"/> 
    <link rel="preconnect" href="https://bcp.crwdcntrl.net" crossorigin="anonymous"/> 
    <link rel="preconnect" href="https://c.ltmsphrcl.net" crossorigin="anonymous"/> 
    
    // Initialize Lotame configuration
    <script defer>
        (function() { 
            const lotameClientId = '17974';
            const lotameTagInput = { 
                data: {}, 
                config: { 
                    clientId: parseInt(lotameClientId, 10)
                } 
            }; 

            const lotameConfig = lotameTagInput.config;
            const namespace = window['lotame_' + lotameConfig.clientId] = {
                config: lotameConfig,
                data: lotameTagInput.data || {},
                cmd: []
            };
        })(); 
    </script> 
    
    // Load Lotame script
    <script defer src="https://tags.crwdcntrl.net/lt/c/17974/lt.min.js"></script>
}