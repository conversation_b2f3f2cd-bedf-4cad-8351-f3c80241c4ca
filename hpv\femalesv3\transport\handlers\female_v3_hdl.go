package handlers

import (
	"context"
	"errors"
	"webgo/hpv/femalesv3/transport/responses"
	"webgo/pkg/gos/templates"
	"webgo/pkg/sctx/core"
	"webgo/views/v3/females"

	"github.com/gofiber/fiber/v2"
)

type FemaleV3Usc interface {
	GetFemaleV3Usc(ctx context.Context) (*responses.FemaleV3Resp, error)
}

type femaleV3Hdl struct {
	usc FemaleV3Usc
}

func NewFemaleV3Hdl(usc FemaleV3Usc) *femaleV3Hdl {
	return &femaleV3Hdl{
		usc: usc,
	}
}

/***
 * Page: Home Female V3
 * Link: /du-phong-hpv-cho-nu-v3
 * View: views/v3/female.templ
 * Components: true
 */

func (h *femaleV3Hdl) PageFemaleV3Hdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		page, err := h.usc.GetFemaleV3Usc(c.Context())
		if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
			return c.Redirect("/page-404")
		}
			
		return templates.Render(c, females.FemaleIndex(page))
	}
}
