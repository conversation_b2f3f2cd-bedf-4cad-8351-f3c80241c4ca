.container-sidebar,
.container-sidebar-mobile {
  display: flex;
  flex-direction: column;
  gap: 1em;
}
.container-sidebar .sidebar-title,
.container-sidebar-mobile .sidebar-title {
  background-color: var(--primary-500);
  border-radius: var(--radius-sm);
  font-weight: 700;
  font-size: 15px;
  color: white;
  padding: 8px 10px;
  text-align: center;
}

.list-card {
  display: flex;
  flex-direction: column;
  gap: 1em;
}
.list-card .card-item {
  font-size: 1rem;
  display: flex;
  flex-direction: row;
  gap: 1.5em;
  background-color: white;
  border-radius: var(--radius-md);
  flex: auto;
  overflow: hidden;
}
.list-card .card-item .avatar {
  overflow: hidden;
  flex: 0;
  min-width: 14em;
}
.list-card .card-item .avatar img {
  transition: all 0.3s ease-in-out;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.list-card .card-item .info {
  display: flex;
  flex-direction: column;
  gap: 0.7em;
  text-align: left;
  flex: 1;
  line-height: 1.2;
  font-size: 1.5em;
  justify-content: center;
  padding: 1em 1em 1em 0em;
}
.list-card .card-item .info .title {
  font-size: 18px;
  font-weight: 700;
  text-transform: uppercase;
  color: var(--primary-500);
  letter-spacing: -0.05em;
  display: block;
}
.list-card .card-item .info .description {
  flex-grow: 1;
}
.list-card .card-item .info .actions {
  display: flex;
  flex-direction: row;
  gap: 0.8em;
}
.list-card .card-item .info .actions a {
  font-size: 1.4em;
  flex: 1;
  line-height: 1.2;
}
.list-card .card-item:hover .avatar img {
  transform: scale(1.1) rotateZ(2deg);
}

.container-sidebar-mobile {
  background-color: transparent;
  position: static;
}
.container-sidebar-mobile .copyright {
  font-size: 0.58em;
  color: var(--neutral-300);
  margin-top: 15px;
  display: block;
  text-align: center;
  padding: 0 10px;
}
.container-sidebar-mobile .sidebar-title {
  border-radius: 0;
}
.container-sidebar-mobile .list-card {
  padding: 8px;
  display: flex;
  flex-direction: row;
  gap: 1.5em;
  flex-wrap: wrap;
}
.container-sidebar-mobile .list-card .card-item {
  border-radius: var(--radius-md);
  border: 1px solid var(--neutral-500);
  padding: 0;
  gap: 0;
  flex: 1;
  min-width: 31rem;
  font-size: 1rem;
}
.container-sidebar-mobile .list-card .card-item .avatar {
  border-radius: 0;
  border: none;
  min-width: 10em;
}
.container-sidebar-mobile .list-card .card-item .info {
  text-align: center;
  align-items: center;
  justify-content: center;
  font-size: 1em;
  padding: 1em;
  padding-left: 0;
  letter-spacing: -0.5px;
}
.container-sidebar-mobile .list-card .card-item .info .title {
  font-size: 1.1em;
}
.container-sidebar-mobile .list-card .card-item .info .subtitle {
  margin-right: -15px;
  margin-left: -2.5em;
  font-size: 1em;
  letter-spacing: -0.3px;
}
.container-sidebar-mobile .list-card .card-item .info .btn {
  font-size: 1.2em;
}

.sidebar .container-sidebar .sidebar-title,
.container-sidebar-mobile .sidebar-title {
  background-color: var(--third-500);
}
.sidebar .container-sidebar .list-card .card-item .info .title,
.container-sidebar-mobile .list-card .card-item .info .title {
  color: var(--third-500);
}
.sidebar .container-sidebar .list-card .card-item .actions a,
.container-sidebar-mobile .list-card .card-item .actions a {
  border-color: var(--third-500);
}

.sidebar.is-yellow .container-sidebar .sidebar-title,
.container-sidebar-mobile .sidebar-title {
  background-color: var(--secondary-500);
  color: var(--primary-500);
}
.sidebar.is-yellow .container-sidebar .list-card .card-item .info .title,
.container-sidebar-mobile .list-card .card-item .info .title {
  color: var(--primary-500);
}
.sidebar.is-yellow .container-sidebar .list-card .card-item .actions a,
.container-sidebar-mobile .list-card .card-item .actions a {
  border-color: var(--primary-500);
}
.sidebar.is-yellow .container-sidebar .list-card .card-item .actions a::before,
.container-sidebar-mobile .list-card .card-item .actions a::before {
  background-color: var(--primary-500);
}

@media (min-width: 1100px) {
  .list-card .card-item {
    font-size: 1rem;
  }
  .list-card .card-item .avatar {
    min-width: 12.5em;
  }
  .list-card .card-item .info {
    text-align: center;
    font-size: 1.3em;
  }
  .list-card .card-item .info .title {
    font-size: 1.15em;
  }
  .list-card .card-item .info .subtitle {
    display: none;
  }
  .list-card .card-item .info .actions a {
    font-size: 1.4rem;
    flex: 1;
    line-height: 1.2;
  }
}/*# sourceMappingURL=sidebar-child-page.css.map */