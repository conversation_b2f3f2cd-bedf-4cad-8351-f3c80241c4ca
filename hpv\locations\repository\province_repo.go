package repository

import (
	"context"
	"webgo/hpv/entity"
	"webgo/pkg/gos/generics"
	"webgo/pkg/gos/utils"

	"gorm.io/gorm"
)

type provinceRepo struct {
	db *gorm.DB
}

func NewProvinceRepo(db *gorm.DB) *provinceRepo {
	return &provinceRepo{db: db}
}

/**
 * find location repo
 */
func (r *provinceRepo) FindProvinceRepo(ctx context.Context, filter *utils.Filters) ([]*entity.ProviceEntity, error) {
	tableName := entity.ProviceEntity{}.TableName()
	return generics.FindGeneric[entity.ProviceEntity](ctx, r.db, tableName, filter)
}
