package females

import "webgo/pkg/gos/templates"
import "webgo/views/v3/layouts"
import "webgo/hpv/femalesv3/transport/responses"
import "webgo/views/v3/females/components/female2745hariwon"


templ Female2745Hariwon(datas *responses.Female2745V3Resp) {
    @layouts.Master(datas.Seo,[]templ.Component{head2745test()}, FooterFemaleHariwon2745(datas),female2745TestJS()) {
        <main class="container container-content">
            <div class="main-container" id="main-container">
               <section class="female-banner-main wow animate__pulse" data-wow-duration="1.5s" data-wow-delay="0s">
                  <div class="content-banner">
                        <span id="find-consultant-location-banner" class="btn-cta" alt="dia-diem-tu-van">THAM VẤN VỚI CHUYÊN GIA Y TẾ</span>
                  </div>
                  <div class="img-banner">
                        <div class="img-banner__thumbnail">
                           <div class="img-banner-background">
                              <img src={templates.AssetURL("/asset/images/female-27-45-hariwon/hariwon-banner-mobile.webp")}
                                    srcset={templates.AssetURL("/assetimages/female-27-45-hariwon/hariwon-banner-mobile.webp 480w, /asset/images/female-27-45-hariwon/hariwon-banner-desktop.webp")}
                                    sizes="(max-width: 480px) 480px, 1024px" alt="Dự phòng HPV cho Nữ từ 27-45 tuổi" />
                           </div>
                        </div>
                  </div>
               </section> 
               @female2745hariwon.HariwonVideoEmbed()
               @female2745hariwon.HariwonTripleBanner()

               @female2745hariwon.Female2745ContentResult()
               @female2745hariwon.Female2745ContentFact()
               @female2745hariwon.Female2745ContentProtection()

                if datas != nil && len(datas.PostNew) > 0{
                    @female2745hariwon.Female2745ContentArticle(datas.PostNew)                
                }
               <div class="footer-main-content-mobile">
        <div class="footer-main-content-male-page mobile" id="footer-main-content">
            <div class="container content wow animate__fadeInLeft ">
                <div class="container-footer">
                    <div class="content-footer">
                        <h5 style="font-size: 1.5em; margin-bottom: 0.5em;">Tài liệu tham khảo:
                        </h5>
                        <div class="paragraph-2-col">
                           <ul>
                              <li>
                              (1): Bruni L, Albero G, Serrano B, et al. ICO/IARC Information Centre on HPV and Cancer (HPV Information Centre). Human Papillomavirus and Related Diseases in the World. Summary Report 10 March 2023.
                              </li>
                              <li>
                              (2): World Health Organization (WHO). 2020. Questions and Answers About Human Papillomavirus (HPV)
                              </li>
                              <li>
                              (3): World Health Organization (WHO). Cervical Cancer
                              </li>

                              <li>
                              (4): Chesson HW, Dunne EF, Hariri S, Markowitz LE. The estimated lifetime probability of acquiring human papillomavirus in the United States. Sex Transm Dis. 2014 Nov;41(11):660-4
                              </li>
                              <li>
                              (5): World Health Organization (WHO). 2024. Questions and answers about human papillomavirus (HPV)
                              </li>
                              <li>
                              (6): Hướng dẫn dự phòng và kiểm soát ung thư cổ tử cung. Ban hành kèm theo Quyết định số 3792/QĐ-BYT ngày 17 tháng 12 năm 2024.
                              </li>
                              <li>
                              (7): Centers for Disease Control and Prevention (CDC). How Many Cancers Are Linked with HPV Each Year.
                              </li>
                              <li>
                              (8): WHO. Cervical Cancer. Updated Mar 2024. https://www.who.int/news-room/fact-sheets/detail/cervical-cancer
                              </li>
                              <li>
                              (9): International Agency for Research on Cancer (IARC), Global Cancer Observatory in Vietnam, Jun 2025
                              </li>
                              <li>
                              (10): CDC. HPV Vaccine Information For Young Women. Updated Apr, 2022. https://www.cdc.gov/std/hpv/stdfact-hpv-vaccine-young-women.htm
                              </li>
                              <li>
                              (11): WHO Regional Office for Europe. Questions and answers about human papillomavirus (HPV). 2024. Available at: https://iris.who.int/handle/10665/376263 . Accessed 21 Feb 2025
                              </li>
                           </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer__content">
            <h3 class="container">Nội dung này được phối hợp biên soạn bởi Hội Y học Dự phòng Việt Nam và MSD, kiểm nhận bởi Hội Y học Dự phòng Việt Nam và MSD tài trợ cho mục tiêu giáo dục.<span class="code-footer">VN-GSL-02785</span></h3>
            <div class="direction">
                <a href="https://www.msd.com/our-commitment-to-accessibility-for-all/">Chính sách về Khả Năng Tiếp Cận </a>
                | <a href="https://www.msdprivacy.com/vt/vt/">Điều khoản riêng tư</a>
                |
                <a href="https://www.essentialaccessibility.com/msd">
                    <img src={templates.AssetURL("/asset/images/common/acc-logo.png")} width="80" height="30">
                </a>
            </div>
        </div>
               </div>
            </div>
        </main>
        @female2745hariwon.Female2745ContentFooter()

    }
}

templ head2745test() {
   <link rel="stylesheet" href={templates.AssetURL("/asset/js/lib/swiper/swiper-bundle.min.css")}>
   <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/female-27-45-hariwon/female-27-45-hariwon.css")}>
}

templ female2745TestJS() {
   <script type="text/javascript" src={templates.AssetURL("/asset/js/lib/swiper/swiper-bundle.min.js")}></script>
   <script type="text/javascript" src={templates.AssetURL("/asset/js/pages/male-19-26.js")}></script>

    <script type="text/javascript">
      (() => {
         const scriptElement = document.createElement('script');
         scriptElement.id = "js-load-lib-swiper"
         document.body.appendChild(scriptElement);
      })()
   </script>
  
    <script>
      document.addEventListener("DOMContentLoaded", function () {
         const lazyImages = document.querySelectorAll("img.lazyload");

         const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach((entry) => {
               if (entry.isIntersecting) {
                  const img = entry.target;
                  img.src = img.dataset.src;
                  if (img.dataset.srcset) {
                     img.srcset = img.dataset.srcset;
                  }
                  img.classList.remove("lazyload");
                  observer.unobserve(img);
               }
            });
         });

         lazyImages.forEach((img) => {
            imageObserver.observe(img);
         });
      });
      document.querySelectorAll(".open-popup-locations").forEach(button => {
      button.addEventListener("click", () => {
         document.querySelector("#btn-location-round-id").click()
      });
      });
   </script>
   <script>
         document.addEventListener('DOMContentLoaded', function () {
         const contentContainers = document.querySelectorAll('.content-footer');
         
         contentContainers.forEach((content, index) => {
            const showMoreButton = document.createElement('div');
            showMoreButton.classList.add('show-more');
            showMoreButton.innerHTML = 'Xem thêm &#9660';
            content.parentNode.insertBefore(showMoreButton, content.nextSibling);
   
            showMoreButton.addEventListener('click', function () {
               if (content.style.maxHeight) {
               content.style.maxHeight = null;
               showMoreButton.innerHTML = 'Xem thêm &#9660'; 
               } else {
               content.style.maxHeight = content.scrollHeight + 'px';
               showMoreButton.innerHTML = 'Ẩn &#9650'; 
               }
            });
         });
         });
   </script>
}

func FooterFemaleHariwon2745(datas *responses.Female2745V3Resp) templ.Component{
    if datas != nil && datas.Options != nil {
        if banner, ok := datas.Options["footer-female-2745"]; ok {
            return templ.Raw(banner)
        }
    }    
        return nil
}