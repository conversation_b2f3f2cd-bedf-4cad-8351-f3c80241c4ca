package partials

import "webgo/hpv/entity"
import "webgo/pkg/gos/templates"

templ Seo(seo *entity.Seo) {      
    if seo != nil{
        <title>{seo.Title}</title>      
        <meta property="og:type" content="website" />
        <meta property="og:title" content={seo.Title}>
        if seo.Description != nil{
            <meta name="description" content={*seo.Description} />
            <meta property='og:description' content={*seo.Description}>
        }    
        if seo.Img != "" {
            <meta property="og:image" itemprop="thumbnailUrl" content={templates.ImgURL("/thumb_1200x600" + seo.Img)}>  
        }   
    }else{
        <title>Cộng Đồng Phòng Vệ HPV & các gánh nặng bệnh tật, nguy cơ ung thư liên quan</title> 
        <meta property="og:type" content="website" />
        <meta property="og:title" content="Cộng Đồng Phòng Vệ HPV & các gánh nặng b<PERSON><PERSON> tật, nguy cơ ung thư liên quan">
        <meta property='og:image' content='/featured.jpg'>
        <meta property='og:description' content="Cộng Đồng Phòng Vệ HPV & các gánh nặng bệnh tật, nguy cơ ung thư liên quan">
        <meta name="description" content="Cộng Đồng Phòng Vệ HPV & các gánh nặng bệnh tật, nguy cơ ung thư liên quan">
    }

}