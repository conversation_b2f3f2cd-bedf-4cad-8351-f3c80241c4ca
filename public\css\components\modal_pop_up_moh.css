.modal-pop-up-moh .overlay {
    position: absolute;
    z-index: 0;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(51, 51, 51, 0.5);
    opacity: 0;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
}

.modal-pop-up-moh {
    display: flex;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 99;
    transition: all 300ms ease-in-out;
    /* font-size: 1.2rem; */
    position: fixed;
    opacity: 0;
    display: none;
}

.popup-moh {
    /* transform: scale(0.7); */
    opacity: 0;
    transition: all 300ms ease-in-out;
    border-radius: 2em;
    border: 2px solid rgb(32, 98, 138);
    overflow: hidden;
    /* flex-direction: column; */
    display: flex;
    text-align: center;
    /* position: fixed; */
    /* right: 0; */
    /* bottom: 0; */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    /* margin: auto; */
    z-index: 99;
    width: 45vw;
    height: auto;
    font-size: 1em;
    box-shadow: 1em 0 4em #33300030;
    aspect-ratio: 630 / 428;
    position: relative;
}

.modal-pop-up-moh.active {
    display: block;
    opacity: 1;
}

.modal-pop-up-moh.active .popup-moh {
    /* transform: scale(1); */
    opacity: 1;
}

.modal-pop-up-moh.active .overlay {
    opacity: 1;
}

.bg-moh-pop-up {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.bg-moh-pop-up img {
    width: 100%;
    object-fit: cover;
}

.popup-moh .background-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
}

.popup-moh__top {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: 100%;
}

.header-title-moh {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;
    /* font-size: 2.3em; */
}

.title-moh .title__main {
    font-weight: 800;
    font-size: 1.1vw;
    line-height: 120%;
    text-align: center;   
}

.title-moh .title__sub {
    font-size: 0.7em;
    font-weight: 500;
    margin-bottom: 0.3em;
}

.popup-moh .logo-byt {
    /* width: 6.4em;
    height: 6.4em; */
    width: 20%;
    aspect-ratio: 1;
    margin: 5% 0;
}

.btn-close-modal-moh {
    position: absolute;
    top: 0.5em;
    right: 0.5em;
    width: 1.5em;
    height: 1.5em;
    border-radius: 50%;
    background-color: transparent;
    text-align: center;
    line-height: 1.3em;
    font-weight: 400;
    cursor: pointer;
    font-size: 3em;
    color: white;
    transition: all 250ms ease-in-out;
    border: 3px solid;
    z-index: 10;
}

.btn-close-modal-moh:hover {
    color: white;
    border-color: #009EA4;
    background-color: #009EA4;
}

.center-title-moh {
    width: 70%;
    margin-top: -2%;
}

.around-line {
    position: relative;
    width: 100%;
}

.title__center {
    font-family: 'Unbounded', sans-serif;
    /* Font chữ Unbounded (hoặc bạn có thể chọn font khác) */
    font-weight: 700;
    font-size: 2.5em;
    line-height: 50px;
    letter-spacing: 0.5px;
    text-align: center;
    color: #f9f9f9;
    text-transform: uppercase;
    text-shadow:
        4px 8px 0px rgba(0, 158, 164, 1),
        1px 1px 0px rgba(0, 158, 164, 1);
    z-index: 5;
}

.bottom-section-moh {
    position: absolute;
    width: 100%;
    bottom: 0;
    left: 0;
}

.btn-cta-moh {
    position: absolute;
    background: rgb(254 254 254 / 70%);
    color: #009EA4;
    font-size: 1.1vw;
    bottom: 14%;
    transition: background 0.3s ease;
    border-radius: 3em;
    border: 3px solid #009EA4;
    cursor: pointer;
    min-width: 19.3em;
    padding: 0.8em;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1;
    left: 50%;
    transform: translateX(-50%);
}

.btn-cta-moh:hover {
    background: linear-gradient(89.1deg, #1EA390 0.68%, #5CDCCB 101.21%);
    color: #ffffff;
    border: 0px solid transparent;
    padding: calc( 0.8em + 3px);
}

.btn-cta-moh:hover .arrow-vector {
    filter: brightness(0) saturate(100%) invert(100%) sepia(17%) saturate(7488%) hue-rotate(291deg) brightness(124%) contrast(104%);
}
.btn-tick-prevent.active .arrow-vector {
    filter: brightness(0) saturate(100%) invert(100%) sepia(17%) saturate(7488%) hue-rotate(291deg) brightness(124%) contrast(104%);
}
.btn-tick-prevent:hover .arrow-vector {
    filter: brightness(0) saturate(100%) invert(100%) sepia(17%) saturate(7488%) hue-rotate(291deg) brightness(124%) contrast(104%);
}

.cta-text {
    text-decoration: none;
    color: inherit;
    font-weight: 700;
    text-transform: uppercase;
    text-decoration: none;
    transition: filter 0.3s ease;
}

.arrow-vector {
    color: inherit;
    width: 0.75em;
}

.bg-bottom-moh {
    width: 100%;
    object-fit: cover;
    position: absolute;
    bottom: 0;
}
.bottom-disc-moh {
    position: absolute;
    bottom: 3%;
    display: flex;
    text-align: left;
    width: 81%;
    left: 50%;
    transform: translateX(-50%);
    gap: 1vw;
}
.bottom-disc-moh__left {
    font-weight: 700;
    font-size: 0.75vw;
    text-align: center;
    white-space: nowrap;
    color: #009EA4;
    cursor: pointer;
}
.bottom-disc-moh__right {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-weight: 400;
    font-size: 0.35vw;
    color: #707070;
}

/* Effect button */
#pop-up-moh .group-tick {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1em;
    cursor: pointer;
    bottom: 3.6vw;
}

#pop-up-moh .btn-tick-prevent {
    position: relative;
    display: flex;
    gap: 1em;
    font-size: 1.1vw;
    left: -1em;
    width: fit-content;
    transition: background 250ms ease-in-out;
    text-transform: uppercase;
    background-color: #ffffffb5;
    color: var(--primary-500);
    padding: 0.6em 1.8em;
    border: 2px solid var(--primary-500);
    border-radius: 2.5em;
    font-weight: 500;
    line-height: 1.1;
    text-align: center;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
    overflow: hidden;
    animation: scaleAnimation 1.5s infinite ease-in-out;
    animation-delay: 1s; /* Initial delay */
}
#pop-up-moh .btn-tick-prevent:hover {
    background: linear-gradient(89.1deg, #1EA390 0.68%, #5CDCCB 101.21%);
    color: #ffffff;
    border: 0px solid transparent;
    padding: calc(0.6em + 2px) calc(1.8em + 2px);
}
@keyframes scaleAnimation {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.03);
    }
    75% {
        transform: scale(1.03);
    }
}
#pop-up-moh .btn-tick-prevent::before {
    content: "";
    position: absolute;
    width: 100px;
    height: 100%;
    background-image: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 30%,
      rgba(255, 255, 255, 0.9),
      rgba(255, 255, 255, 0) 70%
    );
    top: 0;
    left: -100px;
    opacity: 0.6;
    animation: shine 2s ease-out infinite;
}
@keyframes shine {
    0% {
      left: -100px;
    }
  
    60% {
      left: 100%;
    }
  
    to {
      left: 100%;
    }
  }
  

#pop-up-moh .btn-tick-prevent:hover {
    background-color: var(--primary-500);
    color: var(--neutral-900);
}

#pop-up-moh .btn-tick-prevent.active {
    color: var(--neutral-900);
    animation: none;
    transform: scale(1.03);
    background: linear-gradient(89.1deg, #1EA390 0.68%, #5CDCCB 101.21%);
    color: #ffffff;
    border: 0px solid transparent;
    padding: calc(0.6em + 2px) calc(1.8em + 2px);
}

#pop-up-moh .btn-tick-prevent span {
    font-weight: 700;
    font-size: 1em;
}

#pop-up-moh .title-moh-banner {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: var(--neutral-100);
    gap: 0.8em;
}

#pop-up-moh .title-moh-banner .logo-byt-moh {
    width: 4.8em;
    aspect-ratio: 1;
}

#pop-up-moh .title-moh-banner__sub {
    font-weight: 700;
    font-size: 1.6em;
    line-height: 1.3;
    margin-top: 0.3em;
    text-transform: uppercase;
    font-family: Unbounded;
}
#pop-up-moh .tagline-moh-banner {
    width: 45.1em;
    aspect-ratio: 451 / 91;
}
#pop-up-moh .tagline-moh-banner img{
    width: 100%;
    object-fit: cover;
}

#pop-up-moh .title-moh-banner__main {
    font-weight: 700;
    font-size: 2.8em;
    line-height: 1.1;
    padding: 0.3em 0.8em;
    background-color: var(--secondary-500);
    border-radius: 2em;
    text-transform: uppercase;
}

#pop-up-moh .tick-border {
    width: 4.5vw;
    min-width: 6.5em;
    aspect-ratio: 181 / 179;
    position: relative;
    left: -5%;
}

#pop-up-moh .tick-border img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

#pop-up-moh .line-tick {
    position: absolute;
    z-index: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: none;
    bottom: 0;
}

#pop-up-moh .line-tick .line-moh {
    width: 100.5em;
    aspect-ratio: 1005 / 306;
    left: 9em;
    position: relative;
    top: -1em;
    clip-path: inset(0 100% 0 0);
    transition: clip-path 1s ease-in-out;
}

#pop-up-moh .line-tick .hand-img {
    position: relative;
    top: 2.1em;
    left: -6em;
    width: 24.9em;
    aspect-ratio: 290 / 188;
    opacity: 0;
    transition: opacity 0.5s ease-in-out 0.7s;
}

@media (max-width: 576px) {
    .popup-moh{
        width: 90vw;
        font-size: 0.65rem;
    }
    .popup-moh .logo-byt {
        margin: 2% 0;
        width: 16%;
    }
    .title-moh .title__main {
        font-size: 2.5vw;
    }
    .btn-close-modal-moh {
        font-size: 3.5em;
    }
    .bottom-disc-moh__left {
        font-size: 1.5vw;
    }
    .bottom-disc-moh__right {
        font-size: 0.75vw;
    }
    .btn-cta-moh {
        font-size: 1.9vw;
        min-width: 20em;
        bottom: 14%;
        border: 2px solid #009EA4;
    }
    #pop-up-moh .group-tick {
        bottom: 7.5vw;
    }
    #pop-up-moh .btn-tick-prevent {
        font-size: 2vw;
    }
    #pop-up-moh .tick-border {
        width: 8.5vw;
        left: -6%;
        min-width: 6em;
    }
}

@media (min-width: 576px) and (max-width: 768px) {
    .popup-moh{
        width: 75vw;
        font-size: 0.7rem;
    }
    .popup-moh .logo-byt {
        width: 16%;
        margin: 3% 0;
    }
    .title-moh .title__main {
        font-size: 1.7vw;
    }
    .btn-close-modal-moh {
        font-size: 2.5em;
    }
    .bottom-disc-moh__left {
        font-size: 1.3vw;
    }
    .bottom-disc-moh__right {
        font-size: 0.65vw;
    }
    .btn-cta-moh {
        font-size: 1.6vw;
    }
    #pop-up-moh .group-tick {
        bottom: 5.5vw;
    }
    #pop-up-moh .btn-tick-prevent {
        font-size: 1.6vw;
    }
    #pop-up-moh .tick-border {
        width: 2.5vw;
        left: -7%;
        min-width: 7em;
    }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
    .popup-moh{
        width: 75vw;
    }
    .popup-moh .logo-byt {
        margin: 4% 0;
    }
    .title-moh .title__main {
        font-size: 1.7vw;
    }
    .btn-close-modal-moh {
        font-size: 2.5em;
    }
    .bottom-disc-moh__left {
        font-size: 1.2vw;
    }
    .bottom-disc-moh__right {
        font-size: 0.55vw;
    }
    .btn-cta-moh {
        font-size: 1.4vw;
    }
    #pop-up-moh .group-tick {
        bottom: 6vw;
    }
    #pop-up-moh .btn-tick-prevent {
        font-size: 1.6vw;
    }
    #pop-up-moh .tick-border {
        width: 4vw;
        left: -7%;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {
    .popup-moh{
        width: 65vw;
    }
    .btn-close-modal-moh {
        font-size: 2.5em;
    }
    .bottom-disc-moh__left {
        font-size: 1.2vw;
    }
    .bottom-disc-moh__right {
        font-size: 0.55vw;
    }
    #pop-up-moh .group-tick {
        bottom: 5.5vw;
    }
    #pop-up-moh .btn-tick-prevent {
        font-size: 1.4vw;
    }
    #pop-up-moh .tick-border {
        width: 4vw;
        left: -7%;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .popup-moh{
        width: 50vw;
    }
    .btn-close-modal-moh {
        font-size: 2.5em;
    }
    #pop-up-moh .tick-border {
        width: 4vw;
        left: -7%;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .header-title-moh {
        font-size: 1.3em;
    }
    .btn-close-modal-moh {
        font-size: 2.5em;
    }
}

@media (min-width: 1599px) {
    .btn-cta-moh {
        font-size: 1vw;
    }
    .popup-moh {
        font-size: 0.6vw;
    }
}