package components

import "webgo/hpv/postsv3/transport/responses"

templ ListContent(detail *responses.PostDetail) {
    if len(detail.Contents) > 0 {
        for _, item := range detail.Contents {
            if item.Type == 1 {
                if item.OptionValue.Key != "footer-detail-opt" {
                    if item.Content != nil {
                        @templ.Raw(*item.Content)
                    }
                }
            } else {
                if item.OptionValue.Key == "vote-share-socials" {
                    @VoteShareSocial(detail)
                } else {
                    @templ.Raw(item.OptionValue.Content)
                }
            }
        }
    }
}