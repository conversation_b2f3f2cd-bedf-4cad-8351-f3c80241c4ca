package entity

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

type SourcesList []SourceModel
type SourceModel struct {
	Name string `json:"name"`
	Link string `json:"link"`
}

func (s *SourcesList) Value() (driver.Value, error) {
	if s == nil {
		return nil, nil
	}

	return json.Marshal(s)
}

func (s *SourcesList) Scan(src interface{}) error {
	if src == nil {
		return nil
	}
	var data []byte
	switch dataSrc := src.(type) {
	case string:
		data = []byte(dataSrc)
	case []byte:
		data = dataSrc
	default:
		return errors.New("unsupported type for SourcesList")
	}

	var sources []SourceModel
	if err := json.Unmarshal(data, &sources); err != nil {
		return err
	}

	*s = sources
	return nil
}
