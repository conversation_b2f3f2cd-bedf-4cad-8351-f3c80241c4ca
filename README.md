# msd-goweb
msd new goweb


# I- <PERSON><PERSON><PERSON> h<PERSON> 
```bash
cp .env.sample .env
``` 

# II - Docker dev local nếu ko có
```bash
$ docker-compose -f docker-compose-dev.yml build
$ docker-compose -f docker-compose-dev.yml up
$ docker-compose -f docker-compose-dev.yml up -d
``` 
## - Build docker app

```bash
    docker-composer build --no-cache
    docker-composer up
```

## deploy
*** ---- 1. Keo nginx ---- ***
```bash
mkdir /home/<USER>
<NAME_EMAIL>:dev-networldasia/msd-goweb.git
```
*** ---- 2.set env ---- ***
--->> set ve con 199
```bash
DB_DRIVER=mysql
DB_CONNECTION=mysql
MYSQL_HOST=
MYSQL_PORT=
MYSQL_DATABASE=admin_hpv
MYSQL_USER=
MYSQL_PASS=
```
*** ---- 3. set build 777 ---- ***
``` bash 
sudo chmod -R 777 goweb
```

*** --- 4. set con 199 config mysql --- ***
```bash
docker ps -a (lay name mysql)
docker exec -it name_msql mysql -u root -p
CREATE USER 'web_user_126'@'ip' IDENTIFIED BY 'pass'; GRANT ALL PRIVILEGES ON *.* TO 'web_user_126'@'ip' WITH GRANT OPTION; FLUSH PRIVILEGES;
```

*** test thu ***
``` bash
./goweb web
```
*** ---- 5. set service ---- ***
--> Tao service /ect/systemd/system/goweb.service
```bash
[Unit]
Description=Go Server
[Service]
ExecStart=/home/<USER>/msd-goweb/goweb web
WorkingDirectory=/home/<USER>/msd-goweb
User=root
Group=rootngin
Restart=always
[Install]
WantedBy=multi-user.target
```

systemctl enable goweb.service
systemctl start goweb.service
systemctl status goweb.service

-->> test web done

## VI - Golang with Air
https://github.com/air-verse/air
```bash
go install github.com/air-verse/air@latest
air -v
curl -sSfL https://raw.githubusercontent.com/air-verse/air/master/install.sh | sh -s
air init
air //(is run)
```
**1. Error (macbook): zsh: command not found: air**
```bash
echo 'export PATH=$PATH:$HOME/go/bin' >> ~/.zshrc
source ~/.zshrc
```

**2. Change path in .air.toml when main.go in /cmd**
```bash
cmd = "go build -o ./tmp/main ." -> cmd = "go build -o ./tmp/main ./cmd"
```

## VII - Golang templ
https://templ.guide
```bash
go install github.com/a-h/templ/cmd/templ@latest
```
Then, create file xyz.templ
```
go get github.com/a-h/templ
templ generate
```


*** fix macbook ***
```bash
go install github.com/a-h/templ/cmd/templ@latest
 GOBIN=$HOME/go/bin go install github.com/a-h/templ/cmd/templ@latest\n
 which templ
  export GOPATH="$HOME/go"
  export GOBIN="$GOPATH/bin"
  export PATH="$PATH:$GOBIN"
 source ~/.zshrc
 go install github.com/a-h/templ/cmd/templ@latest
 which templ
```

### Tích hợp Air và Templ
Sửa file .air.toml
```bash
  cmd = "templ generate && go build -o ./tmp/main ."
  exclude_regex = ["_test.go",".*_templ.go"]
  include_ext = ["go", "tpl", "tmpl", "html", "templ"]
```

https://github.com/a-h/templ/blob/main/examples/blog/posts.templ

### Layout
```bash
templ Layout() {
    <!DOCTYPE html>
    <html lang="en">
        <head>
            <meta charset="UTF-8">
            <title>{title}</title>
        </head>
        <body>
            <main>
               { children... }
            </main>
        </body>
    </html>
}
```
```bash
import "admin/views/layouts"

templ Dashboard() {
    @layouts.Layout("Dashboard") {
		<div>Welcome to my website.</div>
	}
}
```

## set seq
```bash
SELECT setval('cms.options_values_id_seq', (SELECT MAX(id) FROM cms.options_values));
SELECT setval('cms.option_group_id_seq', (SELECT MAX(id) FROM cms.option_group));
```

## upgrage
```bash
go get -u github.com/a-h/templ
```