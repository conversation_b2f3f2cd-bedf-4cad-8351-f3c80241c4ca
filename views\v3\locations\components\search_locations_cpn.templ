package components

import (
    "webgo/pkg/gos/templates"
    "webgo/hpv/locations/transport/responses"
    "fmt"
)

templ SearchLocationsCpn(provinces []responses.KeyValueResp) {

    <div class="col-1/3 form-and-partner">
        <!-- Form -->
        <div class="form-consultant" id="form-consultant">
            <div class="header-label">
                <h3 class="header-label__title">
                    TÌM ĐIỂM TƯ VẤN <br> GẦN BẠN NHẤT
                </h3>
            </div>
    
            <div class="form-input form" id="form">
                <div class="container-province-district">
                    <div class="custom-form__group">
                        <span class="custom-form__arrow"><i class="bx bx-chevron-down"></i></span>
                        <select class="custom-form__select" id="province" data-url={ templates.AssetURL("") }>
                            <option disabled>Chọn Tỉnh/Thành</option>
                                for _, province := range provinces {
                                    <option data-value={ province.Name } class="dropdown-item" data-id={ fmt.Sprintf("%v", province.ID) }>         
                                        { province.Name }
                                    </option>
                                }

                        </select>
                    </div>
                    <div class="custom-form__group">
                        <span class="custom-form__arrow"><i class="bx bx-chevron-down"></i></span>
                        <select class="custom-form__select" id="district">
                            <option disabled>Chọn Quận/Huyện</option>
                            <option class="dropdown-item" data-id="">
    
                            </option>
                        </select>
                    </div>
                </div>
                <div class="input-control" id="address" data-url="{{site}}">
                    <input type="text" name="search_address" class="form-input__field" placeholder="Địa chỉ">
                </div>
                <div class="input-control" id="action-submit">
                    <button id="btn-find-now" type="button">Tìm Ngay</button>
                </div>
            </div>
        </div>
        <!-- Location Info Details -->
        <div class="consult-location-partner" id="consult-location-partner">
            <h3 class="partner-title">THAM VẤN <br>CÙNG CHUYÊN GIA Y TẾ</h3>
            <div class="location-list">
                <div class="location-item glow-on-hover" data-link="https://vnvc.vn/dang-ky-tiem-chung" id="btn-location-round-vnvc-display"
                    title="Hệ thống tiêm chủng VNVC - Công ty cổ phần Vacxin Việt Nam">
                    <img src={ templates.AssetURL("/asset/images/common/logo-vnvc.svg") }
                        alt="Hệ thống tiêm chủng VNVC - Công ty cổ phần Vacxin Việt Nam">
                </div>
    
                <div class="location-item glow-on-hover" data-link="https://nhathuoclongchau.com.vn/trung-tam-tiem-chung" id="btn-location-round-long-chau-display"
                    title="Nhà thuốc FPT Long Châu - Hệ thống chuỗi nhà thuốc lớn">
                    <img src={ templates.AssetURL("/asset/images/common/logo-long-chau.svg") }
                        alt="Nhà thuốc FPT Long Châu - Hệ thống chuỗi nhà thuốc lớn">
                </div>
    
                <div class="location-item glow-on-hover" data-link="https://youmed.vn/dat-kham/tiem-chung" id="btn-location-round-you-med-display" 
                    title="Danh sách các Bệnh viện - YouMed - Ứng dụng đặt lịch khám Bệnh viện, Bác sĩ">
                    <img src={ templates.AssetURL("/asset/images/common/logo-you-med.svg") }
                        alt="Danh sách các Bệnh viện - YouMed - Ứng dụng đặt lịch khám Bệnh viện, Bác sĩ">
                </div>
    
                <div class="location-item glow-on-hover" data-link="https://hellobacsi.com/care/" id="btn-location-round-hello-bac-si-display"
                    title="Hello Bacsi - Đăng ký khám bệnh, khám sức khỏe tổng quát, xét nghiệm">
                    <img alt="Hello Bacsi - Đăng ký khám bệnh, khám sức khỏe tổng quát, xét nghiệm"
                         src={ templates.AssetURL("/asset/images/common/logo-hello-health.svg") }>
                </div>
            </div>
        </div>
    </div>
}