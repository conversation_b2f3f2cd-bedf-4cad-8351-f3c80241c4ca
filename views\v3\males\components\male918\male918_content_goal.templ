package male918

import "webgo/pkg/gos/templates"

templ Male918ContentGoal() {
    <section class="container goal">
        <div class="swiper swiper-container-goal"> 
            <div class="boder-bg-slide">
                <div class="slide-to-show">
                    <svg style="transform: rotate(180deg);" class="icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <use xlink:href="#read-more-line-icon" />
                    </svg>
                    Lướt để xem
                </div>
            </div>
            <div class="swiper-wrapper">
                <div class="swiper-slide slide-1">
                    <div class="content-think-area">
                    <div class="pop-up-think right">Bạn kỳ vọng gì <br> cho tương lai con mình?</div>
                    </div>
                    <div class="img-swip-area">
                    <img class="swiper-lazy" data-src={templates.AssetURL("/asset/images/male-9-18-adapt/male-1-small.webp")}
                    srcset={templates.AssetURL("/asset/images/male-9-18-adapt/male-1-small.webp 480w, /asset/images/male-9-18-adapt/male-1.webp")}
                    sizes="(max-width: 480px) 480px, 1024px" alt="Suy nghi" />
                    </div>
                </div>
                <div class="swiper-slide slide-2">
                    <h2 class="title-slide">
                        Bạn kỳ vọng gì cho tương lai con mình?
                    </h2>
                    <div class="content-think-area">
                    <div class="pop-up-think right">Con đạt thành tích cao,
                        <br>du học nước ngoài</div>
                    </div>
                    <div class="img-swip-area">
                    <img class="swiper-lazy" data-src={templates.AssetURL("/asset/images/male-9-18-adapt/male-2-small.webp")}
                    srcset={templates.AssetURL("/asset/images/male-9-18-adapt/male-2-small.webp 480w, /asset/images/male-9-18-adapt/male-2.webp")}
                    sizes="(max-width: 480px) 480px, 1024px" alt="Duong toi thanh cong" />
                    </div>
                </div>
                <div class="swiper-slide slide-3">
                    <h2 class="title-slide">
                    Bạn kỳ vọng gì cho tương lai con mình?
                    </h2>
                    <div class="content-think-area">
                    <div class="pop-up-think right">
                        Con trở thành kiến trúc sư, <br> thiết kế các công trình lớn
                    </div>
                    </div>
                    <div class="img-swip-area">
                    <img class="swiper-lazy" data-src={templates.AssetURL("/asset/images/male-9-18-adapt/male-3-small.webp")}
                    srcset={templates.AssetURL("/asset/images/male-9-18-adapt/male-3-small.webp 480w, /asset/images/male-9-18-adapt/male-3.webp")}
                    sizes="(max-width: 480px) 480px, 1024px" alt="Doc lap tai chinh" />
                    </div>
                </div>
                <div class="swiper-slide slide-4">
                    <h2 class="title-slide">
                    Bạn kỳ vọng gì cho tương lai con mình?
                    </h2>
                    <div class="content-think-area">
                    <div class="pop-up-think right">Con trở thành phi công,<br>chinh phục trời xanh</div>
                    </div>
                    <div class="img-swip-area">
                    <img class="swiper-lazy" data-src={templates.AssetURL("/asset/images/male-9-18-adapt/male-4-small.webp")}
                    srcset={templates.AssetURL("/asset/images/male-9-18-adapt/male-4-small.webp 480w, /asset/images/male-9-18-adapt/male-4.webp")}
                    sizes="(max-width: 480px) 480px, 1024px" alt="Duong toi thanh cong" />
                    </div>
                </div>
                <div class="swiper-slide slide-5">
                    <h2 class="title-slide">
                    Bạn kỳ vọng gì cho tương lai con mình?
                    </h2>
                    <div class="content-think-area">
                    <div class="pop-up-think right">Con trở thành cầu thủ,<br>ghi bàn trên sân cỏ</div>
                    </div>
                    <div class="img-swip-area">
                    <img class="swiper-lazy" data-src={templates.AssetURL("/asset/images/male-9-18-adapt/male-5-small.webp")}
                    srcset={templates.AssetURL("/asset/images/male-9-18-adapt/male-5-small.webp 480w, /asset/images/male-9-18-adapt/male-5.webp")}
                    sizes="(max-width: 480px) 480px, 1024px" alt="Tot nghiep" />
                    </div>
                </div>
                <div class="swiper-slide slide-6">
                    <div class="content-think-area">
                    <div class="pop-up-think right">
                        <span class="note">
                            PHÒNG VỆ HPV <br>CHO CON NGAY <br>HÔM NAY
                            </span>
                        <span>để an tâm cùng con thực hiện <br> những ước mơ tương lai</span>
                        <a href="#result-section" class="now-btn">TÌM HIỂU NGAY VỀ HPV</a>
                    </div>
                    </div>
                    <div class="img-swip-area">
                    <img class="swiper-lazy" data-src={templates.AssetURL("/asset/images/male-9-18-adapt/male-6-small.webp")}
                    srcset={templates.AssetURL("/asset/images/male-9-18-adapt/male-6-small.webp 480w, /asset/images/male-9-18-adapt/male-6.webp")}
                    sizes="(max-width: 480px) 480px, 1024px" alt="Du phong ngay" />
                    </div>
                </div>
            </div>
            <div class="control-navigate-pagination">
                <div class="action-navigate">
                    <span class="btn-play">
                    <svg class="icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="#play-solid-icon" />
                    </svg>
                    </span>
                    <span class="btn-pause active">
                    <svg class="icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="#pause-solid-icon" />
                    </svg>
                    </span>
                </div>
                <div class="swiper-pagination bg-transparent"></div>
            </div>
        </div>
    </section>
}
