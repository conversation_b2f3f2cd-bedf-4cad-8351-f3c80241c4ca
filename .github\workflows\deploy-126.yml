
name: Deploy to VPS 126
on:
  push:
    branches:
      - master
jobs:
  deploy:
    runs-on: ubuntu-latest  # You can choose a different runner if needed
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2
      - name: SSH Deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST_S126 }}
          USERNAME: ${{ secrets.USERNAME_S126 }}
          PORT: ${{ secrets.PORT_S126 }}
          KEY: ${{ secrets.PRI_KEY_S126_GOWEB }}
          script: |            
            systemctl stop goweb
            destination_dir="/home/<USER>/msd-goweb"              
            cd $destination_dir
            git pull origin master
            systemctl restart goweb
            rm -rf /var/cache/nginx/*
            systemctl reload nginx  
