package repository

import (
	"context"
	"webgo/hpv/entity"
	"webgo/pkg/gos/generics"
	"webgo/pkg/gos/utils"

	"gorm.io/gorm"
)

type districtRepo struct {
	db *gorm.DB
}

func NewDistrictRepo(db *gorm.DB) *districtRepo {
	return &districtRepo{db: db}
}

/**
 * find district repo
 */
func (r *districtRepo) FindDistrictRepo(ctx context.Context, filter *utils.Filters) ([]*entity.DistrictEntity, error) {
	tableName := entity.DistrictEntity{}.TableName()
	return generics.FindGeneric[entity.DistrictEntity](ctx, r.db, tableName, filter)
}
