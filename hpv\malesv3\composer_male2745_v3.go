package malesv3

import (
	"webgo/hpv/malesv3/transport/handlers"
	"webgo/hpv/malesv3/usecase"
	"webgo/hpv/repository"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/component/gormc"
	"webgo/pkg/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type composerMale2745V3 interface {
	ListMale2745V3Hdl() fiber.Handler
}

func ComposerMale2745V3Service(serviceCtx sctx.ServiceContext) composerMale2745V3 {
	db := serviceCtx.MustGet(configs.KeyCompGorm).(gormc.GormComponent).GetDB()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web")
	
	repoPost := repository.NewPostRepo(db)
	categoryRepo := repository.NewCategoryRepo(db)
	optionRepo := repository.NewOptionValueRepo(db)
	
	usc := usecase.NewMale2745V3Usc(repoPost, logger, categoryRepo, optionRepo)
	hdl := handlers.NewMale2745V3Hdl(usc)

	return hdl
}
