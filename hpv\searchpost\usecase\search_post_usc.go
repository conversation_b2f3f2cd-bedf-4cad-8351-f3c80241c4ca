package usecase

import (
	"context"
	"webgo/hpv/common/enums"
	"webgo/hpv/entity"
	"webgo/pkg/gos/utils"
)

type SearchPostUsc interface {
	FindPostRepo(ctx context.Context, filter *utils.Filters) ([]*entity.PostEntity, error)
}

type searchPostUsc struct {
	Repo SearchPostUsc
}

func NewSearchPostUsc(repo SearchPostUsc) *searchPostUsc {
	return &searchPostUsc{
		Repo: repo,
	}
}

func (usc *searchPostUsc) SearchPostUsc(ctx context.Context, keyword string) ([]*entity.PostEntity, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"title iLIKE ?": "%" + keyword + "%",
			"status":       enums.POST_STATUS_PUBLISH,
		},
		Columns: &[]string{
			"page_path",
			"title",
			"img",
		},
		PageSize: enums.LIMIT_10,
	}

	datas, err := usc.Repo.FindPostRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return datas, nil
}

func (usc *searchPostUsc) ShowPostUsc(ctx context.Context) ([]*entity.PostEntity, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"status": enums.POST_STATUS_PUBLISH,
		},
		Columns: &[]string{
			"page_path",
			"title",
			"img",
		},
		PageSize: enums.LIMIT_6,
		OrderBy:  &[]string{enums.ORDERBY_ID_DESC},
	}

	postDatas, err := usc.Repo.FindPostRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	if len(postDatas) == 0 {
		return nil, nil
	}

	return postDatas, nil
}
