package female918

import "webgo/pkg/gos/templates"

templ Female918Banner() {
    <section class="female-banner-main wow animate__pulse" data-wow-duration="1.5s" data-wow-delay="0s">
        <div class="description-banner-wrapper">
            <p class="description-banner-left">Không phải là bệnh nhân thật, chỉ dành cho mục đích minh họa. Nội dung này được phối hợp biên soạn bởi Hội Y học Dự phòng Việt Nam và MSD, kiểm nhận bởi Hội Y học Dự phòng Việt Nam và MSD tài trợ cho mục tiêu giáo dục VN-GSL-01840 02032027 <br>Một nghiên cứu ở châu Phi cho thấy, trong số 474 trẻ em gái tuổi vị thành niên dù khai báo chưa có quan hệ tình dục, c<PERSON> đến 8,4% đ<PERSON><PERSON><PERSON> xét nghiệm đã cho thấy bị nhiễm HPV, và có 5,3% b<PERSON> nhiễm các típ HPV nguy cơ cao (1)</p>
            <p class="description-banner-right">(1) Catherine F. Houlihan et al., Prevalence of Human Papillomavirus in Adolescent Girls Before Reported Sexual Debut, The Journal of Infectious Diseases, 2014, 210:837–45</p>    
        </div>
        <div class="content-banner">
            <span id="find-consultant-location-banner" class="btn-cta" alt="dia-diem-tu-van">THAM VẤN VỚI CHUYÊN GIA Y TẾ ĐỂ DỰ PHÒNG CHO CON</span>
        </div>
        <div class="img-banner">
            <div class="img-banner__thumbnail">
                <div class="img-banner-background">
                    <img class="" src={templates.AssetURL("/assetimages/female-9-18-adapt/female918-desktop.webp")}
                        srcset={templates.AssetURL("/assetimages/female-9-18-adapt/female918-mobile.webp 480w, /asset/images/female-9-18-adapt/female918-desktop.webp")}
                        sizes="(max-width: 480px) 480px, 1024px" alt="Male person in the age range of 9 to 18" />
                </div>
            </div>
        </div>
    </section>
}