package utils

import (
	"time"
)

/**
 * calc report_end int
 */

func CalcReportEndTimeInt(create_time, kpi_time int64) *time.Time {
	reportEnd := create_time + (kpi_time * 24 * 60 * 60)
	t := time.Unix(reportEnd, 0)
	location, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
	reportEndTime := t.In(location)

	return &reportEndTime
}

/**
 * get time now location ho chi minh
 */
func TimeNowLocationHCM() time.Time{
	location, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
	return time.Now().In(location)
}