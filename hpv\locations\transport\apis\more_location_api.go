package apis

import (
	"context"
	"webgo/hpv/locations/transport/requests"
	"webgo/hpv/locations/transport/responses"
	"github.com/gofiber/fiber/v2"
)

type ApiMoreLocationUsc interface {
	ListMoreLocationsUsc(ctx context.Context, page int) (*responses.MoreLocationsResp, error)
}

type moreLocationApi struct {
	usc ApiMoreLocationUsc
}

func MoreLocationApi(usc ApiMoreLocationUsc) *moreLocationApi {
	return &moreLocationApi{
		usc: usc,
	}
}

/**
 * search location api
 */
func (a *moreLocationApi) MoreLocationApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.MoreLocationReq // PHẢI LÀ struct này
		if err := c.BodyParser(&payload); err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid request payload",
			})
		}

		if err := payload.Validate(); err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": err.Error(),
			})
		}

		locations, err := a.usc.ListMoreLocationsUsc(c.Context(), payload.Page)
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": err.Error(),
			})
		}

		return c.JSON(fiber.Map{
			"data": locations,
		})
	}
}
