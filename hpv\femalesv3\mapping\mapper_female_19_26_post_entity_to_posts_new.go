package mapping


import (
	"webgo/hpv/entity"
	"webgo/hpv/femalesv3/transport/responses"
)

/**
 * Mapper Post Entity to PostsNew
 * page female1926V3
 */
func MapperFemale1926V3PostEntityToPostsNew(posts []*entity.PostEntity) []responses.Female1926V3PostNew {
	if posts == nil {
		return nil
	}
	var res []responses.Female1926V3PostNew
	for _, post := range posts {
		res = append(res, responses.Female1926V3PostNew{
			Title:    post.Title,
			PagePath: post.PagePath,
			Img:      post.Img,
		})
	}

	return res
}
