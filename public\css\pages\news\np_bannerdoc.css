.wrap__right{
    width: 100%;
    height: fit-content;
    position: sticky;
    top: 0;  
    z-index: 10;  
}
.call-cta {
    width: 100%;
    height: auto;
    aspect-ratio: 284 / 641;
    position: relative;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background: linear-gradient(180deg, rgba(106, 206, 174, 1) 0%, rgba(5, 168, 174, 1) 50%);
    overflow: hidden;
    border-radius: 1em;
}
.call-cta__img {
    position: absolute;
    transform: scale(1.5);
    transform-origin: bottom center; /* Đặt điểm tham chiếu là cạnh dưới */
    bottom: 0;
}
.call-cta__wrapper {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 1em;
    transition: all 0.3s;
    top: 8%;
    font-size: 1.2vw;
}
.call-cta-text p {
    font-size: 1.2em;
    color: white;
    margin-bottom: 0;
}
.call-cta-text h3 {
    font-size: 1.5em;
    color: white;
}
.call-cta__btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5em;
}
.btn-primary {
    background: rgba(255, 222, 0, 1);
    color: #030303;
    border: 1px solid white;
    padding: 0.5em 1.3em;
    border-radius: 3em;
    transition: all 0.3s;
    font-weight: 500;
    font-size: 0.4em;
    min-width: 58%;
}
.call-cta__btn a:hover {
    background-color: rgb(136, 179, 252);
}

/* Responsive */
@media (max-width: 768px) {
    .ana-main {
        margin-top: 7vw;
    }
    .ana-main__wrapper{
        margin-bottom: 3em;
        gap: 3em;
        display: flex;
        flex-direction: column-reverse;
    }
    .wrap__right {
        width: 100%;
        height: fit-content;
        position: relative;
    }
    .call-cta,
    .call-cta-minah {
        width: 100%;
        height: 22vw;
        aspect-ratio: unset;
        position: relative;
        border-radius: 2em;
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        align-items: flex-end;
        overflow: visible;
    }
    .call-cta__img,
    .call-cta-minah__img {
        position: relative;
        transform: scale(1);
        transform-origin: bottom center;
        bottom: 0;
        width: 30%;
    }
    .call-cta__wrapper {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 1em;
        transition: all 0.3s;
        top: 0;
        font-size: 2.5vw;
        height: 100%;
    }
}
@media (max-width: 575px) {
    .call-cta__wrapper {
        font-size: 2.5vw;
    }
    .call-cta__btn {
        font-size: 1.2em;
    }
    .btn-primary {
        padding: 0.25em 0.4em;
        font-size: 0.55em;
        min-width: 58%;
    }
}