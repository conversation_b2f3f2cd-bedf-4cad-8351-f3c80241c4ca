package mapping


import (
	"webgo/hpv/entity"
	"webgo/hpv/femalesv3/transport/responses"
)

/**
 * Mapper Post Entity to PostsNew
 * page female918V3
 */
func MapperFemale918V3PostEntityToPostsNew(posts []*entity.PostEntity) []responses.Female918V3PostNew {
	if posts == nil {
		return nil
	}
	var res []responses.Female918V3PostNew
	for _, post := range posts {
		res = append(res, responses.Female918V3PostNew{
			Title:    post.Title,
			PagePath: post.PagePath,
			Img:      post.Img,
		})
	}

	return res
}
