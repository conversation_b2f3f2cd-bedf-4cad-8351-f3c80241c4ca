package mapping

import (
	"webgo/hpv/entity"
	"webgo/hpv/mohV3/transport/responses"
)

func MapperPostToMohItemResq(postsMoh []*entity.PostEntity) []responses.MohItem {
	if postsMoh == nil {
		return nil
	}
	var res []responses.MohItem
	for _, post := range postsMoh {
		res = append(res, responses.MohItem{
			PagePath:    post.PagePath,
			Title:       post.Title,
			Img:         post.Img,
			Description: post.Description,
			Views:       post.Views,
		})
	}

	return res
}