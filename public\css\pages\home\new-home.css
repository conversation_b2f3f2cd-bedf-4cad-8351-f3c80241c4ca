.root-container {
   background-position: 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px;
   background-image: radial-gradient(25% 70% at -1% 91%, #38ab9f 1%, #38ab9f 1%, #073AFF00 91%),
      radial-gradient(30% 53% at 62% 163%, #73F2FFFF 0%, #073AFF00 100%),
      linear-gradient(125deg, #FFFFFFFF 8%, #d3e9d6 44%, #ACE0C8FF 93%);
   font-family: Unbounded;
   font-display: swap;
}

.container-sidebar.is-sticky {
   position: fixed;
   margin-top: 8em;
   width: inherit;
}
.body-main-content .footer__content {
   display: none;
}

.group-box-male {
   width: 100%;
}

.container-content .main-container .body-main-content {
   position: relative;
   z-index: 1;
   background: linear-gradient(359.98deg, #009585 0.62%, #B2FFF9 99.98%), linear-gradient(0deg, rgba(255, 255, 255, 0.74), rgba(255, 255, 255, 0.74));
}

.main-content {
   overflow: hidden;
}

.container-content .main-container .banner-main {
   position: relative;
   background: #d1faf4;
   font-size: 0.625vw;
}

.banner-main__desktop {
   width: 100%;
   height: 100%;
   object-fit: cover;
}

.banner-main__mobile {
   width: 100%;
   height: 100%;
   object-fit: cover;
}

.male-box__cta.cta-kv {
   position: absolute;
   padding: 0.6em 1.8em;
   top: 50%;
   left: 3%;
   z-index: 9;
   background: rgba(0, 158, 164, 1);
   box-shadow: 0 4px 4px rgba(14, 55, 54, 0.6);
}

.male-box__cta.cta-kv span {
   position: relative;
   font-size: 2em;
   font-weight: 500;
   color: var(--neutral-900);
   text-transform: uppercase;
   font-family: Unbounded; 
}

/* Sidebar */
.container-sidebar,
.container-sidebar-mobile {
   background: linear-gradient(207.71deg, #ffffffba -36.69%, rgb(255 255 255 / 66%) 105.14%);  
   border-radius: 1.5em;
   display: flex;
   flex-direction: column;
   gap: 1.5em;
}

.container-sidebar .sidebar-title,
.container-sidebar-mobile .sidebar-title {
   background-color: var(--primary-500);
   border-radius: 1.5em;
   font-weight: 700;
   font-size: 1.5em;
   color: white;
   padding: 0.5em;
   text-align: center;
}

.list-card {
   display: flex;
   flex-direction: column;
   gap: 1.5em;
}

.list-card .card-item {
   font-size: 1em;
   display: flex;
   flex-direction: row;
   gap: 1.5em;
   background-color: white;
   border-radius: var(--radius-md);
   padding: 1em;
   flex: auto;
   overflow: hidden;
}

.list-card .card-item .avatar {
   border-radius: var(--radius-md);
   border: 1px solid var(--primary-500);
   overflow: hidden;
   flex: 0;
   min-width: 14em;
}

.list-card .card-item .avatar img {
   transition: all 0.3s ease-in-out;
   width: 100%;
   height: 100%;
   object-fit: cover;
}

.list-card .card-item .info {
   display: flex;
   flex-direction: column;
   gap: 0.7em;
   text-align: left;
   flex: 1;
   line-height: 1.2;
   font-size: 1.5em;
}

.list-card .card-item .info .title {
   font-size: 18px;
   font-weight: 700;
   text-transform: uppercase;
   color: var(--primary-500);
}

.list-card .card-item .info .description {
   flex-grow: 1;
}

.list-card .card-item .info .actions {
   display: flex;
   flex-direction: row;
   gap: 0.8em;
}

.list-card .card-item .info .actions a {
   font-size: 1.4em;
   flex: 1;
   line-height: 1.2;
}

.list-card .card-item:hover .avatar img {
   transform: scale(1.1) rotateZ(2deg);
}

.sidebar-action-controls {
   display: flex;
   flex-direction: column;
   gap: 1em;
   color: var(--neutral-400);
}

.sidebar-action-controls .action-item {
   display: flex;
   flex-direction: row;
   border-radius: var(--radius-md);
   padding: 0.5em 2em;
   color: white;
   font-size: 2em;
   background-color: var(--primary-500);
   vertical-align: middle;
   align-items: center;
   justify-content: center;
   gap: 0.7em;
   cursor: pointer;
   transition: all 0.3s ease-in;
}

.sidebar-action-controls .action-item:hover {
   background-color: var(--primary-800);
}

.sidebar-action-controls .action-item.active {
   background-color: var(--primary-800);
}

.sidebar-action-controls .action-item img {
   width: 2em;
   height: 2em;
}

.sidebar-action-controls .action-item#sidebar-btn-booking img {
   height: 1.7em;
}

.container-sidebar-mobile {
   background-color: transparent;
   position: static;
}

.container-sidebar-mobile .copyright {
   font-size: 0.58em;
   color: var(--neutral-300);
   margin-top: 15px;
   display: block;
   text-align: center;
   padding: 0 10px;
}

.container-sidebar-mobile .sidebar-title {
   border-radius: 0;
}

.container-sidebar-mobile .list-card {
   padding: 8px;
   display: flex;
   flex-direction: row;
   gap: 1.5em;
   flex-wrap: wrap;
}

.container-sidebar-mobile .list-card .card-item {
   border-radius: var(--radius-md);
   border: 1px solid var(--neutral-500);
   padding: 0;
   gap: 0;
   flex: 1;
   min-width: 31em;
   font-size: 1em;
}

.container-sidebar-mobile .list-card .card-item .avatar {
   border-radius: 0;
   border: none;
   min-width: 10em;
}

.container-sidebar-mobile .list-card .card-item .info {
   text-align: center;
   align-items: center;
   justify-content: center;
   font-size: 1em;
   padding: 1em;
   padding-left: 0;
   letter-spacing: -0.5px;
}

.container-sidebar-mobile .list-card .card-item .info .title {
   font-size: 1.1em;
}

.container-sidebar-mobile .list-card .card-item .info .subtitle {
   margin-right: -15px;
   margin-left: -2.5em;
   font-size: 1em;
   letter-spacing: -0.3px;
}

.container-sidebar-mobile .list-card .card-item .info .btn {
   font-size: 1.2em;
}

@media (min-width: 1500px) {}
@media (min-width: 1300px) {}
@media (min-width: 1100px) {
   .list-card .card-item {
      font-size: 1em;
   }

   .list-card .card-item .avatar {
      min-width: 12.5em;
   }

   .list-card .card-item .info {
      font-size: 1.3em;
   }

   .list-card .card-item .info .title {
      font-size: 1.15em;
   }

   .list-card .card-item .info .actions a {
      font-size: 1.4em;
      flex: 1;
      line-height: 1.2;
   }
}

@media (max-width: 575px) {
   .container-content .main-container .banner-main {
      font-size: 2vw;
   }
   .male-box__cta.cta-kv {
      padding: 0.6em 1.8em;
      top: 20%;
      z-index: 99;
      font-size: 0.7em;
      left: 50%;
      transform: translateX(-50%);
      width: max-content;
      white-space: nowrap;
   }

   .male-box__cta.cta-kv span {
    color: var(--neutral-900);
   }
}
/* END Sidebar */

/* Sidebar New */
.group-box-male {
   position: relative;
   height: 100%;
   display: grid;
   grid-template-columns: 1fr;
   grid-template-rows: repeat(3, 1fr);
   grid-column-gap: 0px;
   grid-row-gap: 1em;
   padding: 0 1em 1em;
}

.male-box {
   position: relative;
   display: flex;
   justify-content: flex-end;
   flex-direction: column;
   border-radius: 1.5em;
   cursor: pointer;
   transition: all 200ms ease-in;
   flex: auto;
   gap: 0.5em;
   background: var(--neutral-900);
   padding: 1em 2em;
   height: 23vh;
}

.male-box .bg-overlay {
   background: linear-gradient(190.11deg, rgba(102, 102, 102, 0) 45.8%, rgba(0, 60, 5, 0.576) 85.22%);
   width: 100%;
   height: 100%;
   position: absolute;
   top: 0;
   left: 0;
   border-radius: 1.5em;
   z-index: 1;
}

.male-box__content {
   color: var(--neutral-900);
   font-size: 2em;
   font-weight: 800;
   line-height: 1.2;
   text-shadow: 0px 4px 3px rgba(0, 0, 0, 0.4),
      0px 8px 13px rgba(0, 0, 0, 0.1),
      0px 18px 23px rgba(0, 0, 0, 0.1);
   z-index: 2;
}

.male-box.box-1,
.male-box.box-2,
.male-box.box-3 {
   background-image: url(../../../images/new-home/bg-cta.webp);
   background-position: center;
   background-size: cover;
   background-repeat: no-repeat;
}

/* .male-box.box-2 {
   background: linear-gradient(to right, #8cd3f8, #d5efff);
 }
 
 .male-box.box-3 {
   background: linear-gradient(to right, #8cd3f8, #d5efff);
 } */

.male-box__img {
   position: absolute;
   right: 2em;
   bottom: 0;
   transition: transform 0.3s ease, transform-origin 0.3s ease;
   transform-origin: bottom center;
   will-change: transform;
}

.male-box:hover .male-box__img {
   transform: scale(1.1);
   z-index: 2;
}

.male-box__img img {
   width: 27.1em;
   aspect-ratio: 271/200;
}

.group-cta {
   display: flex;
   gap: 1em;
}

.male-box__cta {
   width: fit-content;
   position: relative;
   border-radius: 5em;
   overflow: hidden;
   padding: 0.4em 1.6em;
   left: -1.6em;
   transition: all 0.2s ease;
   border: none;
   background: none;
   cursor: pointer;
   display: flex;
   justify-content: center;
   align-items: center;
   z-index: 2;
}

.male-box__cta:before {
   content: "";
   position: absolute;
   top: 0;
   left: 0;
   display: block;
   border-radius: 5em;
   background: var(--secondary-500);
   width: 0;
   height: 100%;
   transition: all 0.3s ease;
}

.male-box__cta span {
   position: relative;
   font-family: "Ubuntu", sans-serif;
   font-size: 1.5em;
   font-weight: 700;
   letter-spacing: 0.05em;
   color: var(--neutral-900);
}

.male-box__cta img {
   width: 2.4em;
   position: relative;
   top: 0;
   margin-left: 10px;
   transform: translateX(-5px);
   transition: all 0.3s ease;
}

.male-box__cta:hover:before {
   width: 100%;
   background: var(--secondary-500);
   border: 1px solid var(--neutral-900);
}

.male-box__cta:hover span {
   color: var(--primary-700);
}

.male-box__cta:hover img {
   transform: translateX(0);
   filter: invert(30%) sepia(63%) saturate(1800%) hue-rotate(147deg) brightness(88%) contrast(101%);
}

.male-box__cta:active {
   transform: scale(0.95);
}

.content-center__title {
   width: 100%;
   font-size: 2em;
   font-weight: 700;
   line-height: 1.2;
   text-align: center;
   padding: 0.5em 0;
   margin: 0 auto;
   background: var(--primary-900);
   color: var(--neutral-900);
}

/* END New Sidebar */

/* SECTION - Info HPV */
.hpv-info {
   position: relative;
   width: 100%;
   height: auto;
   font-size: 0.625vw;
   padding-bottom: 7vw;
}

.bg-virus {
   position: absolute;
   top: 0;
   left: 0;
   width: 100%;
   aspect-ratio: 1538 / 789;
   overflow: hidden;
   height: 100%;
   will-change: transform;
}

.bg-virus img {
   position: relative;
   width: 100%;
   height: 100%;
   object-fit: contain;
   z-index: 0;
}
.bg-virus picture {
   width: 100%;
   object-fit: cover;
   z-index: 0;
   position: relative;
}

.hpv-info__content {
   padding: 5% 0 10%;
   position: relative;
   z-index: 1;
}

.hpv-info__title {
   width: 85%;
   min-height: 1.7em;
   max-width: 21em;
   background: linear-gradient(270.9deg, rgba(0, 149, 133, 0) -5.04%, #009585 47.41%);
   font-size: 4.4em;
   padding: 0.3em 0;
   padding-left: 4rem;
   font-weight: 800;
   line-height: 1.1;
   color: var(--neutral-900);
}

.hpv-info__disc {
   width: 60%;
   font-size: 1.7em;
   font-weight: 300;
   line-height: 1.1;
   text-align: justify;
   padding: 2em 1em 0 2em;
}

.virus-img {
   position: absolute;
   right: 5%;
   width: 22em;
   aspect-ratio: 197 / 188;
   top: 10%;
   display: flex;
   justify-content: center;
   align-items: center;
}

.virus-img img {
   width: 90%;
   object-fit: cover;
   z-index: 2;
}

.ripple {
   position: absolute;
   text-decoration: none;
   color: #fff;
   width: 100%;
   aspect-ratio: 1;
   background-color: #fff;
   margin: 0 auto;
   border-radius: 50%;
   -webkit-animation: ripple 1s linear infinite;
   animation: ripple 1s linear infinite;
   z-index: 1;
}

@-webkit-keyframes ripple {
   0% {
      -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.3), 0 0 0 1em rgba(255, 255, 255, 0.3), 0 0 0 3em rgba(255, 255, 255, 0.3), 0 0 0 6em rgba(255, 255, 255, 0.3);
      box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.3), 0 0 0 1em rgba(255, 255, 255, 0.3), 0 0 0 3em rgba(255, 255, 255, 0.3), 0 0 0 6em rgba(255, 255, 255, 0.3);
   }

   100% {
      -webkit-box-shadow: 0 0 0 1em rgba(255, 255, 255, 0.3), 0 0 0 3em rgba(255, 255, 255, 0.3), 0 0 0 6em rgba(255, 255, 255, 0.3), 0 0 0 9em rgba(255, 255, 255, 0);
      box-shadow: 0 0 0 1em rgba(255, 255, 255, 0.3), 0 0 0 3em rgba(255, 255, 255, 0.3), 0 0 0 6em rgba(255, 255, 255, 0.3), 0 0 0 9em rgba(255, 255, 255, 0);
   }
}

@keyframes ripple {
   0% {
      box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.3), 
                 0 0 0 1em rgba(255, 255, 255, 0.3), 
                 0 0 0 3em rgba(255, 255, 255, 0.3), 
                 0 0 0 6em rgba(255, 255, 255, 0.3);
      transform: scale(1);
   }

   100% {
      box-shadow: 0 0 0 1em rgba(255, 255, 255, 0.3), 
                 0 0 0 3em rgba(255, 255, 255, 0.3), 
                 0 0 0 6em rgba(255, 255, 255, 0.3), 
                 0 0 0 9em rgba(255, 255, 255, 0);
      transform: scale(1.1);
   }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
   .hpv-info {
      padding-bottom: 14vw;
   }
   .fast-action-controls {
      display: none;
   }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
   .hpv-info {
      padding-bottom: 17vw;
   }
   .fast-action-controls {
      display: none;
   }
}

@media (min-width: 319px) and (max-width: 376px) {

   .hpv-info {
      font-size: 0.42em !important;
      padding-bottom: 7vw !important;
   }
}

@media (max-width: 575px) {
   .group-box-male {
      font-size: 0.9em;
   }

   .male-box {
      height: 21vh;
   }

   .container-sidebar,
   .container-sidebar-mobile {
      gap: 1.5em;
      border-radius: 2em;
      overflow: hidden;
   }

   .container-content .main-container {
      max-height: calc(100svh - 120px);
   }

   .fast-action-controls {
      display: none;
   }

   .hpv-info {
      padding-bottom: 7vw;
      font-size: 1.2vw;
   }

   .hpv-info__content {
      padding: 10% 0 10%;
      position: relative;
      z-index: 1;
   }

   .virus-img {
      position: absolute;
      right: -28%;
      width: 39.7em;
      top: 10%;
      display: flex;
      justify-content: center;
      align-items: center;
   }

   .hpv-info__title {
      padding-left: 1.5rem;
      font-size: 6em;
   }

   .hpv-info__disc {
      width: 65%;
      padding: 1.5rem 1.5rem 0 1.5rem;
      font-size: 3em;
  }

   .bg-virus {
      aspect-ratio: 880 / 1600;
   }
}

@media (min-width: 576px) and (max-width: 768px) {
   .hpv-info {
      font-size: 0.65rem;
      padding-bottom: 9vw;
   }

   .hpv-info__title {
      padding-left: 2.5rem;
   }

   .hpv-info__disc {
      width: 60%;
      padding: 2.5rem 2.5rem 0 2.5rem;
   }

   .container-content .main-container {
      max-height: calc(100svh - 120px);
   }

   .fast-action-controls {
      display: none;
   }
   .male-box {
      height: 16vh;
   }
   .bg-virus {
      aspect-ratio: 880 / 1600;
   }
}

/* SECTION - Fact HPV */
.fact-hpv {
   width: 100%;
   position: relative;
   margin-top: -9em;
   padding: 2em 0 5em;
   font-size: 0.625vw;
   overflow: hidden;
}

.hpv-3d {
   width: 82%;
   margin: 0 auto;
}

.fact-hpv__group {
   width: 100%;
   display: flex;
   flex-direction: column;
   gap: 2em;
   justify-content: center;
   transform: translateY(-8em);
   transition: max-height 0.3s ease-in-out;
}

.fact-hpv__top {
   width: 100%;
   display: flex;
   width: inherit;
   justify-content: flex-end;
   align-items: flex-end;
   gap: 2em;
   transform: translateX(15%);
}

.fact-hpv__bottom {
   width: 100%;
   display: flex;
   width: inherit;
   justify-content: flex-start;
   align-items: flex-start;
   gap: 2em;
   transform: translateX(-10%);
}

.fact-hpv__card {
   position: relative;
}

.fact-hpv__icon {
   display: flex;
   justify-content: center;
   align-items: center;
   width: 11em;
   aspect-ratio: 1;
   border-radius: 50%;
   background: linear-gradient(180deg, #FFFFFF 0%, #B9FDF6 100%);
   box-shadow: 0 4px 15px rgba(0,0,0,0.1);
   position: absolute;
   top: 0;
   left: 50%;
   z-index: 1;
   transform: translate(-50%, -50%);
   will-change: transform;
}

.fact-hpv__icon img {
   width: 70%;
   height: inherit;
   object-fit: cover;
}

.fact-hpv__content {
   border-radius: 2em;
   padding: 8em 2em 4em;
   background: linear-gradient(0deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.4)),
      linear-gradient(254.47deg, rgba(95, 214, 102, 0.21) 25.99%, rgba(179, 243, 253, 0.21) 90.86%);
   border: 1px solid;
   border-image-source: linear-gradient(180deg, rgba(255, 255, 255, 0.46) 0%, rgba(179, 243, 253, 0.46) 100%);
   backdrop-filter: blur(3em);
}

.fact-hpv__content.bg-fact {
   border: solid 5px #ffffff;
   border-radius: 2em;
   box-shadow: 0px 1px 3px 0px #0000004D;
   box-shadow: 3px 4px 7.5px 4px #00000017;
   background: linear-gradient(180deg, rgba(216, 244, 238, 0.83) 0%, rgba(186, 234, 245, 0.83) 100%);
}

.fact-hpv__content h4 {
   font-size: 1.9em;
   font-weight: 700;
   line-height: 1.5;
   letter-spacing: -0.02em;
   color: #39514D;
}

.fact-hpv__content p {
   font-size: 1.9em;
   font-weight: 400;
   line-height: 1.5;
   letter-spacing: -0.02em;
   color: var(--natural-100);
}
.fact-hpv__content p b {
   color: var(--primary-500);
}

.hpv-card-1 {
   width: 45%;
   aspect-ratio: 440 / 296;
}

.hpv-card-2 {
   width: 30%;
   aspect-ratio: 294 / 348;
}

.hpv-card-3 {
   width: 50%;
   aspect-ratio: 481 / 319;
}

.hpv-card-4 {
   width: 27%;
   aspect-ratio: 260 / 266;
}

.opacity-card {
   width: 45%;
   position: relative;
   opacity: 0.32;
   aspect-ratio: 433 / 288;
}

.overlay-card {
   position: absolute;
   top: 0;
   left: 0;
   width: 100%;
   height: 100%;
   border-radius: 2em;
   background: linear-gradient(90deg, rgba(217, 217, 217, 0) 29.95%, #737373 100%);
   opacity: 0.4;
}

.fact-hpv__bottom .overlay-card {
   background: linear-gradient(90deg, rgba(217, 217, 217, 0) 34.61%, #73737357 100%);
}

.hpv-card-3 .fact-hpv__content {
   display: flex;
   flex-direction: column;
   align-items: center;
}

.opacity-card .fact-hpv__content h4,
.opacity-card .fact-hpv__content p {
   color: #ffffff;
}

.blur-card {
   width: 100%;
   visibility: visible;
}

.btn-cta {
   display: block;
   width: fit-content;
   transition: all 250ms ease-in-out;
   text-transform: uppercase;
   background-color: var(--third-500);
   color: var(--neutral-900);
   margin-top: 1em;
   font-size: 1.2em;
   padding: 1em 2em;
   border: 1px solid var(--neutral-900);
   border-radius: 3.2em;
   font-weight: 500;
   line-height: 1.2;
   text-align: center;
   cursor: pointer;
   box-shadow: 0px 4px 4px 0px #00000040;
   min-width: 19em;
}

.btn-cta:hover {
   background-color: var(--third-700);
   background-position: 102% 0;
   transform: translate3d(0, -2px, 0);
   transition: all 0.4s ease-in-out;
}

.warpper-fact {
   display: none;
   flex-direction: column;
   align-items: center;
}

.tabs {
   display: flex;
   position: relative;
   gap: 2em;
   font-size: 0.65em;
   transform: translateY(5em);
   z-index: 1;
}

.tab {
   cursor: pointer;
   display: inline-block;
   border-radius: 3px 3px 0px 0px;
   border-radius: 50%;
   transition: 0.2s linear;
}

.tab .fact-hpv__icon {
   position: relative;
   left: 0;
   top: 0;
   transform: none;
}

.panel .btn-cta {
   margin: 2em auto 1em;
}

.panels {
   min-height: 160px;
   width: 100%;
   max-width: 500px;
   overflow: hidden;
   backdrop-filter: blur(30px);
   display: flex;
   flex-direction: column;
   align-items: center;
   border-radius: 2em;
   padding: 8em 2em 4em;
   border: solid 5px #ffffff;
   border-radius: 2em;
   box-shadow: 0px 1px 3px 0px #0000004D;
   box-shadow: 3px 4px 7.5px 4px #00000017;
   background: linear-gradient(180deg, rgba(216, 244, 238, 0.83) 0%, rgba(186, 234, 245, 0.83) 100%);
}

.panel {
   display: none;
   animation: fadein .8s;
}

@keyframes fadein {
   from {
      opacity: 0;
   }

   to {
      opacity: 1;
   }
}

.panel-title {
   font-size: 1.5em;
   font-weight: bold
}

.radio {
   display: none;
}

#one:checked~.panels #one-panel,
#two:checked~.panels #two-panel,
#three:checked~.panels #three-panel,
#four:checked~.panels #four-panel {
   display: block
}

#one:checked~.tabs #one-tab,
#two:checked~.tabs #two-tab,
#three:checked~.tabs #three-tab,
#four:checked~.tabs #four-tab {
   transform: scale(1.7);
   margin: 0 3em;
}

.warpper-fact .navigation {
   display: flex;
   justify-content: space-between;
   width: 100%;
   max-width: 500px;
   position: absolute;
   top: 40%;
   transform: translateY(-50%);
}

.warpper-fact .btn-tab img {
   width: 5.8em;
   aspect-ratio: 24/28;
   border: none;
   cursor: pointer;
}

#prev-btn {
   transform: rotate(180deg);
}

/* Reponsive fact */
@media only screen and (min-width: 1400px) and (max-width: 1599px) {
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
   .fast-action-controls {
      display: none;
   }
   .body-main-content .footer__content {
      display: flex;
  }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
   .fact-hpv {
      font-size: 1vw;
   }
   .fast-action-controls {
      display: none;
   }
   .body-main-content .footer__content {
      display: flex;
  }
}

@media (min-width: 576px) and (max-width: 768px) {
   .fast-action-controls {
      display: none;
   }
   .body-main-content .footer__content {
      display: flex;
  }
}

@media (max-width: 575px) {
   .fact-hpv {
      font-size: 1.2vw;
   }

   .panels {
      min-height: 130px;
      padding: 6em 2em 1em;
      font-size: 1.2em;
   }

   .fact-hpv__group {
      transform: translateY(-5em);
   }

   .fact-hpv__top {
      display: none;
      width: inherit;
      justify-content: center;
      align-items: flex-end;
      gap: 2em;
      transform: translateX(0);
   }

   .fact-hpv__bottom {
      display: none;
      justify-content: center;
      align-items: flex-start;
      gap: 2em;
      transform: translateX(0);
      flex-direction: row-reverse;
   }

   .hpv-card-1 {
      width: 55%;
   }

   .hpv-card-2 {
      width: 40%;
   }

   .hpv-card-3 {
      width: 55%;
   }

   .hpv-card-4 {
      width: 40%;
   }

   .opacity-card {
      display: none;
      width: 45%;
      position: relative;
      opacity: 0.3;
   }

   .warpper-fact {
      display: flex;
      width: 90%;
      margin: 0 auto;
   }

   .warpper-fact .btn-cta {
      font-size: 2.4em;
      margin-top: 1.5em;
   }
   .body-main-content .footer__content {
      display: flex;
      font-size: 2vw;
  }
}

@media (min-width: 319px) and (max-width: 376px) {
   
}

/* SECTION - HPV-Diseases */
.common-hpv-diseases {
   position: relative;
   padding-bottom: 3em;
   width: 100%;
   font-size: 0.625vw;
   z-index: 0;
}

.common-hpv-diseases__wrapper {
   height: inherit;
   display: flex;
   position: relative;
   flex-direction: column;
   z-index: 0;
}

.common-hpv-diseases::before {
   content: '';
   position: absolute;
   top: 0;
   left: 0;
   mask-image: linear-gradient(168.15deg, rgba(217, 217, 217, 0) 71.35%, #737373 112.31%);
   mask-size: 100% 100%;
   mask-repeat: no-repeat;
   width: 100%;
   height: 100%;
   background: linear-gradient(291.33deg, #009585 -25.46%, #00CFA9 46.97%);
   z-index: -1;
}

.common-hpv-diseases__title {
   display: flex;
   flex-direction: column;
   align-items: center;
   gap: 1.6em;
   position: relative;
}

.title-main,
.title-highlight {
   font-size: 2.5em;
   font-weight: 600;
}

.title-highlight {
   background: linear-gradient(90deg, #00D6E2 0%, #009585 100%);
   border-radius: 50px;
   padding: 0.2em 1.5em;
   color: white;
}

.title-classify {
   display: flex;
   flex-direction: row;
   justify-content: space-between;
   gap: 4em;
}

.title-classify__tag {
   font-size: 1.5em;
   display: flex;
   flex-direction: row;
   gap: 0.75em;
   font-weight: 400;
   color: #707a73;
   padding: 0.5em 1em;
   border-radius: 50px;
   background-color: transparent;
   overflow: hidden;
   cursor: pointer;
   position: relative;
   transition: color 0.3s ease;
}

.title-classify__tag::before {
   content: '';
   position: absolute;
   top: 0;
   left: 0;
   width: 0;
   height: 100%;
   border-radius: 50px;
   background-color: currentColor;
   z-index: -1;
   transition: color 0.3s ease, width 0.3s ease;
}

.title-classify__tag.active::before {
   width: 100%;
}

.male-tag.active {
   color: #ffffff;
}

.male-tag.active::before {
   background-color: #005CFF;
}

.female-tag.active {
   color: #000000;
}

.female-tag.active::before {
   background-color: #ffdf1b;
}

.both-genders-tag.active {
   color: #000000;
}

.both-genders-tag.active::before {
   background-color: #009585;
}

.title-classify__icon {
   color: #FFFFFF;
   position: relative;
   z-index: 1;
   transition: transform 0.3s ease;
}

.title-classify__tag.active .title-classify__icon {
   transform: rotate(45deg);
}

.title-classify__icon::before {
   content: '';
   position: absolute;
   background: #D9D9D94D;
   top: 50%;
   left: 50%;
   transform: translate(-50%, -50%);
   border-radius: 50%;
   width: 1.5em;
   height: 1.5em;
   z-index: 0;
}

.common-hpv-diseases__contents {
   display: flex;
   flex-direction: row;
   justify-content: space-between;
   position: relative;
   /* overflow: hidden; */
}

.common-hpv-diseases__areas {
   width: 30%;
}

.common-hpv-diseases__areas.area-left,
.common-hpv-diseases__areas.area-right {
   display: flex;
   flex-direction: column;
   gap: 2.5em;
}

.common-hpv-diseases__statistics {
   display: flex;
   flex-direction: column;
   gap: 2.5em;
}

.common-hpv-diseases__areas:not(.area-left, .area-right) {
   position: relative;
}

.common-hpv-diseases__image {
   filter: grayscale(1) blur(1px);
   /* transition: filter 0.3s ease-in-out, transform 0.2s ease-in-out; */
   transition: 0.15s linear;
   cursor: pointer;
}

.common-hpv-diseases__image.male {
   position: relative;
   z-index: 1;
   aspect-ratio: 2169 / 6207;
   width: 15em;
   left: 10%;
}

.common-hpv-diseases__image.female {
   position: absolute;
   top: 8%;
   left: 45%;
   z-index: 2;
   aspect-ratio: 3074 / 6020;
   width: 21em;
}

.common-hpv-diseases__image.male.active {
   filter: grayscale(0) blur(0);
   transform: scale(1.02);
   z-index: 3;
}

.common-hpv-diseases__image.female.active {
   filter: grayscale(0) blur(0);
   transform: scale(1.02);
   z-index: 4;
}

.common-hpv-diseases__image:not(.active):hover {
   transform: scale(1.02);
}

.common-hpv-diseases__statistic {
   position: relative;
   padding: 1.5em 0;
   overflow: hidden;
   border-radius: 0 80px 80px 0;
   color: #adadad;
   cursor: pointer;
   transition: color 0.3s ease;
}

.area-left .common-hpv-diseases__disease-percent {
   text-align: right;
   margin-right: 1em;
   z-index: 1;
   position: relative;
}

.area-left .common-hpv-diseases__disease-name {
   text-align: right;
   margin-right: 3em;
   z-index: 1;
   position: relative;
}

.common-hpv-diseases__statistic.active {
   color: #0D867A;
}
.area-left .common-hpv-diseases__statistic.active .common-hpv-diseases__disease-percent{
   text-shadow: 2px 0 #fff, -2px 0 #fff, 0 2px #fff, 0 -2px #fff,
             1px 1px #fff, -1px -1px #fff, 1px -1px #fff, -1px 1px #fff;

}
.common-hpv-diseases__statistic.statistic-both-genders.active.male-color .common-hpv-diseases__disease-percent{
   text-shadow: 2px 0 #fff, -2px 0 #fff, 0 2px #fff, 0 -2px #fff,
   1px 1px #fff, -1px -1px #fff, 1px -1px #fff, -1px 1px #fff;
}
.common-hpv-diseases__statistic.statistic-both-genders.active .common-hpv-diseases__disease-percent{
   text-shadow: 2px 0 #fff, -2px 0 #fff, 0 2px #fff, 0 -2px #fff,
   1px 1px #fff, -1px -1px #fff, 1px -1px #fff, -1px 1px #fff;
}
.common-hpv-diseases__statistic.statistic-female.active {
   /* color: var(--secondary-500); */
   color: #ffdf1b;
} 
.common-hpv-diseases__statistic.statistic-male.active .common-hpv-diseases__disease-percent {
   color: var(--third-500);
   text-shadow: 2px 0 #fff, -2px 0 #fff, 0 2px #fff, 0 -2px #fff,
             1px 1px #fff, -1px -1px #fff, 1px -1px #fff, -1px 1px #fff;
}
.common-hpv-diseases__statistic.statistic-female.active .common-hpv-diseases__disease-name {
   color: #39514D;
}
.common-hpv-diseases__statistic.active .common-hpv-diseases__disease-name {
   color: #39514D;
}
.male-color .common-hpv-diseases__disease-percent {
   color: var(--third-500) !important;
}

.female-color .common-hpv-diseases__disease-percent {
   color: #ffdf1b !important;
   text-shadow: 2px 0 #fff, -2px 0 #fff, 0 2px #fff, 0 -2px #fff,
             1px 1px #fff, -1px -1px #fff, 1px -1px #fff, -1px 1px #fff;
}

.common-hpv-diseases__statistic::before {
   content: '';
   position: absolute;
   /* background: linear-gradient(90deg, rgba(255, 255, 255, 0.46) 0%, rgba(255, 255, 255, 0.092) 100%); */
   background: linear-gradient(90deg, rgb(255 255 255 / 64%) 0%, rgb(255 255 255 / 29%) 100%);
   transform: rotate(180deg);
   top: -10%;
   width: 100%;
   height: 120%;
   z-index: 0;
}

.area-right .common-hpv-diseases__statistic {
   padding: 1.5em 0;
   border-radius: 80px 0 0 80px;
}

.area-right .common-hpv-diseases__statistic::before {
   transform: rotate(0);
}

.area-right .common-hpv-diseases__disease-name {
   position: relative;
   z-index: 1;
   margin-left: 3em;
}

.area-right .common-hpv-diseases__disease-percent {
   position: relative;
   z-index: 1;
   margin-left: 1em;
}

.common-hpv-diseases__disease-percent {
   font-size: 4.5em;
   line-height: 1.2;
   font-weight: 800;
}

.common-hpv-diseases__disease-name {
   font-size: 1.5em;
   font-weight: 500;
}

.common-hpv-diseases__button {
   align-self: center;
}

.common-hpv-diseases__buttons.mobile {
   display: none;
}

@media (min-width: 319px) and (max-width: 376px) {
   .common-hpv-diseases {
      font-size: 0.45rem !important;
   }

   .common-hpv-diseases__areas.area-right,
   .common-hpv-diseases__areas.area-left {
      width: 35% !important;
   }
}

@media (max-width: 575px) {
   .common-hpv-diseases {
      font-size: 0.55rem;
   }

   .common-hpv-diseases__areas:not(.area-right, .area-left) {
      width: 75%;
      position: absolute;
      left: 61%;
      top: 50%;
      transform: translate(-50%, -50%);
      z-index: 1;
   }

   .common-hpv-diseases__contents {
      padding: 15em 0;
   }

   .common-hpv-diseases__areas.area-right,
   .common-hpv-diseases__areas.area-left {
      position: relative;
      justify-content: center;
      width: 34%;
   }

   .common-hpv-diseases__title {
      font-size: 1.2em;
   }

   .common-hpv-diseases__areas.area-right {
      right: 0;
   }

   .common-hpv-diseases__image.male {
      width: 22em;
      left: 0;
   }

   .common-hpv-diseases__image.female {
      left: 31%;
      top: 9%;
      width: 30em;
   }

   .common-hpv-diseases__buttons.mobile {
      font-size: 1.5em;
      display: flex;
      flex-direction: row;
      gap: 2em;
      align-self: center;
   }

   .common-hpv-diseases__button {
      display: none;
   }
   .btn-cta {
      font-size: 1.4em;
      line-height: 1.4;
      padding: 1.2em 1em;
      min-width: 17em;
  }
}

@media (min-width: 576px) and (max-width: 768px) {
   .common-hpv-diseases {
      font-size: 0.75rem;
   }

   .common-hpv-diseases__areas:not(.area-right, .area-left) {
      width: 60%;
      position: absolute;
      left: 60%;
      top: 50%;
      transform: translate(-50%, -50%);
      z-index: 1;
   }

   .common-hpv-diseases__contents {
      padding: 20em 0;
   }

   .common-hpv-diseases__areas.area-right,
   .common-hpv-diseases__areas.area-left {
      position: relative;
      justify-content: center;
      width: 30%;
   }

   .common-hpv-diseases__image.male {
      left: 0;
      width: 25em;
   }

   .common-hpv-diseases__image.female {
      left: 30%;
      width: 35em;
   }

   .common-hpv-diseases__areas.area-right {
      right: 0;
   }

   .common-hpv-diseases__buttons.mobile {
      font-size: 1.5em;
      display: flex;
      flex-direction: row;
      gap: 5em;
      align-self: center;
   }

   .common-hpv-diseases__button {
      display: none;
   }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
   .common-hpv-diseases {
      font-size: 0.88174273858rem;
   }

   .common-hpv-diseases__image.male {
      left: 15%;
   }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
   .common-hpv-diseases__areas:not(.area-left, .area-right) {
      width: 40%;
   }

   .common-hpv-diseases__image.male {
      left: 5%;
   }

   .common-hpv-diseases__image.female {
      left: 40%;
   }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
   .common-hpv-diseases {
      position: relative;
      padding: 2rem 0;
      width: 100%;
      font-size: 0.82rem;
      z-index: 0;
   }

   .common-hpv-diseases__image.male {
      left: -5%;
   }

   .common-hpv-diseases__image.female {
      left: 40%;
   }

   .common-hpv-diseases__contents {
      padding: 4em 0;
   }

   .common-hpv-diseases__areas:not(.area-right, .area-left) {
      width: 30%;
      position: absolute;
      left: 52%;
      top: 50%;
      transform: translate(-50%, -50%);
      z-index: 1;
      font-size: 1.2em;
   }

   .common-hpv-diseases__areas.area-right,
   .common-hpv-diseases__areas.area-left {
      position: relative;
      justify-content: center;
      width: 30%;
   }

   .common-hpv-diseases__button {
      align-self: center;
      font-size: 1.2em;
   }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
   .common-hpv-diseases {
      position: relative;
      padding: 2rem 0;
      width: 100%;
      font-size: 0.9rem;
      z-index: 0;
   }

   .common-hpv-diseases__image.male {
      left: 0;
   }

   .common-hpv-diseases__image.female {
      left: 42%
   }
}

/* END - SECTION - HPV-Diseases */

/* SECTION - Infection */
.hpv-infection-paths {
   overflow: hidden;
   padding: 7svh 3rem;
   background-color: #a1eee9;
   background-image: 
   linear-gradient(135deg,
         #a1eee9 0%,
         #bae5e6 48%,
         #71a6e1 96%);
   font-size: 0.625vw;      
}

.hpv-infection-paths__wrapper {
   position: relative;
}

.hpv-infection-paths__model {
   width: 55%;
   height: 100%;
   aspect-ratio: 582 / 715;
}

.hpv-infection-paths__mina {
   position: relative;
   z-index: 2;
   max-width: 48em;
   width: 100%;
   margin-bottom: 20.5%;
   margin-top: 6%;
   aspect-ratio: 582 / 715;
}

.hpv-infection-paths__circles {
   width: 100%;
   max-width: 103em;
   aspect-ratio: 1044 / 849;
   position: absolute;
   z-index: 0;
   top: -21.5%;
   left: -2%;
}

.hpv-infection-paths__title {
   position: absolute;
   bottom: -7%;
   font-size: 1.6em;
}

.hpv-infection-paths__title--main {
   font-weight: 400;
   font-size: 1em;
   line-height: 1.8;
}

.hpv-infection-paths__title--highlight {
   font-size: 2em;
   line-height: 1.5;
   color: var(--primary-500);
}

.hpv-infection-paths__contents {
   width: 100%;
   height: 100%;
}

.hpv-infection-paths__content-box {
   position: absolute;
   display: flex;
   flex-direction: row;
   gap: 4em;
   align-items: center;
}

.hpv-infection-paths__content-box::after {
   content: "";
    position: absolute;
    top: -1em;
    left: 0;
    width: 40em;
    height: 9em;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.31) 0%, rgba(255, 255, 255, 0) 100%);
    border-radius: 1.6em;
}

.hpv-infection-paths__content-box:nth-child(1) {
   top: 5em;
   left: 61em;
}

.hpv-infection-paths__content-box:nth-child(2) {
   top: 26em;
   left: 69em;
}

.hpv-infection-paths__content-box:nth-child(3) {
   top: 47em;
   left: 70em;
}

.hpv-infection-paths__content-box:nth-child(4) {
   top: 67em;
   left: 65em;
}

.hpv-infection-paths__contents .content-box__images {
   width: 12em;
   height: 100%;
   z-index: 1;
   display: flex;
   justify-content: center;
   align-items: center;
}

.hpv-infection-paths__contents .content-box__image {
   height: inherit;
   position: relative;
   object-fit: cover;
   width: 100%;
}

.hpv-infection-paths__contents .content-box__circle {
   content: "";
   position: absolute;
   background: #e0f5f3;
   width: 15em;
   height: 15em;
   border-radius: 50%;
   background: #e0f5f3;
   z-index: 0;
}

.content-box__description {
   padding: 0;
   width: 100%;
   font-size: 1.3em;
   font-weight: 400;
   align-content: center;
}

.content-box__description p {
   display: inline-block;
   font-weight: 200;
}
@media only screen and (min-width: 1599px) {
   .hpv-infection-paths__title--highlight {
      font-size: 2em;
   }

   .hpv-infection-paths__circles {
      max-width: 95em;
   }

   .hpv-infection-paths__mina {
      max-width: 54em;
   }
   .hpv-infection-paths__content-box:nth-child(1) {
      top: 5%;
      left: 55%;
   }  
   .hpv-infection-paths__content-box:nth-child(2) {
      top: 31%;
      left: 64%;
   }
   .hpv-infection-paths__content-box:nth-child(3) {
      top: 61%;
      left: 68%;
   }
   .hpv-infection-paths__content-box:nth-child(4) {
      top: 87%;
      left: 61%;
   }
   .hpv-infection-paths__contents .content-box__circle {
      width: 13em;
      height: 13em;
   }
   .content-box__description {
      font-size: 1.4em;
   }
}
@media only screen and (min-width: 1400px) and (max-width: 1599px) {
   .hpv-infection-paths__title--highlight {
      font-size: 2em;
   }

   .hpv-infection-paths__circles {
      max-width: 95em;
   }

   .hpv-infection-paths__mina {
      max-width: 54em;
   }
   .hpv-infection-paths__content-box:nth-child(1) {
      top: 5%;
      left: 55%;
   }  
   .hpv-infection-paths__content-box:nth-child(2) {
      top: 31%;
      left: 64%;
   }
   .hpv-infection-paths__content-box:nth-child(3) {
      top: 61%;
      left: 68%;
   }
   .hpv-infection-paths__content-box:nth-child(4) {
      top: 87%;
      left: 61%;
   }
   .hpv-infection-paths__contents .content-box__circle {
      width: 13em;
      height: 13em;
   }
   .content-box__description {
      font-size: 1.4em;
   }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .hpv-infection-paths__title--highlight {
      font-size: 2em;
   }
   .hpv-infection-paths__circles {
      max-width: 95em;
   }
   .hpv-infection-paths__mina {
      max-width: 48em;
   }
   .hpv-infection-paths__content-box:nth-child(1) {
      top: 11%;
      left: 54%;
   }  
   .hpv-infection-paths__content-box:nth-child(2) {
      top: 35%;
      left: 63%;
   }
   .hpv-infection-paths__content-box:nth-child(3) {
      top: 62%;
      left: 65%;
   }
   .hpv-infection-paths__content-box:nth-child(4) {
      top: 87%;
      left: 61%;
   }
   .hpv-infection-paths__contents .content-box__circle {
      width: 10em;
      height: 10em;
   }
   .content-box__description {
      font-size: 1.4em;
   }
   .hpv-infection-paths__contents .content-box__images {
      width: 9em;
   }
   .hpv-infection-paths__content-box::after {
      top: -1em;
      height: 9em;
   }
}
@media only screen and (min-width: 1100px) and (max-width: 1199px) {
   .hpv-infection-paths {
      font-size: 1vw;
   }
   .hpv-infection-paths__title--highlight {
      font-size: 2em;
   }
   .hpv-infection-paths__circles {
      max-width: 95em;
   }
   .hpv-infection-paths__mina {
      max-width: 48em;
   }
   .hpv-infection-paths__content-box:nth-child(1) {
      top: 11%;
      left: 54%;
   }  
   .hpv-infection-paths__content-box:nth-child(2) {
      top: 35%;
      left: 63%;
   }
   .hpv-infection-paths__content-box:nth-child(3) {
      top: 62%;
      left: 65%;
   }
   .hpv-infection-paths__content-box:nth-child(4) {
      top: 87%;
      left: 61%;
   }
   .hpv-infection-paths__contents .content-box__circle {
      width: 10em;
      height: 10em;
   }
   .content-box__description {
      font-size: 1.4em;
   }
   .hpv-infection-paths__contents .content-box__images {
      width: 9em;
   }
   .hpv-infection-paths__content-box::after {
      top: -1em;
      height: 9em;
   }
}
@media only screen and (min-width: 992px) and (max-width: 1099px) {
   .hpv-infection-paths {
    font-size: 1vw;
   }
    .hpv-infection-paths__title--highlight {
      font-size: 2em;
   }
   .hpv-infection-paths__circles {
      max-width: 88em;
   }
   .hpv-infection-paths__mina {
      max-width: 50em;
   }
   .hpv-infection-paths__content-box:nth-child(1) {
      top: 11%;
      left: 54%;
   }  
   .hpv-infection-paths__content-box:nth-child(2) {
      top: 35%;
      left: 63%;
   }
   .hpv-infection-paths__content-box:nth-child(3) {
      top: 62%;
      left: 65%;
   }
   .hpv-infection-paths__content-box:nth-child(4) {
      top: 87%;
      left: 61%;
   }
   .hpv-infection-paths__contents .content-box__circle {
      width: 10em;
      height: 10em;
   }
   .content-box__description {
      font-size: 1.4em;
   }
   .hpv-infection-paths__contents .content-box__images {
      width: 9em;
   }
   .hpv-infection-paths__content-box::after {
      top: -1em;
      height: 8em;
   }
}
@media only screen and (min-width: 769px) and (max-width: 991px) {
   .hpv-infection-paths {
    font-size: 1vw;
   }

   .hpv-infection-paths__content-box {
      gap: 4em;
      width: 100%;
   }

   .hpv-infection-paths__mina {
      width: 60em;
   }

   .hpv-infection-paths__circles {
      width: 85em;
      left: -6%;
   }

   .hpv-infection-paths__contents .content-box__images {
      width: 9em;
   }

   .hpv-infection-paths__contents .content-box__circle {
      width: 12em;
      height: 12em;
   }

   .hpv-infection-paths__content-box::after {
      left: 2rem;
   }

   .hpv-infection-paths__content-box:nth-child(1) {
      top: 10em;
      left: 44em;
   }

   .hpv-infection-paths__content-box:nth-child(2) {
      top: 30em;
      left: 51em;
   }

   .hpv-infection-paths__content-box:nth-child(3) {
      top: 50em;
      left: 52em;
   }

   .hpv-infection-paths__content-box:nth-child(4) {
      top: 68em;
      left: 48em;
   }

   .content-box__description {
      font-size: 1.8em;
   }

   .hpv-infection-paths__title {
      display: flex;
      position: unset;
      justify-content: center;
      text-align: center;
      align-items: center;
    }
   .hpv-infection-paths__wrapper {
      display: flex;
      flex-direction: column;
   }
}
@media (min-width: 576px) and (max-width: 768px) {
   .hpv-infection-paths {
      font-size: 0.9vw;
   }
   .hpv-infection-paths__title {
      font-size: 1.1em;
   }
   .hpv-infection-paths__model {
      width: fit-content;
      height: auto;
   }
   .hpv-infection-paths__mina {
      max-width: 43em;
   }

   .content-box__circle::before {
      content: "";
      position: absolute;
      top: 0;
      left: 10%;
      background: linear-gradient(90deg,
            rgba(255, 255, 255, 0.31) 0%,
            rgba(255, 255, 255, 0) 100%);
   }

   .hpv-infection-paths__circles {
      left: 0%;
        top: -13%;
        width: 74em;
   }

   .hpv-infection-paths__contents .content-box__images {
      padding: 1.5em;
      width: 11em;
   }

   .hpv-infection-paths__contents .content-box__circle {
      width: 10em;
      height: 10em;
   }

   .hpv-infection-paths__content-box {
      gap: 1em;
      width: 100%;
   }

   .content-box__description {
      font-size: 1.6em;
   }

   .hpv-infection-paths__content-box::after {
      left: 5%;
      top: 0;
      height: 100%;
   }

   .hpv-infection-paths__content-box:nth-child(1) {
      top: 0;
      left: 43em;
   }

   .hpv-infection-paths__content-box:nth-child(2) {
      top: 15em;
      left: 52em;
   }

   .hpv-infection-paths__content-box:nth-child(3) {
      top: 31em;
      left: 53em;
   }

   .hpv-infection-paths__content-box:nth-child(4) {
      top: 45em;
      left: 48em;
   }
   .hpv-infection-paths__title {
      display: flex;
      position: unset;
      justify-content: center;
      text-align: center;
      align-items: center;
      margin-top: 6em;
    }
}
@media (max-width: 575px) {
   .hpv-infection-paths {
      padding: 4em 0;
      font-size: 1.9vw;
  }
   .hpv-infection-paths__mina {
      width: 20em;
      margin-top: 21%;
   }
   .hpv-infection-paths__model {
      width: fit-content;
      height: auto;
   }
   .hpv-infection-paths__circles {
      left: -3em;
      top: 2.5em;
      width: 32em;
   }

   .content-box__description {
      font-size: 1.23em;
      padding-left: 0.3em;
      white-space: nowrap;

   }

   .hpv-infection-paths__contents .content-box__images {
      padding: 1.5em;
      width: 9em;
   }

   .hpv-infection-paths__contents .content-box__circle {
      width: 8em;
      height: 8em;
   }

   .hpv-infection-paths__contents .content-box__images {
      padding: 1.5em;
      width: 7em;
   }

   .hpv-infection-paths__contents .content-box__circle {
      width: 6.5em;
      height: 6.5em;
   }

   .content-box__circle::before {
      content: "";
      position: absolute;
      top: 0;
      left: 20%;
      border-radius: 1em;
      height: 6.5em;
      width: 20em;
      background: linear-gradient(90deg,
            rgba(255, 255, 255, 0.31) 0%,
            rgba(255, 255, 255, 0) 100%);
   }

   .hpv-infection-paths__content-box::after {
      height: 0;
   }

   .hpv-infection-paths__content-box:nth-child(1) {
      top: 4em;
      left: 15em;
   }

   .hpv-infection-paths__content-box:nth-child(2) {
      top: 11.5em;
      left: 18.5em;
   }

   .hpv-infection-paths__content-box:nth-child(3) {
      top: 19.5em;
      left: 18.5em;
   }

   .hpv-infection-paths__content-box:nth-child(4) {
      top: 27em;
      left: 16em;
   }
   .hpv-infection-paths__title {
      display: flex;
      position: unset;
      justify-content: center;
      text-align: center;
      align-items: center;
      margin-top: 23vw;
      font-size: 1em;
    }
}
@media (max-width: 374px) {
   .hpv-infection-paths__mina {
      width: 20em;
   }

   .hpv-infection-paths__circles {
      width: 25em;
   }

   .content-box__description {
      font-size: 0.7em;
   }

   .hpv-infection-paths__contents .content-box__images {
      padding: 1.5em;
      width: 6em;
   }

   .hpv-infection-paths__contents .content-box__circle {
      width: 5em;
      height: 5em;
   }

   .content-box__circle::before {
      height: 5em;
   }

   .hpv-infection-paths__content-box:nth-child(1) {
      top: 2em;
      left: 15em;
   }

   .hpv-infection-paths__content-box:nth-child(2) {
      top: 8em;
      left: 17em;
   }

   .hpv-infection-paths__content-box:nth-child(3) {
      top: 16em;
      left: 17em;
   }

   .hpv-infection-paths__content-box:nth-child(4) {
      top: 22em;
      left: 15em;
   }
}

/* SECTION - Preventative */
.hpv-preventative-methods {
   font-size: 0.625vw;
   padding-top: 10em;
   width: 100%;
   background: #c9eae6;
   width: 100%;
   overflow: hidden;
}

.hpv-preventative-methods__wrapper {
   width: 100%;
   height: inherit;
   display: flex;
   position: relative;
}

.hpv-preventative-methods__wrapper::before {
   content: '';
   position: absolute;
   top: 0;
   left: 0;
   mask-image: linear-gradient(162.67deg, rgba(217, 217, 217, 0) 43.47%, #737373 86.04%);
   mask-size: 100% 100%;
   mask-repeat: no-repeat;
   width: 100%;
   height: 100%;
   z-index: 0;
}

.hpv-preventative-methods__contents {
   width: 55%;
   font-size: 1em;
   margin-left: 4.5em;
   margin-bottom: 5em;
   display: flex;
   flex-direction: column;
   gap: 2em;
   z-index: 1;
}

.hpv-preventative-methods__title {
   font-weight: 700;
   font-size: 3em;
   color: #009585;
   line-height: 1.1;
}

.hpv-preventative-methods__ways {
   margin-left: 3.5em;
   display: flex;
   flex-direction: column;
   justify-content: space-between;
   align-items: stretch;
   gap: 4.5em;
   position: relative;
}

.line {
   display: block;
   position: absolute;
   width: 0.5em;
   left: 1.8em;
}

.hpv-preventative-methods__models {
   width: 55%;
   aspect-ratio: 667 / 561;
   position: absolute;
   right: 0;
   bottom: 0;
}

.hpv-preventative-methods__model {
   width: 100%;
   height: 100%;
   object-fit: cover;
}

.hpv-preventative-methods__way {
   font-size: 1em;
   height: 100%;
   display: flex;
   flex-direction: row;
   gap: 2em;
   align-items: center;
}

.hpv-preventative-way__icon-description {
   width: 4em;
   aspect-ratio: 1;
   position: relative;
   display: flex;
   align-items: center;
   justify-content: center;
}

.hpv-preventative-way__icon-circle {
   position: absolute;
   background: #FFFFFF99;
   width: 5.5em;
   height: 5.5em;
   border-radius: 50%;
   z-index: 0;
}

.hpv-preventative-way___icon {
   position: relative;
   z-index: 1;
   width: inherit;
   height: auto;
   object-fit: cover;
}

.hpv-preventative-way__description {
   font-size: 2em;
   align-content: center;
   font-weight: 400;
   color: #39514D;
}

.hpv-preventative-methods__buttons {
   display: flex;
   margin-left: 3.5em;
   align-items: center;
   gap: 2em;
}

.cta-secondary {
   background-color: #ffdf1b;
   color: var(--primary-500);
}

.cta-secondary:hover {
   background-color: var(--secondary-600);
}

@media (max-width: 575px) {
   .hpv-preventative-methods {
      font-size: 1vw;
   }

   .hpv-preventative-methods__contents {
      width: 100%;
      margin: 0;
      padding: 1em 0 5em;
   }

   .hpv-preventative-methods__models {
      width: 80%;
      position: relative;
      right: auto;
      bottom: auto;
      transform: translateX(-50%);
      left: 50%;
   }

   .hpv-preventative-methods__wrapper {
      height: inherit;
      display: flex;
      position: relative;
      flex-direction: column-reverse;
   }

   .hpv-preventative-methods__model {
      width: inherit;
      height: auto;
      aspect-ratio: unset;
   }

   .hpv-preventative-methods__title {
      display: flex;
      justify-content: center;
      align-items: center;
   }

   .hpv-preventative-methods__ways {
      margin: 0 auto;
   }

   .hpv-preventative-methods__buttons {
      margin: 0 auto;
      font-size: 1.3em;
   }

}

@media (min-width: 576px) and (max-width: 768px) {
   .hpv-preventative-methods {
      font-size: 0.7vw;
   }

   .hpv-preventative-methods__contents {
      width: 80%;
   }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
   .hpv-preventative-methods {
      font-size: 0.8vw;
   }

   .hpv-preventative-methods__contents {
      width: 80%;
   }
}

@media only screen and (min-width: 992px) and (max-width: 1099px) {
   .hpv-preventative-methods__contents {
      width: 80%;
   }
}

@media only screen and (min-width: 1100px) and (max-width: 1199px) {
   .hpv-preventative-methods {
      font-size: 0.55em;
   }

   .hpv-preventative-methods__contents {
      width: 80%;
   }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
   .hpv-preventative-methods {
      font-size: 0.625vw;
   }

   .hpv-preventative-methods__contents {
      width: 80%;
   }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
   .hpv-preventative-methods {
      font-size: 0.7em;
   }

   .hpv-preventative-methods__contents {
      width: 80%;
   }
}

/*  */
/* Section Article */
.article {
   padding-top: 20em;
   display: flex;
   justify-content: center;
   align-items: center;
   background: rgb(131, 212, 199);
   background: linear-gradient(0deg, rgba(131, 212, 199, 1) 0%, rgba(193, 229, 225, 1) 90%, rgba(255, 255, 255, 0) 100%);
   margin-top: -7em;
   z-index: 0;
   position: relative;
   font-size: 0.625vw;
}

section.article .header-section {
   font-size: 3.2em;
   font-weight: 600;
   line-height: 1.1;
   color: #39514D;
   text-align: center;
   margin-bottom: 0;
}

section.article .slide-article {
   padding: 5em 1em;
}

section.article .slide-article .article-item {
   border-radius: 2em;
   display: flex;
   flex-direction: column;
   overflow: hidden;
   text-align: center;
   font-weight: 400;
   position: relative;
   background: rgb(109, 212, 169);
   background: rgb(252, 254, 244);
   background: linear-gradient(150deg, rgb(252, 254, 244) 0%, rgb(158, 237, 241) 100%);
}

section.article .slide-article .article-item__title {
   text-transform: uppercase;
   font-size: 1em;
   padding: 0.8em 1em;
   color: var(--primary-800);
   line-height: 1.3;
   height: 6em;
}

section.article .slide-article .article-item__title span {
   display: -webkit-box;
   -webkit-box-orient: vertical;
   -webkit-line-clamp: 3;
   overflow: hidden;
   text-overflow: ellipsis;
   line-height: 1.5em;
}

section.article .slide-article .article-item__avatar {
   aspect-ratio: 2.8/2;
   overflow: hidden;
}

section.article .slide-article .article-item__avatar img {
   width: 100%;
   height: 100%;
   -o-object-fit: cover;
   object-fit: cover;
}

section.article .slide-article .article-item__des {
   display: none;
   -webkit-box-orient: vertical;
   -webkit-line-clamp: 3;
   overflow: hidden;
   text-overflow: ellipsis;
   line-height: 1.5em;
}

section.article .slide-article .article-item__action {
   position: absolute;
   margin: auto;
   left: 0;
   right: 0;
   bottom: 1.5em;
}

section.article .slide-article .article-item__action a {
   color: var(--primary-500);
   display: flex;
   flex-direction: row;
   width: -moz-fit-content;
   width: fit-content;
   align-items: center;
   gap: 1em;
   padding: 0.7em 2.2em;
   margin: auto;
   box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);
   background-color: var(--primary-100);
   color: var(--primary-800);
   cursor: pointer;
   border-radius: 1.5em;
   font-size: 1em;
   transition: all 0.3s ease-in;
}

section.article .slide-article .article-item__action a:hover {
   background-color: var(--primary-600);
   color: white;
}
@media (max-width: 991px) {
   section.article {
      font-size: 0.6rem;
      padding-top: 10em;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgb(131, 212, 199);
      background: linear-gradient(0deg, rgba(131, 212, 199, 1) 0%, rgba(193, 229, 225, 1) 99%, rgba(255, 255, 255, 0) 100%);
      margin-top: -4em;
      z-index: 2;
      position: relative;
   }

}

@media (max-width: 767px) {
   section.article {
      font-size: 0.6rem;
      padding-top: 10em;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgb(131, 212, 199);
      background: linear-gradient(0deg, rgba(131, 212, 199, 1) 0%, rgba(193, 229, 225, 1) 99%, rgba(255, 255, 255, 0) 100%);
      margin-top: -3em;
      z-index: 2;
      position: relative;
   }

   section.article .slide-article {
      padding: 2em 0;
   }

   section.article .slide-article .control-navigate-pagination {
      justify-content: center;
      width: 100%;
   }

}

.control-navigate-pagination {
   display: flex;
   position: relative;
   justify-content: center;
   width: -moz-fit-content;
   width: fit-content;
   margin: auto;
   gap: 2em;
   margin-top: 1.5em;
}

.control-navigate-pagination .swiper-pagination {
   bottom: -0.8%;
}

.control-navigate-pagination .swiper-pagination.bg-transparent {
   background-color: rgba(128, 128, 128, 0.3);
   border-radius: 2em;
   width: -moz-fit-content;
   width: fit-content;
   padding: 1em;
   bottom: -4em !important;
}

.control-navigate-pagination .swiper-pagination-bullet {
   width: 1.5em;
   height: 1.5em;
   background-color: white;
   opacity: 1;
}

.control-navigate-pagination .swiper-pagination-bullet-active {
   width: 4em;
   transition: width 0.5s;
   border-radius: 2em;
   background: #05A8AE;
   border: 1px solid transparent;
}

.control-navigate-pagination .swiper-pagination,
.control-navigate-pagination .swiper-button-prev,
.control-navigate-pagination .swiper-button-next {
   position: static;
   display: flex;
   align-items: center;
   justify-content: center;
   gap: 0.5em;
}

.control-navigate-pagination .swiper-button-prev,
.control-navigate-pagination .swiper-button-next {
   height: 4.5em;
   width: 4.5em;
   margin: 0;
   background-color: rgba(255, 255, 255, 0.32);
   padding: 1em !important;
   line-height: 2em;
   border-radius: 50%;
   color: #05A8AE;
   flex: none;
}

.control-navigate-pagination .swiper-button-prev::after,
.control-navigate-pagination .swiper-button-next::after {
   font-size: 1.5em !important;
   -webkit-text-stroke-width: 0.15em;
}

.control-navigate-pagination .swiper-button-prev:hover,
.control-navigate-pagination .swiper-button-next:hover {
   background-color: #05A8AE;
   color: white;
}

.control-navigate-pagination .btn-play,
.control-navigate-pagination .btn-pause {
   height: 2.5em;
   width: 2.5em;
   display: none;
   justify-content: center;
   background-color: #b8c9bb;
   border-radius: 2em;
   align-items: center;
   font-size: 1.5em;
   color: #05A8AE;
}

.control-navigate-pagination .btn-play.active,
.control-navigate-pagination .btn-pause.active {
   display: flex;
}

.control-navigate-pagination .btn-play svg {
   margin-right: -0.2em;
}

.swiper-wrapper {
   height: auto;
   font-size: 1vw;
}

.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right {
   background-image: none;
}

/* END - Section Article */

/* Footer Content Mobile */
.footer-main-content-mobile {
   font-size: 0.625vw;
}

.footer-main-content-male-page {
   background-color: #05A8AE !important;
   text-align: justify;
   padding: 10px;
   color: white;
   border-bottom-left-radius: var(--radius-md);
   border-bottom-right-radius: var(--radius-md);
 }
 .footer-main-content-male-page .content {
   margin: 2em auto 0 auto;
 }
 .footer-main-content-male-page .content b {
   font-size: 1.4em;
 }
 .footer-main-content-male-page .content ul {
   padding: 0;
   text-align: left;
   margin: auto;
 }
 .footer-main-content-male-page .content ul li {
   margin-bottom: 10px;
   font-size: 1.2em;
   overflow-wrap: break-word;
   display: list-item;
   list-style-type: decimal;
 }
 .footer-main-content-male-page .footer__content img {
   width: 7em;
   height: auto;
 }
 .footer-main-content-male-page .footer__content {
   display: none;
 }
 .footer-main-content-male-page .paragraph-2-col {
   -moz-column-count: 2;
        column-count: 2;
   -moz-column-gap: 3em;
        column-gap: 3em;
   padding: 0 2em;
 }
 
 @media (max-width: 767px) {
   .footer-main-content-male-page {
     font-size: 0.7rem;
   }
   .footer-main-content-male-page .content {
     padding: 0;
   }
 }
 @media (max-width: 1099px) {
   .footer-main-content-male-page .footer__content {
     display: flex;
   }
 }
 .footer-main-content-male-page.mobile {
   background-color: #05A8AE !important;
   text-align: justify;
   padding: 1em;
   color: white;
   border-bottom-left-radius: 0;
   border-bottom-right-radius: 0;
 }
 .footer-main-content-male-page.mobile .content {
   margin: 2em auto 0 auto;
 }
 .footer-main-content-male-page.mobile .content b {
   font-size: 1.4em;
 }
 .footer-main-content-male-page.mobile .content ul {
   padding: 0;
   text-align: left;
   margin: auto;
 }
 .footer-main-content-male-page.mobile .content ul li {
   margin-bottom: 10px;
   font-size: 1.2em;
   overflow-wrap: break-word;
   display: list-item;
   list-style-type: decimal;
 }
 .footer-main-content-male-page.mobile .footer__content img {
   width: 7em;
   height: auto;
 }
 .footer-main-content-male-page.mobile .footer__content {
   display: none;
 }
 .footer-main-content-male-page.mobile .paragraph-2-col {
   -moz-column-count: 2;
        column-count: 2;
   -moz-column-gap: 3em;
        column-gap: 3em;
   padding: 0 2em;
 }
 
 .footer-main-content-mobile .footer__content {
   font-size: 0.8em;
   background: var(--primary-500);
 }
 .footer-main-content-mobile .footer__content img {
   width: 7em;
   height: auto;
 }
 .footer-main-content-mobile .footer__content h3 {
   color: var(--natural-100);
 }
 .footer-main-content-mobile .footer__content .direction a {
   color: var(--natural-200);
 }
 .footer-main-content-mobile .container-footer {
   position: relative;
   max-width: 100%;
 }
 .footer-main-content-mobile .content-footer {
   max-height: 8em;
   overflow: hidden;
   transition: max-height 0.3s ease;
 }
 .footer-main-content-mobile .show-more {
   font-size: 1.5em;
   position: absolute;
   width: 100%;
   text-align: end;
   height: 2em;
   top: 0;
   right: 0;
   cursor: pointer;
   color: var(--natural-900);
   display: block;
   background-color: linear-gradient(to bottom, rgba(255, 255, 255, 0.4) 0%, rgb(255, 255, 255) 100%);
 }
 
 @media (max-width: 767px) {
   .footer-main-content-male-page {
     font-size: 0.7rem;
   }
   .footer-main-content-male-page .content {
     padding: 0;
   }
 }
 @media (max-width: 1099px) {
   .footer-main-content-male-page .footer__content {
     display: flex;
   }
 }


/* Fast action button */
.popover-modal-delay-change-page {
   position: fixed;
   left: 0;
   top: 0;
   right: 0;
   bottom: 0;
   z-index: 99;
   transition: all 250ms ease-in-out;
   font-size: 1.2rem;
   display: none;
 }
 .popover-modal-delay-change-page.active {
   display: block;
 }
 .popover-modal-delay-change-page.active .popover-content {
   transform: scale(1);
   opacity: 1;
 }
 .popover-modal-delay-change-page.active .overlay {
   opacity: 1;
 }
 .popover-modal-delay-change-page .overlay {
   position: absolute;
   z-index: 0;
   left: 0;
   top: 0;
   right: 0;
   bottom: 0;
   background-color: rgba(51, 51, 51, 0.5);
   opacity: 1;
   -webkit-backdrop-filter: blur(5px);
           backdrop-filter: blur(5px);
 }
 .popover-modal-delay-change-page .popover-content {
   z-index: 9;
   background-color: white;
   position: absolute;
   bottom: 4em;
   right: 6em;
   padding: 1.5em;
   border-radius: var(--radius-md);
 }
 
 .popover-modal-delay-change-page .ribbon-white {
   left: 0;
   top: -28px;
   width: 65%;
   height: 40px;
   border-top-left-radius: var(--radius-md);
   border-top-right-radius: var(--radius-md);
   background-color: white;
   position: absolute;
   z-index: -1;
 }
 .popover-modal-delay-change-page .title {
   display: flex;
   justify-content: space-between;
   align-items: center;
   margin-bottom: 0.5em;
   font-weight: 500;
   color: var(--neutral-200);
 }
 .popover-modal-delay-change-page .title__left {
   font-size: 2.1em;
 }
 .popover-modal-delay-change-page .title__right {
   font-size: 1.3em;
   font-weight: 400;
 }
 .popover-modal-delay-change-page .popover-content {
   border-bottom-left-radius: var(--radius-md);
   border-top-right-radius: var(--radius-md);
   border-bottom-right-radius: var(--radius-md);
   border-top-left-radius: 0;
   padding-top: 0;
   max-width: 65em;
 }
 .popover-modal-delay-change-page .content {
   background-color: var(--primary-800);
   color: white;
   padding: 1.6em;
   border-radius: var(--radius-sm);
   font-size: 1.3em;
   display: flex;
   flex-direction: column;
   gap: 1.5em;
   font-weight: 400;
 }
 .popover-modal-delay-change-page .content a {
   color: var(--third-400);
 }
 
 @media (max-width: 1099px) {
   .popover-modal-delay-change-page .title__left {
     font-size: 1.3em;
   }
   .popover-modal-delay-change-page .title__right {
     font-size: 0.8em;
   }
   .popover-modal-delay-change-page .content {
     font-size: 1em;
   }
   .popover-modal-delay-change-page .popover-content {
     bottom: 12em;
     left: 1em;
     right: 1em;
     margin: auto;
   }
 }
 