.root-container {
  background-position: 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px;
  background-image: radial-gradient(25% 70% at -1% 91%, #38ab9f 1%, #38ab9f 1%, rgba(7, 58, 255, 0) 91%), radial-gradient(30% 53% at 62% 163%, #73f2ff 0%, rgba(7, 58, 255, 0) 100%), linear-gradient(125deg, white 8%, #d3e9d6 44%, #ace0c8 93%);
}

.container-content .main-container .body-main-content {
  background-color: transparent;
}
.container-content .main-container .body-main-content::before {
  background-color: transparent;
}

.footer-main-content-mobile {
  display: none;
}

#footer-all {
  display: none;
}

@media (max-width: 1099px) {
  .container-content .main-container {
    max-height: calc(100svh - 175px);
    margin-top: 0.8em;
  }
}
/* @media (min-width: 1200px) {
  .container {
    max-width: 1200px;
  }
}
@media (min-width: 1800px) {
  .container {
    max-width: 1400px;
  }
} */
@media (max-width: 767px) {
  .container-content {
    background: #05a8ae;
  }
  .container-content .main-container {
    background-image: radial-gradient(25% 70% at -1% 91%, #38ab9f 1%, #38ab9f 1%, rgba(7, 58, 255, 0) 91%), radial-gradient(30% 53% at 62% 163%, #73f2ff 0%, rgba(7, 58, 255, 0) 100%), linear-gradient(125deg, white 8%, #d3e9d6 44%, #ace0c8 93%);
  }
}
@media ((max-width: 575px)) {
  .container-content {
    background: #05a8ae;
  }
  .container-content .main-container {
    background-image: radial-gradient(25% 70% at -1% 91%, #38ab9f 1%, #38ab9f 1%, rgba(7, 58, 255, 0) 91%), radial-gradient(30% 53% at 62% 163%, #73f2ff 0%, rgba(7, 58, 255, 0) 100%), linear-gradient(125deg, white 8%, #d3e9d6 44%, #ace0c8 93%);
  }
  .footer-main-content-male-page {
    display: none;
  }
  .footer-main-content-mobile {
    display: block;
  }
  .footer-main-content-male-page.mobile {
    display: block;
  }
}
section .header-section {
  display: flex;
  flex-direction: column;
  gap: 1em;
  margin-bottom: 2em;
}
section .subtitle-section {
  text-align: center;
  font-size: 2.5em;
  font-weight: 700;
  color: var(--third-500);
}
section .main-title-section {
  text-align: center;
  font-size: 4em;
  font-weight: 700;
  font-size: 4em;
  line-height: 1.2;
}

.content-banner {
  position: absolute;
  margin: 5vw 0 0 3vw;
  bottom: 12%;
  z-index: 1;
}

.male-banner-main {
  position: relative;
}

.title-img {
  display: block;
  width: 70em;
}

.btn-cta {
    background-color: #05A8AE;
    color: white;
    border-radius: 50px;
    text-align: center;
    font-size: 1.6em;
    font-weight: 700;
    padding: 5px 0;
    box-shadow: 0 4px 4px rgba(14, 55, 54, 0.6);
    cursor: pointer;
    border: 1px solid #FFFFFF;
    display: block;
    box-sizing: border-box;
    margin-top: 1em;
    width: -moz-fit-content;
    width: fit-content;
    padding: 0.4em 1.5em;
    transition: 0.3s linear;
}
.btn-cta:hover {
  background-color: var(--primary-600);
  color: white;
}

.banner-main__mobile {
  display: none;
}

.description-banner-wrapper {
  position: absolute;
  bottom: 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  font-size: 0.625vw;
  width: 100%;
  padding: 1em;
}

.description-banner-left {
  text-align: left;
  width: 50%;
  font-weight: 400;
  font-size: 0.8em;
  z-index: 1;
}

.description-banner-right {
  text-align: left;
  width: 37%;
  font-weight: 400;
  font-size: 0.8em;
  z-index: 1;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .content-banner {
    position: absolute;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {}
@media only screen and (min-width: 769px) and (max-width: 991px) {
  section.male-banner-main {
    font-size: 0.8rem;
  }
}
@media only screen and (max-width: 768px) {
  section.male-banner-main {
    font-size: 0.65rem;
  }
  .content-banner {
    position: absolute;
    font-size: 0.7em;
    bottom: 12%;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: left;
    width: 95%;
    left: 0;
    right: 0;
  }
  .content-banner img {
    width: 90%;
    height: auto;
  }
  .description-banner {
    text-align: left;
    bottom: 1.5em;
    font-size: 1.3em;
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  section.male-banner-main {
    font-size: 0.65rem;
  }
  .content-banner {
    position: absolute;
    width: 100%;
    margin: 2.5em;
  }
  .description-banner {
    text-align: left;
  }
  .btn-cta {
    font-size: 1.5vw;
  }
  .img-banner {
    border-radius: 2em;
    overflow: hidden;
    width: 100%;
    height: 52svh;
  }
  .img-banner picture {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .img-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
@media (max-width: 575px) {
  .description-banner {
    font-size: 0.85em;
    margin: auto;
    left: 0;
    right: 0;
  }
   .img-banner {
    border-radius: 2em;
    overflow: hidden;
    width: 100%;
    height: 54svh;
  }
  .img-banner picture {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .img-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .btn-cta {
    font-size: 1.5vw;
  }
  .description-banner-wrapper {
        padding: 1em 4em 2em 4em;
    }
}



.container-protection {
  align-items: center;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding-top: 30px;
  margin-bottom: -8em;
}

.protection {
  width: 100%;
  height: 100%;
}

.protection-body-img {
  max-width: 90%;
}

.protection-box {
  border-radius: 30px;
  background: rgb(225, 238, 254);
  background: linear-gradient(194deg, rgba(225, 238, 254, 0.73) 0%, rgba(57, 156, 249, 0.83) 100%);
  position: relative;
  padding: 6em 12em;
  top: -16em;
  -webkit-backdrop-filter: blur(6px);
          backdrop-filter: blur(6px);
  border: 1px solid #e0f5ff;
}

.protection-text-box {
  text-align: center;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.protection-text-box p {
  font-size: 3.5em;
  color: var(--third-900);
}

.protection-text-box h2 {
  font-size: 4em;
  color: var(--third-900);
  font-weight: 800;
  line-height: 1.3;
  margin: 0.5em;
  position: relative;
  width: -moz-fit-content;
  width: fit-content;
  margin: auto;
}

.protection-btn {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: space-between;
  margin-top: 3.4em;
  gap: 2em;
}

.protection-btn-left,
.protection-btn-right {
  flex: 1;
  box-sizing: border-box;
  background-color: #05A8AE;
  color: white;
  border-radius: 4em;
  text-align: center;
  font-size: 1.4em;
  font-weight: 700;
  padding: 0.6em 2em;
  box-shadow: 0 0.4em 0.4em rgba(14, 55, 54, 0.15);
  cursor: pointer;
  border: 1px solid #85dadd;
}
.protection-btn-left:hover,
.protection-btn-right:hover {
  background-color: var(--primary-600);
  color: white;
}

.quote-symbol-top {
  top: -0.2em;
  left: -0.55em;
}

.quote-symbol-bottom {
  bottom: -0.6em;
  right: -0.6em;
}

.quote-symbol-top,
.quote-symbol-bottom {
  font-size: 3em;
  color: rgb(116, 213, 166);
  font-weight: 600;
  position: absolute;
  z-index: -1;
  line-height: 1;
  height: 1em;
}

@media only screen and (max-width: 992px) {
  .container-protection {
    font-size: 0.85rem;
  }
  .container-protection .protection-box {
    width: 90%;
    padding: 3em 6em;
  }
}
@media (max-width: 768px) {
  .container-protection {
    font-size: 0.75rem;
  }
  .container-protection .protection-box {
    width: 100%;
    padding: 3em 4em;
    background: linear-gradient(178deg, rgba(255, 255, 255, 0.6705882353) 0%, rgba(255, 255, 255, 0) 70%);
    -webkit-backdrop-filter: blur(20px);
            backdrop-filter: blur(20px);
    border: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    top: -10em;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}
@media (max-width: 576px) {
  .container-protection {
    font-size: 0.55rem;
  }
  .container-protection .protection-text-box h2 {
    font-size: 3.2em;
    margin: 0.5em 1.5em;
  }
  .container-protection .protection-btn {
    flex-direction: column;
  }
  .container-protection .protection-btn .protection-btn-left,
  .container-protection .protection-btn .protection-btn-right {
    font-size: 2.2em;
    width: -moz-fit-content;
    width: fit-content;
    margin: auto;
    padding: 0.5em 4em;
  }
}
section.goal {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  position: relative;
  font-size: 1rem;
  padding: 5em 1em;
  max-width: 110em;
}
section.goal .title-goal {
  text-align: center;
  font-size: 2.5em;
  font-weight: 700;
  color: var(--third-500);
  position: absolute;
  left: 2em;
  top: 3em;
  font-size: 3em;
}
section.goal .title-slide {
  font-size: 2.5em;
  font-weight: 600;
  text-align: center;
  margin: auto;
  margin-bottom: 2em;
  word-wrap: break-word;
}
section.goal .title-slide br {
  display: none;
}
section.goal .swiper-container {
  width: 100%;
  height: 100%;
}
section.goal .swiper-pagination-bullet {
  width: 1.2em;
  height: 1.2em;
  background: var(--neutral-900);
  border: 1px solid var(--neutral-900);
  opacity: 1;
}
section.goal .swiper-pagination-bullet-active {
  width: 4em;
  transition: width 0.5s;
  border-radius: 5px;
  background: var(--primary-700);
  border: 1px solid transparent;
}
section.goal .boder-bg-slide {
  display: flex;
  /* make sure these links (inline by default) are block */
  width: 100%;
  height: 33%;
  position: absolute;
  background: rgba(255, 255, 255, 0.3294117647);
  color: var(--white);
  bottom: 0;
  box-shadow: rgba(5, 113, 193, 0.2509803922) 0px 5px 20px -6px inset, rgba(5, 113, 193, 0.2509803922) 0px 4px 6px -3px inset;
  border-radius: 6em;
  bottom: 11em;
}
section.goal .boder-bg-slide .slide-to-show {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 2em;
  margin: auto;
  z-index: 10;
  font-size: 1.6em;
  font-weight: 600;
  padding: 0.5em 4em;
  background: rgb(255, 255, 255);
  background: linear-gradient(270deg, rgba(255, 255, 255, 0.74) 0%, rgba(164, 233, 255, 0.7371323529) 40%);
  border-radius: 1em;
  display: flex;
  align-items: center;
  gap: 1em;
  height: 3.5em;
  width: -moz-fit-content;
  width: fit-content;
  padding: 1em 2em;
}
section.goal .swiper-container-goal {
  max-width: 100%;
  position: relative;
  overflow-y: hidden;
  padding-bottom: 6em;
}
section.goal .swiper-container-goal .swiper-wrapper {
  align-items: flex-end;
}
section.goal .swiper-container-goal .swiper-slide {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: 100%;
}
section.goal .swiper-container-goal .swiper-slide .content-think-area {
  width: 100%;
  height: -moz-fit-content;
  height: fit-content;
  position: absolute;
  display: flex;
  justify-content: center;
  z-index: 2;
  padding-top: 7em;
}
section.goal .swiper-container-goal .swiper-slide .content-think-area .pop-up-think {
  width: -moz-fit-content;
  width: fit-content;
  font-size: 2.5em;
  font-weight: 600;
  line-height: 1.4;
  height: -moz-fit-content;
  height: fit-content;
  text-align: center;
  text-wrap: nowrap;
  border: 1px solid #ffffff;
  background: rgb(229, 249, 255);
  background: linear-gradient(0deg, rgba(190, 240, 255, 0.41) 0%, rgba(255, 255, 255, 0.43) 90%);
  border-radius: 1.2em;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  padding: 0.6em 1.5em;
  margin-left: 5%;
}
section.goal .swiper-container-goal .swiper-slide .content-think-area .pop-up-think.left {
  margin-right: auto;
  margin-left: 2%;
  margin-top: 6%;
}
section.goal .swiper-container-goal .swiper-slide .content-think-area .pop-up-think.right {
  margin-right: 2%;
  margin-left: auto;
}
section.goal .swiper-container-goal .swiper-slide .img-swip-area {
  width: 73.5em;
  left: 0;
  transform: none;
  margin: auto;
  max-width: 100%;
}
section.goal .swiper-container-goal .swiper-slide.slide-6 .content-think-area {
  padding-top: 0em;
}
section.goal .swiper-container-goal .swiper-slide.slide-6 .pop-up-think.left {
  font-size: 1.8em;
  margin-top: 4%;
}
section.goal .swiper-container-goal .swiper-slide.slide-6 .pop-up-think.right {
  font-size: 2.6em;
  display: flex;
  flex-direction: column;
  padding: 1em;
  margin-right: 2%;
}
section.goal .swiper-container-goal .swiper-slide.slide-6 .pop-up-think.right span {
  font-size: 0.7em;
}
section.goal .swiper-container-goal .swiper-slide.slide-6 .pop-up-think.right .note {
  color: var(--third-500);
  font-size: 1.1em;
}
section.goal .swiper-container-goal .swiper-slide.slide-6 .pop-up-think.right .now-btn {
  background-color: #05A8AE;
  color: white;
  border-radius: 50px;
  text-align: center;
  font-size: 0.6em;
  font-weight: 700;
  padding: 5px 0;
  box-shadow: 0 4px 4px rgba(14, 55, 54, 0.6);
  cursor: pointer;
  border: 1px solid #FFFFFF;
  display: block;
  box-sizing: border-box;
  margin-top: 1em;
  padding: 0.6em 1.5em;
}
section.goal .swiper-container-goal .swiper-slide.slide-6 .pop-up-think.right .now-btn:hover {
  background-color: var(--primary-600);
  color: white;
}
section.goal .swiper-container-goal .swiper-slide.slide-6 .img-swip-area {
  width: 67.8em;
  left: 0;
  transform: none;
}
section.goal .swiper-container-goal:has(.slide-6) .slide-to-show {
  margin-bottom: 6em;
}

/* 
* <576:     xs
* 576-768:  sm
* 768-992:  md
* 992-1200: lg
* 1200 -1400: xl
* >= 1400:  xxl
*/
@media only screen and (max-width: 1099px) {
  section.goal .swiper-container-goal .swiper-slide .content-think-area .pop-up-think.left {
    margin-left: 0;
  }
  section.goal .swiper-container-goal .swiper-slide .content-think-area .pop-up-think.right {
    margin-right: 0;
  }
}
@media only screen and (max-width: 767px) {
  section.goal .title-slide {
    margin-bottom: 1em;
  }
  section.goal .title-slide br {
    display: block;
  }
  section.goal {
    font-size: 0.6rem;
  }
  section.goal .title-goal {
    width: 100%;
    text-align: center;
    left: 0;
  }
  section.goal .swiper-container-goal .swiper-slide .content-think-area {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1em;
    padding-top: 0;
    margin-bottom: 2em;
  }
  section.goal .swiper-container-goal .swiper-slide .content-think-area .pop-up-think {
    margin: auto !important;
  }
  section.goal .swiper-container-goal .swiper-slide .content-think-area .pop-up-think br {
    display: none;
  }
  section.goal .swiper-container-goal .swiper-slide .content-think-area .pop-up-think.slide-6 br {
    display: block;
  }
  section.goal .swiper-container-goal .swiper-slide.slide-6 .content-think-area .pop-up-think.left {
    display: none;
  }
  section.goal .swiper-container-goal .swiper-slide.slide-6 .content-think-area .pop-up-think.right .now-btn {
    margin-top: 0;
  }
  section.goal .swiper-container-goal:has(.slide-6) .slide-to-show {
    display: none;
  }
  section.goal .boder-bg-slide {
    height: 28%;
    border-radius: 4em;
  }
}
@media only screen and (max-width: 577px) {
  section.goal .title-slide {
    font-size: clamp(1rem, 1.7rem, 2.5rem);
  }
  section.goal .swiper-container-goal {
    font-size: 0.8rem;
  }
  section.goal .swiper-container-goal .swiper-slide.slide-1 .content-think-area br {
    display: block;
  }
  section.goal .swiper-container-goal .swiper-slide.slide-6 .content-think-area .pop-up-think.right {
    font-size: 1.9rem;
  }
}
section.result .slide-result {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 4em;
  margin-top: 1em;
}
section.result .card {
  display: flex;
  height: 60%;
  max-width: 100%;
  border-radius: 3em;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  color: black;
  box-shadow: 0 0.7em 2em rgba(0, 0, 0, 0.2);
  transition: 0.3s ease-out;
  background-color: hsl(0, 0%, 100%);
  background-image: radial-gradient(at 14% 84%, hsl(64, 75%, 61%) 0px, transparent 50%), radial-gradient(at 4% 3%, hsl(90, 67%, 86%) 0px, transparent 50%), radial-gradient(at 98% 2%, hsl(149, 50%, 92%) 0px, transparent 50%), radial-gradient(at 95% 88%, hsl(178, 50%, 54%) 0px, transparent 50%);
  background-size: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  border: 1px solid var(--neutral-900);
  padding-bottom: 2em;
  flex-direction: column;
  justify-content: space-between;
}
section.result .card .content-top {
  margin: 2em;
  transition: 0.3s;
  font-size: 1.2em;
  text-shadow: 0 2px 5px #ffffff;
  position: relative;
  z-index: 2;
}
section.result .card .content-top p {
  font-size: 1.35em;
  font-weight: 400;
  line-height: 1.2;
  width: 60%;
}
section.result .card .content-top p:first-child {
  margin-bottom: 0.8em;
}
section.result .card .content-top h4 {
  width: 82%;
  line-height: 1.1;
  font-size: 2em;
  margin: 0.3em 0;
}
section.result .card img {
  position: absolute;
  -o-object-fit: contain;
     object-fit: contain;
  bottom: 0;
  right: 0;
  opacity: 0.9;
  transition: opacity 0.2s ease-out;
}
section.result .card .img-bg-card1,
section.result .card .img-bg-card1 {
  z-index: -1;
}
section.result .card .img-bg-card1 {
  width: 63%;
  right: -3.5em;
  height: auto;
  display: block;
  max-height: 95%;
}
section.result .card .img-bg-card2 {
  right: -8%;
  height: 90%;
  display: none;
}
section.result .card .card-content {
  position: relative;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.7607843137);
  width: -moz-fit-content;
  width: fit-content;
  max-width: 90%;
  padding: 1em 2em;
  border-radius: 2em;
  border: 1px solid white;
  margin: 0 2em;
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
}
section.result .card .card-content h2 {
  margin: 0;
  line-height: 1;
  font-size: 2.4em;
  font-weight: 600;
  color: var(--third-500);
}
section.result .card .card-content h2 p {
  display: none;
}
section.result .card .card-content .card-text {
  opacity: 0;
  display: none;
  transition: opacity 0.3s ease-out;
  font-size: 2em;
  margin-top: 1rem;
  text-align: justify;
  line-height: 1.25;
}
section.result .card.swiper-slide-active {
  width: 42em;
  max-height: 100%;
  height: calc(100% - 2em);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
section.result .card.swiper-slide-active .content-top {
  font-size: 1.3em;
}
section.result .card.swiper-slide-active h2 {
  width: 90%;
  border-end-end-radius: 0rem;
  border-bottom-left-radius: 0rem;
}
section.result .card.swiper-slide-active .card-content {
  margin: 0 auto !important;
  padding: 2em;
  font-size: 0.8em;
}
section.result .card.swiper-slide-active .card-content h2 span {
  display: none;
}
section.result .card.swiper-slide-active .card-content h2 p {
  display: inline-block;
}
section.result .card.swiper-slide-active .img-bg-card2 {
  display: block;
}
section.result .card.swiper-slide-active .img-bg-card1 {
  display: none;
}
section.result .card.swiper-slide-active .card-text {
  opacity: 1;
  transition: opacity 0.5s 0.1s ease-in;
  display: block;
}
section.result .card.active img {
  transition: opacity 0.3s ease-in;
  opacity: 1;
}
section.result .card-2 {
  background: linear-gradient(48.7deg, #23ccee 18.42%, rgba(157, 255, 204, 0.6) 83.79%);
}
section.result .card-3 {
  background: linear-gradient(48.7deg, #cbffd8 18.42%, #a89cf9 100%);
}
section.result .card-4 {
  background: linear-gradient(211deg, rgba(223, 255, 162, 0.5607843137) 0%, #20c2d9 100%);
}
section.result .swiper-3d .swiper-wrapper {
  height: 65em;
  align-items: center;
}
section.result .swiper {
  padding: 0 3.5em;
}

@media (max-width: 767px) {
  section.result {
    font-size: 0.6rem;
  }
  section.result .card .img-bg-card2 {
    right: -9%;
    top: 14em;
    max-height: 90%;
    height: auto;
  }
  section.result .card-4 .img-bg-card2 {
    right: -7%;
  }
  section.result .card .content-top h4 {
    font-size: 2.3em;
  }
  section.result .card .content-top p {
    width: 65%;
  }
  section.result .swiper-3d .swiper-wrapper {
    height: 80em;
  }
  section.result .card.swiper-slide-active {
    height: calc(100% - 2em);
  }
  section.result .card.swiper-slide-active .card-text {
    font-size: 2.2em;
  }
}
@media (max-width: 391px) {
    section.result {
        font-size: 0.8rem;
    }

    section.result .card.swiper-slide-active {
        height: calc(100% - 15em);
    }
}
section.article .header-section {
  font-size: 3.5em;
  font-weight: 700;
  text-align: center;
  margin-bottom: 0;
}
section.article .slide-article {
  padding: 5em 1em;
}
section.article .slide-article .article-item {
  border-radius: 2em;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  text-align: center;
  font-weight: 400;
  position: relative;
  background: rgb(109, 212, 169);
  background: rgb(252, 254, 244);
  background: linear-gradient(150deg, rgb(252, 254, 244) 0%, rgb(158, 237, 241) 100%);
}
section.article .slide-article .article-item__title {
  text-transform: uppercase;
  font-size: 1.8em;
  padding: 0.8em 1em;
  color: var(--primary-800);
  line-height: 1.3;
  height: 6em;
}
section.article .slide-article .article-item__title span {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5em;
}
section.article .slide-article .article-item__avatar {
  aspect-ratio: 2.8/2;
}
section.article .slide-article .article-item__avatar img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
section.article .slide-article .article-item__des {
  display: none;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5em;
}
section.article .slide-article .article-item__action {
  position: absolute;
  margin: auto;
  left: 0;
  right: 0;
  bottom: 1.5em;
}
section.article .slide-article .article-item__action a {
  color: var(--primary-500);
  display: flex;
  flex-direction: row;
  width: -moz-fit-content;
  width: fit-content;
  align-items: center;
  gap: 1em;
  padding: 0.7em 1.2em;
  margin: auto;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);
  background-color: var(--primary-100);
  color: var(--primary-800);
  cursor: pointer;
  border-radius: 1em;
  font-size: 1.5em;
  transition: all 0.3s ease-in;
}
section.article .slide-article .article-item__action a:hover {
  background-color: var(--primary-600);
  color: white;
}

@media (max-width: 767px) {
  section.article {
    font-size: 0.6rem;
  }
  section.article .slide-article {
    padding: 2em 0;
  }
  section.article .slide-article .control-navigate-pagination {
    justify-content: center;
    width: 100%;
  }
  section.article .slide-article .control-navigate-pagination .swiper-pagination {
    display: none;
  }
}
.footer-main-content-male-page {
  background-color: #05A8AE !important;
  text-align: justify;
  padding: 10px;
  color: white;
  border-bottom-left-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
}
.footer-main-content-male-page .content {
  margin: 2em auto 0 auto;
}
.footer-main-content-male-page .content b {
  font-size: 1.4em;
}
.footer-main-content-male-page .content ul {
  padding: 0;
  text-align: left;
  margin: auto;
}
.footer-main-content-male-page .content ul li {
  margin-bottom: 10px;
  font-size: 1.2em;
  overflow-wrap: break-word;
  display: list-item;
  list-style-type: decimal;
}
.footer-main-content-male-page .footer__content img {
  width: 7em;
  height: auto;
}
.footer-main-content-male-page .footer__content {
  display: none;
}
.footer-main-content-male-page .paragraph-2-col {
  -moz-column-count: 2;
       column-count: 2;
  -moz-column-gap: 3em;
       column-gap: 3em;
  padding: 0 2em;
}

@media (max-width: 767px) {
  .footer-main-content-male-page {
    font-size: 0.7rem;
  }
  .footer-main-content-male-page .content {
    padding: 0;
  }
}
@media (max-width: 1099px) {
  .footer-main-content-male-page .footer__content {
    display: flex;
  }
}
.footer-main-content-male-page.mobile {
  background-color: #05A8AE !important;
  text-align: justify;
  padding: 10px;
  color: white;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.footer-main-content-male-page.mobile .content {
  margin: 2em auto 0 auto;
}
.footer-main-content-male-page.mobile .content b {
  font-size: 1.4em;
}
.footer-main-content-male-page.mobile .content ul {
  padding: 0;
  text-align: left;
  margin: auto;
}
.footer-main-content-male-page.mobile .content ul li {
  margin-bottom: 10px;
  font-size: 1.2em;
  overflow-wrap: break-word;
  display: list-item;
  list-style-type: decimal;
}
.footer-main-content-male-page.mobile .footer__content img {
  width: 7em;
  height: auto;
}
.footer-main-content-male-page.mobile .footer__content {
  display: none;
}
.footer-main-content-male-page.mobile .paragraph-2-col {
  -moz-column-count: 2;
       column-count: 2;
  -moz-column-gap: 3em;
       column-gap: 3em;
  padding: 0 2em;
}

.footer-main-content-mobile .footer__content {
  font-size: 0.8em;
  background: var(--primary-500);
}
.footer-main-content-mobile .footer__content img {
  width: 7em;
  height: auto;
}
.footer-main-content-mobile .footer__content h3 {
  color: var(--natural-100);
}
.footer-main-content-mobile .footer__content .direction a {
  color: var(--natural-200);
}
.footer-main-content-mobile .container-footer {
  position: relative;
  max-width: 600px;
}
.footer-main-content-mobile .content-footer {
  max-height: 20px; /* Adjust the initial max-height as needed */
  overflow: hidden;
  transition: max-height 0.3s ease; /* Add smooth transition effect */
}
.footer-main-content-mobile .show-more {
  font-size: 1.5em;
  position: absolute;
  width: 100%;
  text-align: end;
  height: 20px;
  top: 0;
  right: 0;
  cursor: pointer;
  color: var(--natural-900);
  display: block;
  background-color: linear-gradient(to bottom, rgba(255, 255, 255, 0.4) 0%, rgb(255, 255, 255) 100%);
}

@media (max-width: 767px) {
  .footer-main-content-male-page {
    font-size: 0.7rem;
  }
  .footer-main-content-male-page .content {
    padding: 0;
  }
}
@media (max-width: 1099px) {
  .footer-main-content-male-page .footer__content {
    display: flex;
  }
}
.control-navigate-pagination {
  display: flex;
  position: relative;
  justify-content: center;
  width: -moz-fit-content;
  width: fit-content;
  margin: auto;
  gap: 2em;
  margin-top: 1.5em;
}
.control-navigate-pagination .swiper-pagination {
  bottom: -0.8%;
}
.control-navigate-pagination .swiper-pagination.bg-transparent {
  background-color: rgba(128, 128, 128, 0.3);
  border-radius: 2em;
  width: -moz-fit-content;
  width: fit-content;
  padding: 1em;
  bottom: -4em !important;
}
.control-navigate-pagination .swiper-pagination-bullet {
  width: 1.5em;
  height: 1.5em;
  background-color: white;
  opacity: 1;
}
.control-navigate-pagination .swiper-pagination-bullet-active {
  width: 4em;
  transition: width 0.5s;
  border-radius: 2em;
  background: #05A8AE;
  border: 1px solid transparent;
}
.control-navigate-pagination .swiper-pagination,
.control-navigate-pagination .swiper-button-prev,
.control-navigate-pagination .swiper-button-next {
  position: static;
  display: flex;
  align-items: center;
  justify-content: center;
}
.control-navigate-pagination .swiper-button-prev,
.control-navigate-pagination .swiper-button-next {
  height: 4.5em;
  width: 4.5em;
  margin: 0;
  background-color: rgba(255, 255, 255, 0.32);
  padding: 1em !important;
  line-height: 2em;
  border-radius: 50%;
  color: #05A8AE;
  flex: none;
}
.control-navigate-pagination .swiper-button-prev::after,
.control-navigate-pagination .swiper-button-next::after {
  font-size: 1.5em !important;
  -webkit-text-stroke-width: 0.15em;
}
.control-navigate-pagination .swiper-button-prev:hover,
.control-navigate-pagination .swiper-button-next:hover {
  background-color: #05A8AE;
  color: white;
}
.control-navigate-pagination .btn-play,
.control-navigate-pagination .btn-pause {
  height: 2.5em;
  width: 2.5em;
  display: none;
  justify-content: center;
  background-color: #b8c9bb;
  border-radius: 2em;
  align-items: center;
  font-size: 1.5em;
  color: #05A8AE;
}
.control-navigate-pagination .btn-play.active,
.control-navigate-pagination .btn-pause.active {
  display: flex;
}
.control-navigate-pagination .btn-play svg {
  margin-right: -0.2em;
}

.swiper-wrapper {
  height: auto;
}

.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right {
  background-image: none;
}

/* SECTION - HPV-Diseases */
.common-hpv-diseases {
  position: relative;
  padding: 3rem 0;
  width: 100%;
  font-size: 1.03734439834rem;
  z-index: 0;
}

.common-hpv-diseases__wrapper {
  height: inherit;
  display: flex;
  position: relative;
  flex-direction: column;
  z-index: 0;
}

.common-hpv-diseases::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  mask-image: linear-gradient(180deg, rgba(217, 217, 217, 0) 80%, #737373 112.31%);
  mask-size: 100% 100%;
  mask-repeat: no-repeat;
  width: 100%;
  height: 100%;
  background: linear-gradient(291.33deg, #009585 -25.46%, #00CFA9 46.97%);
  z-index: -1;
}

.common-hpv-diseases__title {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.6em;
  position: relative;
}

.title-main,
.title-highlight {
  font-size: 2.5em;
  font-weight: 600;
}

.title-highlight {
  background: linear-gradient(90deg, #00D6E2 0%, #009585 100%);
  border-radius: 50px;
  padding: 0.2em 1.5em;
  color: white;
}

.title-classify {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 4em;
}

.common-hpv-diseases__contents {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  position: relative;
  /* overflow: hidden; */
  margin-top: 2vw;
  font-size: 1.3em;
}

.common-hpv-diseases__areas {
  width: 35%;
}

.common-hpv-diseases__areas.area-left,
.common-hpv-diseases__areas.area-right {
  display: flex;
  flex-direction: column;
  gap: 2.5em;
  justify-content: center;
}

.common-hpv-diseases__statistics {
  display: flex;
  flex-direction: column;
  gap: 2.5em;
  margin-top: -50%;
}

.common-hpv-diseases__areas:not(.area-left, .area-right) {
  position: relative;
}

.common-hpv-diseases__image {
  filter: grayscale(1) blur(1px);
  /* transition: filter 0.3s ease-in-out, transform 0.2s ease-in-out; */
  transition: 0.15s linear;
  /* cursor: pointer; */
}

.common-hpv-diseases__image.male {
  position: relative;
  z-index: 1;
  aspect-ratio: 1020 / 1530;
  width: 100%;
  left: 15%;
}

.common-hpv-diseases__image.male.active {
  filter: grayscale(0) blur(0);
  /* transform: scale(1.02); */
  z-index: 3;
}

.common-hpv-diseases__image.female.active {
  filter: grayscale(0) blur(0);
  transform: scale(1.02);
  z-index: 4;
}

.common-hpv-diseases__image:not(.active):hover {
  transform: scale(1.02);
}

.common-hpv-diseases__statistic {
  position: relative;
  padding: 1.5em 0;
  overflow: hidden;
  border-radius: 0 80px 80px 0;
  color: #adadad;
  cursor: pointer;
  transition: color 0.3s ease;
}

.area-left .common-hpv-diseases__disease-percent {
  text-align: right;
  margin-right: 1em;
  z-index: 1;
  position: relative;
}

.area-left .common-hpv-diseases__disease-name {
  text-align: right;
  margin-right: 3em;
  z-index: 1;
  position: relative;
}

.common-hpv-diseases__statistic.active {
  color: #0D867A;
}
.area-left .common-hpv-diseases__statistic.active .common-hpv-diseases__disease-percent{
  text-shadow: 2px 0 #fff, -2px 0 #fff, 0 2px #fff, 0 -2px #fff,
            1px 1px #fff, -1px -1px #fff, 1px -1px #fff, -1px 1px #fff;

}
.common-hpv-diseases__statistic.statistic-both-genders.active.male-color .common-hpv-diseases__disease-percent{
  text-shadow: 2px 0 #fff, -2px 0 #fff, 0 2px #fff, 0 -2px #fff,
  1px 1px #fff, -1px -1px #fff, 1px -1px #fff, -1px 1px #fff;
}
.common-hpv-diseases__statistic.statistic-both-genders.active .common-hpv-diseases__disease-percent{
  text-shadow: 2px 0 #fff, -2px 0 #fff, 0 2px #fff, 0 -2px #fff,
  1px 1px #fff, -1px -1px #fff, 1px -1px #fff, -1px 1px #fff;
}
.common-hpv-diseases__statistic.statistic-female.active {
  /* color: var(--secondary-500); */
  color: #ffdf1b;
} 
.common-hpv-diseases__statistic.statistic-male.active .common-hpv-diseases__disease-percent {
  color: var(--third-500);
  text-shadow: 2px 0 #fff, -2px 0 #fff, 0 2px #fff, 0 -2px #fff,
            1px 1px #fff, -1px -1px #fff, 1px -1px #fff, -1px 1px #fff;
}
.common-hpv-diseases__statistic.statistic-female.active .common-hpv-diseases__disease-name {
  color: #39514D;
}
.common-hpv-diseases__statistic.active .common-hpv-diseases__disease-name {
  color: #39514D;
}
.male-color .common-hpv-diseases__disease-percent {
  color: var(--third-500) !important;
}

.female-color .common-hpv-diseases__disease-percent {
  color: #ffdf1b !important;
  text-shadow: 2px 0 #fff, -2px 0 #fff, 0 2px #fff, 0 -2px #fff,
            1px 1px #fff, -1px -1px #fff, 1px -1px #fff, -1px 1px #fff;
}

.common-hpv-diseases__statistic::before {
  content: '';
  position: absolute;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.66) 0%, rgba(255, 255, 255, 0.02) 100%);
  /* background: linear-gradient(90deg, rgb(255 255 255 / 64%) 0%, rgb(255 255 255 / 29%) 100%); */
  transform: rotate(180deg);
  top: -10%;
  width: 100%;
  height: 120%;
  z-index: 0;
}

.area-right .common-hpv-diseases__statistic {
  padding: 1.5em 0;
  border-radius: 80px 0 0 80px;
}

.area-right .common-hpv-diseases__statistic::before {
  transform: rotate(0);
}

.area-right .common-hpv-diseases__disease-name {
  position: relative;
  z-index: 1;
  margin-left: 3em;
}

.area-right .common-hpv-diseases__disease-percent {
  position: relative;
  z-index: 1;
  margin-left: 1em;
}

.common-hpv-diseases__disease-percent {
  font-size: 4.5em;
  line-height: 1.2;
  font-weight: 800;
}

.common-hpv-diseases__disease-name {
  font-size: 1.5em;
  font-weight: 500;
}

@media (min-width: 319px) and (max-width: 376px) {
  .common-hpv-diseases {
     font-size: 0.45rem !important;
  }

  .common-hpv-diseases__areas.area-right,
  .common-hpv-diseases__areas.area-left {
     width: 35% !important;
  }
}

@media (max-width: 575px) {
  .common-hpv-diseases {
     font-size: 0.55rem;
  }

  .common-hpv-diseases__areas:not(.area-right, .area-left) {
     width: 40%;
     z-index: 1;
  }

  .common-hpv-diseases__areas.area-right,
  .common-hpv-diseases__areas.area-left {
     position: relative;
     justify-content: center;
     width: 34%;
  }

  .common-hpv-diseases__title {
     font-size: 1.2em;
  }

  .title-main, .title-highlight {
      font-size: 2.5vw;
      font-weight: 600;
  }

  .common-hpv-diseases__areas.area-right {
     right: 0;
  }

  .btn-cta {
    font-size: 1.5em;
    line-height: 1.4;
    min-width: 17em;
 }
 .common-hpv-diseases__contents {
   font-size: 1em;
 }

 .common-hpv-diseases__statistics {
   margin-top: 0;
 }
}

@media (min-width: 576px) and (max-width: 768px) {
  .common-hpv-diseases {
     font-size: 0.75rem;
  }

  .common-hpv-diseases__areas:not(.area-right, .area-left) {
     width: 45%;
     z-index: 1;
  }

  .common-hpv-diseases__areas.area-right,
  .common-hpv-diseases__areas.area-left {
     position: relative;
     justify-content: center;
     width: 30%;
  }

  .common-hpv-diseases__areas.area-right {
     right: 0;
  }
  .common-hpv-diseases__contents {
    font-size: 1.2em;
  }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
  .common-hpv-diseases {
     font-size: 0.88174273858rem;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .common-hpv-diseases__areas:not(.area-left, .area-right) {
     width: 40%;
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .common-hpv-diseases {
     position: relative;
     padding: 2rem 0;
     width: 100%;
     font-size: 0.82rem;
     z-index: 0;
  }

  .common-hpv-diseases__areas:not(.area-right, .area-left) {
     width: 30%;
  }

  .common-hpv-diseases__areas.area-right,
  .common-hpv-diseases__areas.area-left {
     position: relative;
     justify-content: center;
     width: 30%;
  }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .common-hpv-diseases {
     position: relative;
     padding: 2rem 0;
     width: 100%;
     font-size: 0.9rem;
     z-index: 0;
  }
}

/* END - SECTION - HPV-Diseases */