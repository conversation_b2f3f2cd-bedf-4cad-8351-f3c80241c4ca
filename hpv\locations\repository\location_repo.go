package repository

import (
	"context"
	"webgo/hpv/entity"
	"webgo/pkg/gos/generics"
	"webgo/pkg/gos/utils"

	"gorm.io/gorm"
)

type locationsRepo struct {
	db *gorm.DB
}

func NewLocationsRepo(db *gorm.DB) *locationsRepo {
	return &locationsRepo{db: db}
}

/**
 *
 * Find Location Repo
 */
func (r *locationsRepo) FindLocationsRepo(ctx context.Context, filter *utils.Filters) ([]*entity.LocationsEntity, error) {
	tableName := entity.LocationsEntity{}.TableName()
	return generics.FindGeneric[entity.LocationsEntity](ctx, r.db, tableName, filter)
}
