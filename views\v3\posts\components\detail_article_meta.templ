package components

import "webgo/hpv/postsv3/transport/responses"
import "webgo/pkg/gos/utils"

templ ArticleMeta(post *responses.PostDetail) {
    <ul class="article-meta mb-1">
        if post != nil && post.Views > 0 {
        <li class="article-meta-item">
            <span class="article-meta-icon">
                <svg xmlns:xlink="http://www.w3.org/1999/xlink">
                    <use xlink:href="#eye-icon" fill="currentColor"></use>
                </svg>
            </span>           
            <span>{ utils.FormatNumberWithDot(post.Views) }</span>
        </li>
        }    
        <li class="article-meta-item">
            <span class="article-meta-icon">
                <svg xmlns:xlink="http://www.w3.org/1999/xlink">
                    <use xlink:href="#calendar-icon" fill="currentColor"></use>
                </svg>
            </span>           
            <span>{ post.CreatedAt.Format("02/01/2006") }</span>
        </li>
    </ul>
}