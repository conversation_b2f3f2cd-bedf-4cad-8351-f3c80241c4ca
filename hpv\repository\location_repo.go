package repository

import (
	"context"
	"webgo/hpv/entity"
	"webgo/pkg/gos/generics"
	"webgo/pkg/gos/utils"

	"gorm.io/gorm"
)

type locationRepo struct {
	db *gorm.DB
}

func NewLocationRepo(db *gorm.DB) *locationRepo {
	return &locationRepo{db: db}
}

/**
 * Find Location
 * Use generic
 */

func (r *locationRepo) FindLocationsRepo(ctx context.Context, filter *utils.Filters) ([]*entity.LocationsEntity, error) {
	tableName := entity.LocationsEntity{}.TableName()
	return generics.FindGeneric[entity.LocationsEntity](ctx, r.db, tableName, filter)
}


/**
 * find location repo
 */
func (r *locationRepo) FindProvinceRepo(ctx context.Context, filter *utils.Filters) ([]*entity.ProviceEntity, error) {
	tableName := entity.ProviceEntity{}.TableName()
	return generics.FindGeneric[entity.ProviceEntity](ctx, r.db, tableName, filter)
}