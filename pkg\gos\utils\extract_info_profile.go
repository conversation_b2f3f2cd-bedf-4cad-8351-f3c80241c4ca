package utils

import (
	"regexp"
	"strings"
)

var (
	rexpMailPattern  = `([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9._-]+)`
	rexpPhonePattern = `(\(?\+84\)?\s?[0-9 -.\\?]+)|([0-9][.\- ]?){10}`
)

func ExtractEmailDetailFromBio(biography string) *string {
	rexpCompile := regexp.MustCompile(rexpMailPattern)
	emailSlices := rexpCompile.FindAllString(biography, -1)
	justString := strings.Join(emailSlices, ", ")

	return &justString
}

func ExtractPhoneDetailFromBio(biography string) *string {
	rexpCompile := regexp.MustCompile(rexpPhonePattern)
	phoneSlices := rexpCompile.FindAllString(biography, -1)

	var tmp = make([]string, 0)
	var checkUnique = make(map[string]struct{}, 0)
	// process replace all special charater from phone => standalize phone number
	for _, phone := range phoneSlices {
		phone = strings.ReplaceAll(phone, ".", "")
		phone = strings.ReplaceAll(phone, "-", "")
		phone = strings.ReplaceAll(phone, "+", "")
		phone = strings.ReplaceAll(phone, "?", "")
		phone = strings.ReplaceAll(phone, " ", "")
		// only append if current phone not exist in the map
		if _, ok := checkUnique[phone]; !ok {
			tmp = append(tmp, phone)
			checkUnique[phone] = struct{}{}
		}
	}
	var result = make([]string, len(tmp))
	copy(result, tmp)
	phones := strings.Join(result, ", ")

	return &phones
}
