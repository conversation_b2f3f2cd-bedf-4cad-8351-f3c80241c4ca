package usecase

import (
	"context"
	"errors"
	"webgo/hpv/common/enums"
	"webgo/hpv/entity"
	"webgo/hpv/malesv3/mapping"
	"webgo/hpv/malesv3/transport/responses"
	"webgo/pkg/gos/utils"
	"webgo/pkg/sctx/core"
)

type MaleV3CategoryRepo interface {
	FirstCategoryRepo(ctx context.Context, filter *utils.Filters) (*entity.CategoryEntity, error)
}

type MaleOptionValueRepo interface {
	FindOptionValueRepo(ctx context.Context, filter *utils.Filters) ([]*entity.OptionValue, error)
}

type maleV3Usc struct {
	categoryRepo MaleV3CategoryRepo
	optionRepo MaleOptionValueRepo
}

func NewMaleV3Usc(categoryRepo MaleV3CategoryRepo, optionRepo MaleOptionValueRepo) *maleV3Usc {
	return &maleV3Usc{
		categoryRepo: categoryRepo,
		optionRepo: optionRepo,
	}
}

/**
 * usc for hdl male v3
 */
func (usc *maleV3Usc) GetMaleV3Usc(ctx context.Context) (*responses.MaleV3Resp, error) {
	filter := &utils.Filters{
		Conds: &map[string]interface{} {
			"id":   enums.CATEGORY_MALE,
			"status": enums.STATUS_ACTIVE,
		},
	}
	
	category, err := usc.categoryRepo.FirstCategoryRepo(ctx, filter)
	if err != nil {
		return nil, err
	}

	options, err := usc.getOptionValueMale(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	return &responses.MaleV3Resp{
		Seo:   		category.Seo,
		Options:    options,
	}, nil

}

func (usc *maleV3Usc) getOptionValueMale(ctx context.Context) (map[string]string, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"option_group_id": enums.CATEGORY_MALE,
			"status":          enums.STATUS_ACTIVE,
		},
		Columns: &[]string{
			"key",
			"content",
		},
	}

	options, err := usc.optionRepo.FindOptionValueRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return mapping.MapperOptionValueToMapMale(options), nil
}


