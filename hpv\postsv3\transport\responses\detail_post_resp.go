package responses

import (
	"time"
	"webgo/hpv/entity"
)

type DetailPostResp struct {
	Post             *PostDetail         `json:"post"`
	PostListCategory *[]PostListCategory `json:"post_list_category"`
	Options           map[string]string
}

type PostDetail struct {
	Title       string              `json:"title"`
	Slug        string              `json:"slug"`
	PagePath    string              `json:"page_path"`
	Description string              `json:"description,omitempty"`
	Seo         *entity.Seo         `json:"seo"`
	Img         string              `json:"img"`
	Views       int64               `json:"views"`
	Contents    []*entity.Content   `json:"contents"`
	Sources     *entity.SourcesList `json:"sources,omitempty"`
	CreatedAt   time.Time           `json:"created_at"`

	CategoryName string `json:"category_name"`
	CategorySlug string `json:"category_slug"`
}

type PostListCategory struct {
	Title       string `json:"title"`
	PagePath    string `json:"page_path"`
	Img         string `json:"img"`
	Views       int64  `json:"views"`
	Description string `json:"description"`
}
