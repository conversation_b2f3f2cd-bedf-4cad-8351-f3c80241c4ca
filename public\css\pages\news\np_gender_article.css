.section-gender {
    position: relative;
    margin: 0 auto;
    margin-top: 5vw;
}
.gender-tabs-wrapper {
    display: flex;
    position: relative;
    justify-content: space-between;
    align-items: center;
    border-radius: 1em 1em 0 0;
    transition: background 0.5s ease;
    aspect-ratio: 1218 / 68;
    width: 100%;
}
.gender-tabs-background {
    display: none;
    width: 100%;
    aspect-ratio: 1218 / 68;
    position: absolute;
    left: 0;
    z-index: 0;
    bottom: 0;
    overflow: hidden;
    border-radius: 1em 1em 0 0;
}
.gender-tabs-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
#gender-bg-1 {
    display: block; 
}
.gender-kid-bg{
   z-index: -1;
}
.gender-modal {
    position: absolute;
    bottom: 0;
    left: 3%;
    height: auto;
    z-index: 2;
    width: 17vw;
    aspect-ratio: 684 / 343;
}
.gender-modal__img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.gender-menu-tabs {
    font-size: 0.625vw;
    width: 80%;
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
    right: 0;
    gap: 4em;
}
.gender-tab {
    font-size: 1.5em;
    text-align: center;
    cursor: pointer;
    color: rgba(173, 255, 191, 1);
    font-weight: 400;
    padding: 0.3em 1em; 
    border-radius: 2em; 
    background-color: transparent;
    transition: background-color 0.3s, color 0.3s;
}
.gender-tab:hover {
    background-color: rgba(173, 255, 191, 0.5);
    color: #fff;
    transform: scale(1.05);
}
.gender-tab.active {
    background-color: #009688;
    color: #fff;
    padding: 0.3em 1em;
    border-radius: 2em;
    font-weight: 600;
}
.gender-articles-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3em;
    padding: 3%;
    background: rgba(246, 248, 250, 1);
    border-radius: 1em;
}
.gender-article {
    display: flex;
    gap: 1em;
    background-color: #fff;
    border-radius: 1em;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    font-size: 0.652vw;
}
.gender-article:hover .article-img {
    transform: scale(1.05);
    transition: all 0.3s linear;
}
.gender-article .article-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.gender-article-cont-img {
    width: 40%;
    aspect-ratio: 4 / 3;
    overflow: hidden;
}
.gender-article-content {
    width: 60%;
    padding: 1.6em;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 0.5em;
}
.gender-article-title {
    font-size: 2em;
    font-weight: 600;
    color: #222;
}
.gender-article-desc {
    font-size: 1.2em;
    color: #000000;
    margin-bottom: 1.6em;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: initial;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
}
.gender-article-meta {
    font-size: 0.7em;
    color: #777;
    display: flex;
    gap: 1.6em;
}

/* Responsive */
@media (max-width: 768px) {
    .section-gender {
        margin: 0 auto;
        margin-top: 35vw;
    }
    .gender-articles-grid {
        grid-template-columns: 1fr;
        gap: 1em;
    }
    .gender-tabs-background {
        display: none;
        width: 100%;
        aspect-ratio: unset;
        position: absolute;
        border-radius: 1em 1em 0 0;
        height: 8vw;
    }
    .gender-tabs-wrapper {
        aspect-ratio: unset;
        width: 100%;
        height: 8vw;
    }
    .article {
        flex-direction: column;
    }

    .article-img {
        width: 33%;
        height: 100%;
    }
    .gender-article {
        gap: 0.8em;
        font-size: 1.25vw;
    }
    .gender-article-content {
        padding: 0.8em;
    }
    .gender-article:first-child {
        position: relative;
        margin-top: 0;  
        font-size: 1.25vw;
    }
    .gender-article:first-child .gender-article-cont-img {
        width: 100%;
        aspect-ratio: 4 / 3;
        overflow: hidden;
    }
    .gender-article:first-child .gender-article-content{
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        display: flex;
        flex-direction: row;
        padding: 4em;
    }
    .gender-article:first-child .gender-article-meta {
        font-size: 1.5em;
        color: #ffffff;
        align-items: end;
    }
    .gender-article:first-child .article-img{
        width: 100%;
        height: 100%;
        aspect-ratio: 4 / 3;
    }
    .gender-article:first-child .gender-article-title {
        font-size: 3.5em;
        color: var(--neutral-900);
        max-width: 70%;
    }
    .gender-article:first-child .gender-article-desc {
        display: none;
    }
    .gender-article:first-child .nitem__support-item  {
        font-size: 1em;
        gap: 0.5em;
        color: #ffffff;
    }

    .gender-article:first-child .nitem__support-item span  {
        color: #ffffff;
    }
    .gender-article:first-child .nitem__support-icon {
        filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7500%) hue-rotate(140deg) brightness(105%) contrast(101%);
    }
    .gender-modal {
        position: absolute;
        bottom: 8vw;
        left: 50%;
        height: auto;
        z-index: 2;
        width: 100%;
        aspect-ratio: 828 / 292;
        transform: translateX(-50%);
    }
    .gender-menu-tabs {
        font-size: 1.2vw;
        width: 100%;
        gap: 5vw;
    }
}
@media (max-width: 575px) {
    .gender-menu-tabs {
        font-size: 1.4vw;
        width: 100%;
        gap: 3vw;
    }
    .gender-tab.active {
        background-color: unset;
        color: var(--neutral-900);
        padding: 0.3em 1em;
        border-radius: 2em;
        transform: scale(1.3);
    }
    .gender-article {
        gap: 0.8em;
        font-size: 1.5vw;
    }
}
