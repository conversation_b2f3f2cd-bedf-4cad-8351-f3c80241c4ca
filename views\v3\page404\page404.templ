package page404

import "webgo/views/v3/partials/menus"
import "webgo/views/v3/partials"
import "webgo/pkg/gos/templates"

templ Page404() {
    @menus.Headers()
    <main>
        <section class="page_404">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 ">
                        <div class="col-sm-10 col-sm-offset-1  text-center">
                            <div class="four_zero_four_bg"></div>
                            <div class="contant_box_404">
                                <h3 class="h2">
                                    Trang không tồn tại
                                </h3>
                                <p>Vui lòng truy cập trang khác!</p>
                                <a href="/" class="link_404">Trang chủ</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    @partials.HPVSvgIcon()
    @partials.JSMain()
    @css404()
}

templ css404() {
    <link rel="stylesheet" href={templates.AssetURL("/asset/js/lib/swiper/swiper-bundle.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/components/fast-action-controls.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/page-404/page-404.css")}> 
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/layout/menu/menu-2.css") }>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/main.css") }>
}