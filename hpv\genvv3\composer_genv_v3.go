package genvv3

import (
	"webgo/hpv/genvv3/transport/handlers"
	"webgo/hpv/genvv3/usecase"
	"webgo/hpv/repository"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/component/gormc"
	"webgo/pkg/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type composerGenvV3 interface {
	PageGenvV3Hdl() fiber.Handler
}

func ComposerGenvV3Service(serviceCtx sctx.ServiceContext) composerGenvV3 {
	db := serviceCtx.MustGet(configs.KeyCompGorm).(gormc.GormComponent).GetDB()
	repoCategory := repository.NewCategoryRepo(db)
	usc := usecase.NewGenvV3Usc(repoCategory)

	hdl := handlers.NewGenvV3Hdl(usc)
	return hdl
}
