package usecase

import (
	"context"
	"errors"
	"webgo/hpv/blogs/mapping"
	"webgo/hpv/blogs/transport/responses"
	"webgo/hpv/common/enums"
	"webgo/hpv/entity"
	"webgo/pkg/gos/utils"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/core"

	"gorm.io/gorm/clause"
)

type BlogPostRepo interface {
	FindPostRepo(ctx context.Context, filter *utils.Filters) ([]*entity.PostEntity, error)
}

type BlogOptionValueRepo interface {
	FindOptionValueRepo(ctx context.Context, filter *utils.Filters) ([]*entity.OptionValue, error)
}

type blogUsc struct {
	repo       BlogPostRepo
	optionRepo BlogOptionValueRepo
	logger     sctx.Logger
}

func NewBlogUsc(repo BlogPostRepo, optionRepo BlogOptionValueRepo, logger sctx.Logger) *blogUsc {
	return &blogUsc{
		repo:       repo,
		optionRepo: optionRepo,
		logger:     logger,
	}
}

/**
 * List blog usc
 */
func (usc *blogUsc) ListBlogUsc(ctx context.Context) (*responses.BlogListResp, error) {
	postNew, err := usc.postWithFilterCategory(ctx, nil, enums.LIMIT_5, enums.ORDERBY_ID_DESC)
	if err != nil {
		return nil, err
	}

	postViews, err := usc.postWithFilterCategory(ctx, nil, enums.LIMIT_3, enums.ORDERBY_VIEWS_DESC)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	giaiMaHPV, err := usc.postWithFilterCategory(ctx, []int64{enums.CATEGORY_GIAI_MA_HPV}, enums.LIMIT_4, enums.ORDERBY_ID_DESC)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	ganhNangTuHPV, err := usc.postWithFilterCategory(ctx, []int64{enums.CATEGORY_GANH_NANG_TU_HPV}, enums.LIMIT_4, enums.ORDERBY_ID_DESC)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	phongVeHPV, err := usc.postWithFilterCategory(ctx, []int64{enums.CATEGORY_PHONG_VE_HPV}, enums.LIMIT_10, enums.ORDERBY_ID_DESC)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	thuVienBenhHocHPV, err := usc.postWithFilterCategory(ctx, []int64{enums.CATEGORY_THU_VIEN_BENH_HOC_HPV}, enums.LIMIT_5, enums.ORDERBY_ID_DESC)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	treViThanhNien, err := usc.postWithFilterCategory(ctx, enums.TreViThanhNien, enums.LIMIT_4, enums.ORDERBY_ID_DESC)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	thanhThieuNien, err := usc.postWithFilterCategory(ctx, enums.ThanhThieuNien, enums.LIMIT_4, enums.ORDERBY_ID_DESC)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	nguoiTruongThanh, err := usc.postWithFilterCategory(ctx, enums.NguoiTruongThanh, enums.LIMIT_4, enums.ORDERBY_ID_DESC)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	options, err := usc.getOptionValueBlogs(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	return &responses.BlogListResp{
		BlogNew:           postNew,
		BlogViews:         postViews,
		GiaiMaHPV:         giaiMaHPV,
		GanhNangTuHPV:     ganhNangTuHPV,
		PhongVeHPV:        phongVeHPV,
		ThuVienBenhHocHPV: thuVienBenhHocHPV,
		TreViThanhNien:    treViThanhNien,
		ThanhThieuNien:    thanhThieuNien,
		NguoiTruongThanh:  nguoiTruongThanh,
		Options:           options,
	}, nil
}

/**
 * post with categoryId
 *
 * @param ctx
 * @param categoryId
 * @param limit
 * @param orderBy
 *
 * @return []responses.BlogItem
 * @return error
 */
func (usc *blogUsc) postWithFilterCategory(ctx context.Context, categoryIds []int64, limit int, orderBy string) ([]responses.BlogItem, error) {
	conds := map[string]interface{}{
		"status": enums.POST_STATUS_PUBLISH,
	}
	if len(categoryIds) > 0 {
		subQuery := "SELECT post_id FROM cms.categories_posts WHERE category_id IN (?)"
		conds["id IN (?)"] = clause.Expr{SQL: subQuery, Vars: []interface{}{categoryIds}}
	}

	filter := utils.Filters{
		Conds: &conds,
		Columns: &[]string{
			"id",
			"page_path",
			"title",
			"img",
			"description",
			"views",
		},
	}
	if orderBy != "" {
		filter.OrderBy = &[]string{orderBy}
	} else {
		filter.OrderBy = &[]string{enums.ORDERBY_ID_DESC}
	}

	if limit > 0 {
		filter.PageSize = limit
	} else {
		filter.PageSize = enums.LIMIT_3
	}

	posts, err := usc.repo.FindPostRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return mapping.MapperPostToBlogItemResq(posts), nil
}

/**
 * get option value
 *
 */
func (usc *blogUsc) getOptionValueBlogs(ctx context.Context) (map[string]string, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"option_group_id": enums.OPTION_GROUP_BLOGS,
			"status":          enums.STATUS_ACTIVE,
		},
		Columns: &[]string{
			"key",
			"content",
		},
	}

	options, err := usc.optionRepo.FindOptionValueRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return mapping.MapperOptionValueToMap(options), nil
}
