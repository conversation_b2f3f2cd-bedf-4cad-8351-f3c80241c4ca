//----------------------------------------------------------------
// *** MENU DESKTOP ***
//----------------------------------------------------------------

function getLink(type, value) {
    switch (type) {
        case "image":
            return `${window.location.protocol}//cms.${window.location.hostname}/api/CMS/image/folder/${value}`;

        case "link-post":
            return `${window.location.origin}/${value.url_category ? `${value.url_category}/` : ""
                }${value.url}`;
    }
}

function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);

    if (parts.length === 2) {
        return parts.pop().split(";").shift();
    }

    return null;
}

//----------------------------------------------------------------
// *** SEARCH CONTROL ***
//----------------------------------------------------------------
function addEventForButtonSearch() {
    const elBtn = document.querySelector(".nav-menu #search-control");
    let hasLoadedArticles = false;
    if (!elBtn) return console.log("Not found element!");

    elBtn.addEventListener("click", (e) => {
        e.stopPropagation();
        activeModalSearch();
        addEventForBtnSearchControl();
        if (!hasLoadedArticles) {
            hasLoadedArticles = true;
            requestDefaultArticles("");
        }
    });

    document.addEventListener("click", (event) => {
        if (elBtn.contains(event.target)) return;

        const containerSearch = document.querySelector(
            "#modal-search.modal-search .search-control"
        );
    });
}

function addEventForBtnSearchControl() {
    const containerSearch = document.querySelector(
        "#modal-search.modal-search"
    );

    if (!containerSearch) return console.log("Not found element!");

    const btnSearch = containerSearch.querySelector(".search-control .search");
    const input = containerSearch.querySelector(".input-control");

    if (!btnSearch || !input) return;

    //Render 6 most viewed articles default
    // handleSearch("", containerSearch);

    if (!input.dataset.keyDownEnterEventAdded) {
        input.addEventListener("keydown", (event) => {
            if (event.key === "Enter" || event.keyCode === 13) {
                event.preventDefault();
                handleSearch(input.value, containerSearch);
            }
        });
        input.dataset.keyDownEnterEventAdded = true;
    }
    if (!btnSearch.dataset.clickEventAdded) {
        btnSearch.addEventListener("click", (event) => {
            event.preventDefault();
            handleSearch(input.value, containerSearch);
        });
        btnSearch.dataset.clickEventAdded = true;
    }
}

function activeModalSearch() {
    const popupSearchEle = document.querySelector(".modal-search#modal-search");
    if (!popupSearchEle) return;

    popupSearchEle.classList.add("active");

    const searchContent = popupSearchEle.querySelector(".search-content");

    function eventClickOutSide(event) {
        if (searchContent.contains(event.target)) return;

        popupSearchEle.classList.remove("active");
        document.removeEventListener("click", eventClickOutSide);
    }

    document.addEventListener("click", eventClickOutSide);

    const elBtnCloseModal = popupSearchEle.querySelector(".close-modal");
    if (elBtnCloseModal) {
        elBtnCloseModal.addEventListener("click", () => {
            popupSearchEle.style.display = "block";
            popupSearchEle.classList.remove("active");
            setTimeout(() => {
                popupSearchEle.style.display = "none";
                document.removeEventListener("click", eventClickOutSide);
            }, 300);
        });
    }
}
let isSearching = false;
let lastSearchTime = 0;

async function handleSearch(inputValue, containerSearch) {
    const now = Date.now();

    if (isSearching || now - lastSearchTime < 1000) return;

    lastSearchTime = now;
    isSearching = true;

    const titleResultBox = containerSearch.querySelector(".results-search .title");
    const resultsContent = containerSearch.querySelector(".results-search .list-results");
    const searchControl = containerSearch.querySelector(".search-control");

    if (!titleResultBox || !resultsContent || !searchControl) {
        isSearching = false;
        return;
    }

    searchControl.classList.add("is-loading");
    titleResultBox.textContent = "Kết quả tìm kiếm";
    resultsContent.innerHTML = `
        <div class="msd-loader-container">
            <div class="msd-loader"></div>
            <div class="msd-loader-text">Đang tìm kiếm...</div>
        </div>
    `;

    try {
        const html = await requestSearchArticles(inputValue);
        if (!html || html.trim() === "") {
            resultsContent.innerHTML = `<div class="not-found-post">Không tìm thấy bài viết!</div>`;
        } else {
            resultsContent.innerHTML = html;
        }
    } catch (err) {
        console.error("Search error:", err);
        resultsContent.innerHTML = `<div class="not-found-post">Có lỗi xảy ra. Vui lòng thử lại!</div>`;
    } finally {
        isSearching = false;
        searchControl.classList.remove("is-loading");
    }
}

function requestSearchArticles(value) {
    return fetch(`${window.location.origin}/apis/v3/search-post`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-Token": getCookie("csrf_"),
        },
        body: JSON.stringify({ search: value }),
    })
        .then((response) => response.json())
        .then((data) => data?.html || "")
        .catch((err) => {
            console.error("Lỗi tìm kiếm:", err);
            return "";
        });
}

function requestDefaultArticles(value) {
    return fetch(`${window.location.origin}/apis/v3/get-post`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-Token": getCookie("csrf_"),
        },
        body: JSON.stringify({ search: value }),
    })
        .then((response) => response.json())
        .then((data) => {
            const html = data?.html || "";
            const container = document.getElementById("list-results");
            if (container) {
                container.innerHTML = html;
            }
        })
        .catch((err) => {
            console.error("Lỗi tìm kiếm:", err);
        });
}

function removePopupSearch() {
    const popupSearchEle = document.querySelector("#popup-search");
    if (popupSearchEle) document.body.removeChild(popupSearchEle);
}

setTimeout(() => {
    addEventForButtonSearch();
}, 1000);

//----------------------------------------------------------------
// *** MENU MOBILE ***
//----------------------------------------------------------------

//Menu mobile
function addEventToggleMenuMobile() {
    const elBtn = document.querySelector(".btn-open-menu-mobile");
    const elModalMenu = document.querySelector(
        ".popup-menu-mobile#popup-menu-mobile"
    );

    if (!elBtn) return console.log("Not found element!");

    elBtn.addEventListener("click", (e) => {
        // createMenuMobile();
        elModalMenu.style.display = "block";
    });

    document.addEventListener("click", (event) => {
        if (elBtn.contains(event.target)) return;

        const containerSearch = document.querySelector(
            "#popup-menu-mobile #container-menu-mobile"
        );
        if (!containerSearch?.contains(event.target)) {
            elModalMenu.style.display = "none";
            // removePopupMenuMobile();
        }
    });
}

//----------------------------------------------------------------
// *** DETECT FIXED MENU SCROLL ***
//----------------------------------------------------------------

function addEventDetectMenuStuck() {
    let isCurrentlySticky = false;

    document.addEventListener("scroll", () => {
        const stickyElement = document.querySelector("header.menu-header#menu");

        if (!stickyElement) {
            return;
        }

        const container = stickyElement.parentElement;
        const elementTop = stickyElement.getBoundingClientRect().top;
        const containerTop = container.getBoundingClientRect().top;
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // Reset when at top of page
        if (scrollTop <= 10) {
            if (isCurrentlySticky) {
                stickyElement.classList.remove("is-sticky");
                isCurrentlySticky = false;
            }
            return;
        }

        // Use different thresholds for adding vs removing to prevent oscillation
        const shouldBeSticky = elementTop <= 0 && containerTop < -10;
        const shouldNotBeSticky = elementTop > 50 || containerTop > 20;

        if (shouldBeSticky && !isCurrentlySticky) {
            stickyElement.classList.add("is-sticky");
            isCurrentlySticky = true;
        } else if (shouldNotBeSticky && isCurrentlySticky) {
            stickyElement.classList.remove("is-sticky");
            isCurrentlySticky = false;
        }
    });
}

function addTheTradeDeskTrackingTagForButtonFindClinic() {
    const btnFindClinicDesktopEl = document.querySelector(
        "#btn-location-id-desktop"
    );
    const btnFindClinicMobileEl = document.querySelector("#btn-location-id");
    const btnFindClinicMenuMobileEl = document.querySelector(
        "#menu-mobile-btn-location-id"
    );
    const btnFindClinicFastControlEl = document.querySelector("#location-id");

    function fireTTDPixel() {
        (function () {
            var ttdPixel = document.createElement("script");
            ttdPixel.src =
                "https://insight.adsrvr.org/track/pxl/?adv=3dc1drw&ct=0:kqbhzhm&fmt=3";             // "https://insight.adsrvr.org/track/pxl/?adv=3dc1drw&ct=0:kqbhzhm&fmt=3";
            document.body.appendChild(ttdPixel);
        })();
    }

    [
        btnFindClinicDesktopEl,
        btnFindClinicMobileEl,
        btnFindClinicMenuMobileEl,
        btnFindClinicFastControlEl,
    ].forEach((btn) => {
        if (btn) {
            btn.addEventListener("click", fireTTDPixel);
        }
    });

    // if (btnFindClinicDesktopEl) {
    //     btnFindClinicDesktopEl.addEventListener("click", fireTTDPixel);
    // }
    // if (btnFindClinicDesktopEl) {
    //     btnFindClinicDesktopEl.addEventListener("click", fireTTDPixel);
    // }
    // if (btnFindClinicMobileEl) {
    //     btnFindClinicMobileEl.addEventListener("click", fireTTDPixel);
    // }
    // if (btnFindClinicFastControlEl) {
    //     btnFindClinicFastControlEl.addEventListener("click", fireTTDPixel);
    // }
}
document.addEventListener("DOMContentLoaded", () => {
    addEventDetectMenuStuck();
    addEventToggleMenuMobile();
    addTheTradeDeskTrackingTagForButtonFindClinic();

    // Open tab menu for mobile and tablet
    const menuItems = document.querySelectorAll(".click-mobile");

    menuItems.forEach((menuItem) => {
        const submenu = menuItem.querySelector(".child-menu-mobile");

        menuItem.addEventListener("click", function (event) {
            if (event.target.tagName === "A") {
                return;
            }
            event.preventDefault();

            menuItems.forEach((item) => {
                if (item !== menuItem) {
                    const otherSubmenu = item.querySelector(".child-menu-mobile");
                    if (otherSubmenu && otherSubmenu.classList.contains("open")) {
                        otherSubmenu.classList.remove("open");
                    }
                }
            });

            if (submenu) {
                submenu.classList.toggle("open");
            }
        });
    });
});