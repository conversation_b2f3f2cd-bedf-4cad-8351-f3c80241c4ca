package males

import "webgo/pkg/gos/templates"
import "webgo/views/v3/layouts"
import "webgo/hpv/malesv3/transport/responses"

templ MaleIndex(pages *responses.MaleV3Resp) {
    @layouts.Master(pages.Seo,[]templ.Component{head()}, FooterMaleIndex(pages.Options),maleIndexJS()) {
      <main class="container container-content-male">
        <div class="content__left">
            <div class="title-img">
                <img src={templates.AssetURL("/asset/images/male-27-45/title-banner-27-45.png")} alt="Dự phòng HPV cho Nam">
            </div>
            <p class="description-banner">*Nội dung này do Hội Y học Dự phòng Việt Nam cung cấp và được MSD tài trợ vì mục đích giáo dục CDC.<br>HPV and  Men - Fact Sheet, Centers for Disease Control and Prevention. Updated Apr 2022</p>
            <div class="swiper mySwiper-left">
                <div class="swiper-wrapper">
                    <div class="swiper-slide">
                    <picture>
                        <source srcset={templates.AssetURL("/asset/images/male/male-bg-1-800px.webp")} media="(max-width: 768px)" type="image/webp">
                        <source srcset={templates.AssetURL("/asset/images/male/male-bg-1-1200px.webp")} type="image/webp">
                        <img src={templates.AssetURL("/asset/images/male/male-bg-1-1200px.webp")} alt="Dự phòng HPV cho Nam">
                    </picture>
                    </div>
                    <div class="swiper-slide">
                    <picture>
                        <source srcset={templates.AssetURL("/asset/images/male/male-bg-2-800px.webp")} media="(max-width: 768px)" type="image/webp">
                        <source srcset={templates.AssetURL("/asset/images/male/male-bg-2-1200px.webp")} type="image/webp">
                        <img src={templates.AssetURL("/asset/images/male/male-bg-2-1200px.webp")} alt="Dự phòng HPV cho Nam">
                    </picture>
                    </div>
                    <div class="swiper-slide">
                    <picture>
                        <source srcset={templates.AssetURL("/asset/images/male/male-bg-3-800px.webp")} media="(max-width: 768px)" type="image/webp">
                        <source srcset={templates.AssetURL("/asset/images/male/male-bg-3-1200px.webp")} type="image/webp">
                        <img src={templates.AssetURL("/asset/images/male/male-bg-3-1200px.webp")} alt="Dự phòng HPV cho Nam">
                    </picture>
                    </div>
                </div>
                <div class="swiper-pagination-custom-new"></div>
            </div>
        </div>
        <div class="content__center">
            <span class="content-center__title">AI CẦN DỰ PHÒNG HPV?</span>
            <div class="group-box-male">
                <a class="male-box box-1" href="/du-phong-hpv-cho-nam-tu-9-18-tuoi" target="_blank" title="Du phong HPV nam 9-18 tuoi">
                    <div class="bg-overlay"></div>
                    <div class="male-box__img">
                        <img   
                        src={templates.AssetURL("/asset/images/male/male-9-18-small.webp")}
                        srcset={templates.AssetURL("/asset/images/male/male-9-18-small.webp 480w, /asset/images/male/male-9-18.webp 1024w")}
                        sizes="(max-width: 480px) 480px, 1024px"
                        alt="Male person in the age range of 9 to 18"
                        />
                    </div>
                    <span class="male-box__content">
                        TRẺ VỊ THÀNH NIÊN
                        <br>
                        9 - 18 TUỔI</span>
                    <button class="male-box__cta">
                        <span>Tìm hiểu thêm</span>
                        <img src={templates.AssetURL("/asset/images/male/arrow-right.svg")} alt="arrow-right">
                    </button>
                </a>
                <a class="male-box box-2" href="/du-phong-hpv-cho-nam-tu-19-26-tuoi" target="_blank" title="Du phong HPV nam 19-26 tuoi">
                    <div class="bg-overlay"></div>
                    <div class="male-box__img">
                        <img   
                        src={templates.AssetURL("/asset/images/male/male-19-26-small.webp")}
                        srcset={templates.AssetURL("/asset/images/male/male-19-26-small.webp 480w, /asset/images/male/male-19-26.webp 1024w")}
                        sizes="(max-width: 480px) 480px, 1024px"
                        alt="Male person in the age range of 19 to 26"
                        />
                    </div>
                    <span class="male-box__content">
                        THANH THIẾU NIÊN
                        <br>
                        19 - 26 TUỔI</span>
                    <button class="male-box__cta">
                        <span>Tìm hiểu thêm</span>
                        <img src={templates.AssetURL("/asset/images/male/arrow-right.svg")} alt="arrow-right">
                    </button>
                </a>
                <a class="male-box box-3" href="/du-phong-hpv-cho-nam-tu-27-45-tuoi" target="_blank" title="Du phong HPV nam 27-45 tuoi">
                    <div class="bg-overlay"></div>
                    <div class="male-box__img">
                        <img   
                        src={templates.AssetURL("/asset/images/male/male-27-45-small.webp")}
                        srcset={templates.AssetURL("/asset/images/male/male-27-45-small.webp 480w, /asset/images/male/male-27-45.webp 1024w")}
                        sizes="(max-width: 480px) 480px, 1024px"
                        alt="Male person in the age range of 27 to 45"
                        />
                    </div>
                    <span class="male-box__content">
                        NGƯỜI TRƯỞNG THÀNH
                        <br>
                        27 - 45 TUỔI</span>
                    <button class="male-box__cta">
                        <span>Tìm hiểu thêm</span>
                        <img src={templates.AssetURL("/asset/images/male/arrow-right.svg")} alt="arrow-right">
                    </button>
                </a>
            </div>
        </div>
        <div class="content__right">
            <div class="content-right__top">
                <div class="content-right__title">
                    Phòng vệ HPV<br>
                    <span>Ngay hôm nay!</span>
                </div>
                <div class="group-cta-male">
                    <div class="content-right__cta open-popup-locations">
                        THAM VẤN VỚI<br>CHUYÊN GIA Y TẾ
                    </div>
                    <a class="content-right__cta" href="/dia-diem-tu-van" target="_blank" title="dia-diem-tu-van">
                        Tìm địa điểm<br>tư vấn gần nhất
                    </a>
                </div>
            </div>
            <div class="content-right__img">
                <img   
                    src={templates.AssetURL("/asset/images/male/male-docter-small.webp")}
                    srcset={templates.AssetURL("/asset/images/male/male-docter-small.webp 480w, /asset/images/male/male-docter.webp 1024w")}
                    sizes="(max-width: 480px) 480px, 1024px"
                    alt="Male Doctor"
                />
            </div>
        </div>
    </main>  
    }
}

templ head() {
    <link rel="stylesheet" href={templates.AssetURL("/asset/js/lib/swiper/swiper-bundle.min.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/male/male.css")}>
}

templ maleIndexJS() {
    <script type="text/javascript" src={templates.AssetURL("/asset/js/lib/swiper/swiper-bundle.min.js")}></script>
    <script>
        document.addEventListener("DOMContentLoaded", () => {
            var swiper = new Swiper(".mySwiper-left", {
                loop: true,
                autoplay: {
                    delay: 5000,
                },
                pagination: {
                    el: ".swiper-pagination-custom-new",
                    clickable: true,
                },
            });
        });
    </script>
}

func FooterMaleIndex(options map[string]string) templ.Component{
    if banner, ok := options["footer-male-index"]; ok {
        return templ.Raw(banner)
    }
    return nil
}