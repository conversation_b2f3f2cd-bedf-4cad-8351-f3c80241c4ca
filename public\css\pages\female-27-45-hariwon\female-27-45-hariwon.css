.root-container {
    background: var(--neutral-900);
    background-size: cover;
}

.border {
    border: 1px solid black;
}

.menu-header {
    margin-top: 0;
}

.control-navigate-pagination .swiper-pagination-bullet {
    width: 1.5em;
    height: 1.5em;
    background-color: #ffffff !important;
    opacity: 1;
}

.control-navigate-pagination .swiper-pagination-bullet-active {
    width: 4em;
    transition: width .5s;
    border-radius: 2em;
    background: #05A8AE !important;
    border: 1px solid transparent;
}

img.lazyload {
    filter: blur(10px);
    transition: filter 0.3s;
}

img:not(.lazyload) {
    filter: blur(0);
}

/* Container */
@media (min-width: 320px) {

    .container,
    .container-sm {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
        max-width: 95% !important;
    }
}

@media (min-width: 576px) {

    .container,
    .container-sm {
        padding-left: 10px;
        padding-right: 10px;
        max-width: 98% !important;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 98% !important;
    }
}

@media (min-width: 992px) {

    .container,
    .container-lg,
    .container-md,
    .container-sm {
        max-width: 98% !important;
    }
}

@media (min-width: 1199px) {

    .container,
    .container-lg,
    .container-md,
    .container-sm,
    .container-xl {
        max-width: 100% !important;
    }
}

@media (min-width: 1400px) {

    .container,
    .container-lg,
    .container-md,
    .container-sm,
    .container-xl {
        max-width: 100% !important;
    }
}

@media (min-width: 1600px) {

    .container,
    .container-lg,
    .container-md,
    .container-sm,
    .container-xl {
        max-width: 100% !important;
    }
}

/* MENU - view width */
.main-menu {
    font-size: 0.7vw;
    padding: 0.5em 3em;
    border-radius: 5em;
}

.nav-menu__item img {
    width: 1.2em;
}

.main-menu .logos img {
    height: 4em;
}

.main-menu .nav-menu__item>a {
    border-radius: 5em;
    font-size: 0.7vw;
}

.main-menu .nav-menu .search-control svg {
    font-size: 3em;
}

.search-content .results-search {
    max-height: calc(100svh - 33em);
    font-size: 0.652vw;
}

.search-content .search-control input {
    font-size: 1.8em;
    padding: 0.6em 0.9em;
    border-radius: var(--radius-sm);
    border: 1.5px solid var(--primary-500);
    flex-grow: 1;
}

.search-content .search-control {
    max-width: 60%;
}

.search-content {
    font-size: 0.652vw
}

@media (min-width: 1100px) {
    .root-container {
        gap: 0;
    }
}

@media (max-width: 575px) {
    .main-menu {
        font-size: 1em;
    }

    .main-menu.container {
        max-width: 100% !important;
    }

    .search-content .results-search {
        max-height: calc(100svh - 33em);
        font-size: 0.8rem;
    }

    .search-content .search-control {
        max-width: 100%;
    }

    .search-content {
        font-size: 0.7rem
    }
}

/* Main banner */
.content-banner {
    width: fit-content;
    position: absolute;
    bottom: 20%;
    right: 10%;
    z-index: 1;
    aspect-ratio: 397 / 29;
    font-size: 0.625vw;
}

.female-banner-main {
    position: relative;
    margin-bottom: 3vw;
}

.img-banner {
    position: relative;
    display: flex;
    flex-direction: column;
}

.img-banner__thumbnail {
    box-sizing: border-box;
}

.img-banner:before {
    content: "";
    display: table;
    box-sizing: border-box;
    width: 0;
    padding-bottom: 56.25%
}

.img-banner-background {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    aspect-ratio: 16 / 9;
}

.img-banner-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 2rem;
    max-width: 100%;
}

.img-banner-background picture {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 2rem;
    max-width: 100%;
}

.btn-cta {
    background: rgba(0, 158, 164, 1);
    color: var(--neutral-900);
    border-radius: 5em;
    text-align: center;
    font-size: 2em;
    font-weight: 700;
    padding: 5px 0;
    box-shadow: 0 4px 4px rgba(14, 55, 54, 0.6);
    cursor: pointer;
    border: 1px solid #FFFFFF;
    display: block;
    box-sizing: border-box;
    width: fit-content;
    padding: 0.6em 3em;
    transition: 0.3s linear;
}

.btn-cta:hover {
    background: var(--secondary-700);
    color: var(--neutral-100);
    transform: translateY(-3px);
}

.banner-main__mobile {
    display: none;
}

@media (max-width: 575px) {
    .img-banner {
        position: relative;
        display: flex;
        flex-direction: column;
        padding-top: 31em;
    }

    .content-banner {
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        bottom: 30%;
        z-index: 1;
        right: 0;
        left: 0;
        margin: 0 !important;
        width: 100%;
    }

    .btn-cta {
        font-size: 1rem;
    }
}

@media (min-width: 576px) and (max-width: 768px) {
    section.female-banner-main {
        font-size: 0.65rem;
    }

    .btn-cta {
        font-size: 3em;
    }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
    section.female-banner-main {
        font-size: 0.8rem;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {}

/* Video Component */
.video {
    max-width: 95%;
    width: 100%;
    margin: 0 auto;
    font-size: 0.625vw;
}

.video__container {
    position: relative;
    width: 100%;
    aspect-ratio: 16 / 9;
    z-index: 1;
}

.video__thumb {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    border-radius: 2em;
    overflow: hidden;
    transition: opacity 0.5s ease;
    z-index: 2;
}

.video__thumb--fade-out {
    opacity: 0;
    pointer-events: none;
}

.video__img {
    width: 100%;
    height: auto;
    border-radius: 2em;
    display: block;
}

.video__play {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6em;
    height: 6em;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.video__play:hover {
    background-color: rgba(0, 0, 0, 0.9);
    transform: translate(-50%, -50%) scale(1.1);
}

.video__play-icon {
    width: 0;
    height: 0;
    border-left: 2em solid white;
    border-top: 1em solid transparent;
    border-bottom: 1em solid transparent;
    margin-left: 0.3em;
}

.video__iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 2em;
    overflow: hidden;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.video__iframe--fade-in {
    opacity: 1;
}

/* Fallback for older browsers */
@supports not (aspect-ratio: 16 / 9) {
    .video__iframe {
        height: 0;
        padding-bottom: 56.25%;
    }
}

.video__iframe iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 2em;
    display: block !important;
}

/* Responsive */
@media (max-width: 768px) {
    .video {
        max-width: 100%;
        font-size: 1vw;
    }

    .video__play {
        width: 4em;
        height: 4em;
    }

    .video__play-icon {
        border-left: 1.5em solid white;
        border-top: 0.75em solid transparent;
        border-bottom: 0.75em solid transparent;
    }
}

@media (max-width: 575px) {
    .video {
        font-size: 1.5vw;
    }

    .video__img {
        border-radius: 1em;
    }

    .video__iframe {
        border-radius: 1em;
    }
}

/* Quote Section */
.quote {
    margin: 4em 0;
    font-size: 1em;
}

.quote__content {
    display: flex;
    gap: 4em;
    align-items: flex-start;
    max-width: 120em;
    margin: 0 auto;
}

.quote__left {
    flex: 1;
}

.quote__title {
    font-size: 2.8em;
    font-weight: 700;
    color: var(--primary-500);
    line-height: 1.2;
    margin: 0;
}

.quote__right {
    flex: 1.5;
}

.quote__text {
    font-size: 1.6em;
    font-weight: 400;
    color: var(--neutral-100);
    line-height: 1.6;
    margin-bottom: 1.5em;
}

.quote__text:last-child {
    margin-bottom: 0;
}

/* Quote Responsive */
@media (max-width: 768px) {
    .quote {
        font-size: 1.2vw;
        margin: 3em 0;
    }

    .quote__title {
        font-size: 1.8em;
        text-align: left;
    }

    .quote__title br {
        display: none;
    }

    .quote__text {
        font-size: 1.2em;
    }

    .quote__content {
        flex-direction: column;
        gap: 2em;
    }

    .quote__text {
        font-size: 1em;
    }
}

@media (max-width: 575px) {
    .quote {
        font-size: 3vw;
        margin: 2em 0;
    }

    .quote__content {
        padding: 0 1em;
        gap: 1em;
    }

    .quote__title {
        font-size: 1.2em;
    }

    .quote__text {
        font-size: 1.1em;
    }

    .quote__title br:nth-child(2) {
        display: block;
    }

}

/* Banner Container - Shared for all banners */
.container__banner {
    max-width: 120em;
    margin: 0 auto;
    position: relative;
}

/* Banner Components - Common Styles */
.banner1,
.banner2,
.banner3 {
    position: relative;
    font-size: 0.652vw;
    margin-top: 5em;
}

.banner1__content,
.banner2__content,
.banner3__content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 40em;
    border-radius: 6em;
}

.banner1__content {
    background: linear-gradient(to top, #D4EBC2 0%, transparent 85%);
}

.banner2__content {
    flex-direction: row-reverse;
    background: linear-gradient(to top, #D4EBC2 0%, transparent 85%);
    align-items: flex-end;
}

.banner3__content {
    flex-direction: row-reverse;
    align-items: flex-end;
    background: linear-gradient(to top, #D4EBC2 0%, transparent 85%);
}

.banner1__woman {
    position: relative;
    z-index: 2;
    flex: 3;
    padding-left: 3em;
}

.banner2__woman {
    position: relative;
    z-index: 2;
    flex: 4;
    padding-right: 1em;
}

.banner3__woman {
    position: relative;
    z-index: 2;
    flex: 2;
    padding-right: 5em;
}

.banner1__woman-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    aspect-ratio: 614 / 716;
}

.banner2__woman-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    aspect-ratio: 1028 / 770;
}

.banner3__woman-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    aspect-ratio: 551 / 850;
}

.banner1__info,
.banner2__info,
.banner3__info {
    position: relative;
    text-align: center;
    z-index: 3;
    flex: 7;
    display: flex;
    flex-direction: column;
}

.banner1__info {
    transform: translate(-10em, 3em);
}

.banner2__info {
    flex: 6;
    transform: none;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 2em;
    margin-bottom: 5em;
}

.banner3__info {
    flex: 8;
    transform: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 5em;
}

.banner1__title {
    font-size: 1.5em;
    font-weight: 500;
    color: var(--neutral-100);
    margin-bottom: 0.5em;
}

.banner1__stats {
    display: flex;
    position: relative;
    justify-content: center;
    align-items: center;
    margin-bottom: 6em;
    flex-direction: column;
}

.line-1 {
    transform: translateX(-12.5em);
}

.line-2 {
    position: absolute;
    top: 4.5em;
    transform: translateX(7em);
}


.banner1__stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5em;
}

.banner1__number {
    font-size: 4em;
    font-weight: 800;
    background: linear-gradient(308.18deg, #FFDE00 -22.33%, #009885);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 0.9;
    display: inline;
}

.banner1__label {
    font-size: 2.4em;
    font-weight: 500;
    color: var(--primary-600);
    text-align: center;
    line-height: 1.2;
}

.banner1__desc {
    font-size: 1.5em;
    font-weight: 500;
    color: var(--neutral-100);
    margin-bottom: 1em;
    line-height: 1.35;
}

.banner1__desc strong {
    color: var(--primary-500);
    font-weight: 700;
    font-size: 1.2em;
}

.banner1__source {
    font-size: 0.7em;
    color: var(--neutral-400);
    font-weight: 400;
    line-height: 1.3;
    margin: 0 auto;
}

.banner1__virus {
    position: absolute;
    right: -2em;
    z-index: 1;
    bottom: 0;
    height: 100%;
    aspect-ratio: 506 / 696;
}

.banner2__virus {
    position: absolute;
    left: 0;
    z-index: 1;
    bottom: 0;
    height: 100%;
    aspect-ratio: 1026 / 740;
}

.banner3__virus {
    position: absolute;
    left: 0;
    z-index: 1;
    bottom: 0;
    height: 100%;
    aspect-ratio: 1580 / 756;
}

.banner1__virus-img,
.banner2__virus-img,
.banner3__virus-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.9;
}

/* Banner 2 Specific Styles */
.banner2__stats {
    display: flex;
    justify-content: center;
    align-items: center;
}

.banner2__stat-circle {
    width: 21em;
    height: 21em;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
}

.circle-gradient {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1;
}

.banner2__number {
    font-size: 6.4em;
    font-weight: 800;
    background: linear-gradient(308.18deg, #FFDE00 -22.33%, #009885);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 0.9;
    position: relative;
    z-index: 2;
}

.banner2__label1 {
    color: var(--neutral-100);
    z-index: 2;
    font-weight: 500;
    font-size: 1.6em;
}

.banner2__label {
    font-size: 1.6em;
    font-weight: 500;
    color: var(--neutral-100);
    position: relative;
    z-index: 2;
}

.banner2__text {
    text-align: left;
}

.banner2__desc {
    font-size: 1.5em;
    font-weight: 500;
    color: var(--neutral-100);
    line-height: 1.35;
}

.banner2__desc strong {
    color: var(--primary-500);
    font-weight: 700;
    font-size: 1.2em;
}

/* Banner 3 Specific Styles */
.banner3__stats {
    margin-bottom: 2em;
}

.banner3__stat-text {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    gap: 0.5em;
}

.banner3__prefix {
    font-size: 3.4em;
    font-weight: 500;
    color: var(--primary-500);
    line-height: 1;
}

.banner3__number {
    font-size: 9.6em;
    font-weight: 800;
    background: linear-gradient(308.18deg, #FFDE00 -22.33%, #009885);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 0.9;
    position: relative;
    z-index: 2;
}

.banner3__suffix {
    font-size: 3.4em;
    font-weight: 500;
    color: var(--primary-500);
    text-align: left;
    line-height: 1.3;
}

.banner3__text {
    text-align: left;
}

.banner3__desc {
    font-size: 2em;
    font-weight: 500;
    color: var(--neutral-100);
    line-height: 1.35;
    margin-bottom: 0.5em;
    text-align: center;
}

.banner3__desc strong {
    color: var(--primary-500);
    font-weight: 800;
    font-size: 1.2em;
}

.banner3__note {
    font-size: 1em;
    color: var(--neutral-80);
    line-height: 1.4;
    font-style: italic;
    text-align: center;
}

/* Banner Responsive */
@media (max-width: 1099px) {
    .fast-action-controls {
        flex-direction: row;
        bottom: 7em;
        left: 0;
        right: 0;
        margin: auto;
        justify-content: center;
        font-size: 1.25vw;
    }
}

@media (max-width: 768px) {

    .banner1,
    .banner2,
    .banner3 {
        overflow: visible;
    }

    .banner1__content,
    .banner2__content,
    .banner3__content {
        width: 100%;
    }
}

@media (max-width: 575px) {
    .fast-action-controls {
        flex-direction: row;
        bottom: 7em;
        left: 0;
        right: 0;
        margin: auto;
        justify-content: center;
        font-size: 2vw;
    }

    .container__banner {
        max-width: max-content;
    }

    .banner1,
    .banner2,
    .banner3 {
        font-size: 1.75vw;
        overflow: visible;
    }

    .banner1__stats {
        font-size: 0.85em;
    }

    .line-1 {
        transform: translateX(-15.5em);
    }

    .banner1__woman {
        flex: none;
        padding-left: 0;
        flex-shrink: 0;
        align-self: center;
        max-width: 60%;
    }

    .banner1__info {
        transform: none;
    }

    .banner1__content {
        flex-direction: column-reverse;
        min-height: auto;
        border-radius: 2em;
        width: 100%;
        align-items: center;
        gap: 2em;
    }

    .banner1__woman-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        aspect-ratio: 520 / 614;
    }

    .banner1__virus {
        position: absolute;
        left: 0;
        right: auto;
        z-index: 1;
        bottom: 0;
        height: 100%;
        aspect-ratio: 868 / 654;
        width: 100%;
    }

    .banner2__content,
    .banner3__content {
        flex-direction: column-reverse;
        align-items: center;
    }

    .banner2__stats {
        font-size: 0.8em;
    }

    .banner2__stat-circle::before {
        inset: 0.6em;
    }

    .banner2__virus {
        aspect-ratio: 828 / 968;
        width: 100%;
    }

    .banner2__woman-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        aspect-ratio: 823 / 644;
    }

    .banner3__virus {
        position: absolute;
        left: 0;
        z-index: 1;
        bottom: 0;
        height: 100%;
        width: 100%;
        aspect-ratio: 842 / 746;
        display: flex;
        justify-content: flex-start;
        align-items: end;
    }

    .banner3__woman {
        width: 53%;
        padding-right: 0;
    }

    .banner3__info {
        flex: none;
        font-size: 0.75em;
    }

    .banner3__virus-img {
        margin-bottom: 3em;
    }
}

@media (min-width: 319px) and (max-width: 376px) {
    .fast-action-controls {
        bottom: 10em;
        font-size: 2.2vw;
    }
}

/* custome pagination */
.control-navigate-pagination {
    display: flex;
    position: relative;
    justify-content: center;
    width: fit-content;
    margin: auto;
    gap: 2em;
    margin-top: 1.5em;
}

.control-navigate-pagination .swiper-pagination {
    bottom: -0.8%;
}

.control-navigate-pagination .swiper-pagination.bg-transparent {
    background-color: rgba(128, 128, 128, 0.3);
    border-radius: 2em;
    width: fit-content;
    padding: 1em;
    bottom: -4em !important;
}

.control-navigate-pagination .swiper-pagination-bullet {
    width: 1.5em;
    height: 1.5em;
    background-color: rgb(188, 188, 188) !important;
    opacity: 1;
}

.control-navigate-pagination .swiper-pagination-bullet-active {
    width: 4em;
    transition: width .5s;
    border-radius: 2em;
    background: #05A8AE !important;
    border: 1px solid transparent;
}

.control-navigate-pagination .swiper-pagination,
.control-navigate-pagination .swiper-button-prev,
.control-navigate-pagination .swiper-button-next {
    position: static;
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-navigate-pagination .swiper-button-prev,
.control-navigate-pagination .swiper-button-next {
    height: 4.5em;
    width: 4.5em;
    margin: 0;
    background-color: rgba(255, 255, 255, 0.32);
    padding: 1em !important;
    line-height: 2em;
    border-radius: 50%;
    color: #05A8AE;
    flex: none;
}

.control-navigate-pagination .swiper-button-prev::after,
.control-navigate-pagination .swiper-button-next::after {
    font-size: 1.5em !important;
    -webkit-text-stroke-width: .15em;
}

.control-navigate-pagination .swiper-button-prev:hover,
.control-navigate-pagination .swiper-button-next:hover {
    background-color: #05A8AE;
    color: white;
}

.control-navigate-pagination .btn-play,
.control-navigate-pagination .btn-pause {
    height: 2.5em;
    width: 2.5em;
    display: none;
    justify-content: center;
    background-color: rgba(128, 128, 128, 0.3);
    border-radius: 2em;
    align-items: center;
    font-size: 1.5em;
    color: #05A8AE;
}

.control-navigate-pagination .btn-play.active,
.control-navigate-pagination .btn-pause.active {
    display: flex;
}

.control-navigate-pagination .btn-play svg {
    margin-right: -0.2em;
}

.swiper-wrapper {
    height: 100%;
}

.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right {
    background-image: none;
}

/* Section Result */
.result {
    margin-top: 4rem;
    font-size: 0.625vw;
}

.container_result {
    max-width: 150em;
    margin: 0 auto;
}

section .header-section {
    display: flex;
    flex-direction: column;
    gap: 1em;
    margin-bottom: 2em;
}

section .subtitle-section {
    text-align: center;
    font-size: 2em;
    font-weight: 700;
    color: var(--third-500);
}

section .main-title-section {
    text-align: center;
    font-size: 3em;
    font-weight: 700;
    line-height: 1.2;
}

section.result .slide-result {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 4em;
    margin-top: 1em;
}

section.result .card {
    display: flex;
    height: 60%;
    max-width: 100%;
    border-radius: 3em;
    overflow: hidden;
    cursor: pointer;
    position: relative;
    color: black;
    box-shadow: 0 0.7em 2em rgba(0, 0, 0, 0.2);
    transition: 0.3s ease-out;
    background-color: hsl(0, 0%, 100%);
    background: linear-gradient(203.19deg, #FFF7B9 0%, #8BE6BA 100%);
    background-size: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    border: 1px solid var(--neutral-900);
    padding-bottom: 2em;
    flex-direction: column;
    justify-content: space-between;
}

section.result .card .content-top {
    margin: 2em;
    transition: 0.3s;
    font-size: 1.2em;
    text-shadow: 0 2px 5px #ffffff;
    position: relative;
    z-index: 2;
}

section.result .card .content-top p {
    font-size: 1.6em;
    font-weight: 400;
    line-height: 1.2;
    width: 60%;
}

section.result .card .content-top p:first-child {
    font-size: 2em;
}

section.result .card .content-top h4 {
    width: 82%;
    line-height: 1.1;
    font-size: 3em;
    margin: 0.3em 0;
}

section.result .card img {
    position: absolute;
    -o-object-fit: contain;
    object-fit: contain;
    bottom: 0;
    right: 0;
    opacity: 0.9;
    transition: opacity 0.2s ease-out;
}

section.result .card .img-bg-card1,
section.result .card .img-bg-card1 {
    z-index: -1;
}

section.result .card .img-bg-card1 {
    width: auto;
    right: -0.5em;
    height: 75%;
    display: block;
}

section.result .card .img-bg-card2 {
    right: 0;
    height: 80%;
    display: none;
}

section.result .card .card-content {
    position: relative;
    z-index: 10;
    background-color: rgba(255, 255, 255, 0.7607843137);
    width: -moz-fit-content;
    width: fit-content;
    max-width: 90%;
    padding: 1em 2em;
    border-radius: 2em;
    border: 1px solid white;
    margin: 0 2em;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
}

section.result .card .card-content h2 {
    margin: 0;
    line-height: 1;
    font-size: 2.4em;
    font-weight: 600;
    color: var(--third-500);
    text-transform: uppercase;
}

section.result .card .card-content h2 p {
    display: none;
}

section.result .card .card-content .card-text {
    opacity: 0;
    display: none;
    transition: opacity 0.3s ease-out;
    font-size: 2em;
    margin-top: 1rem;
    text-align: justify;
    line-height: 1.25;
}

section.result .card .card-content .card-text small {
    font-size: 0.7em;
    line-height: 1;
}

section.result .card.swiper-slide-active {
    width: 42em;
    max-height: 100%;
    height: calc(100% - 2em);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

section.result .card.swiper-slide-active .content-top {
    font-size: 1.35em;
}

section.result .card.swiper-slide-active h2 {
    width: 90%;
    border-end-end-radius: 0rem;
    border-bottom-left-radius: 0rem;
    text-transform: none;
}

section.result .card.swiper-slide-active .card-content {
    margin: 0 auto !important;
    padding: 2em;
    font-size: 0.8em;
}

section.result .card.swiper-slide-active .card-content h2 span {
    display: none;
}

section.result .card.swiper-slide-active .card-content h2 p {
    display: inline-block;
}

section.result .card.swiper-slide-active .img-bg-card2 {
    display: block;
}

section.result .card.swiper-slide-active .img-bg-card1 {
    display: none;
}

section.result .card.swiper-slide-active .card-text {
    opacity: 1;
    transition: opacity 0.5s 0.1s ease-in;
    display: block;
}

section.result .card.active img {
    transition: opacity 0.3s ease-in;
    opacity: 1;
}

section.result .card-2 {
    background: linear-gradient(161.89deg, #F3FFC1 0.82%, #5FD675 247.31%);
}

section.result .card-3 {
    background: linear-gradient(45.1deg, #BCE86B 0%, #F6F8E5 71.9%);
}

section.result .card-4 {
    background: linear-gradient(225deg, rgba(255, 255, 255, 0.7) 0%, rgba(154, 242, 130, 0.7) 100%);
}

section.result .swiper-3d .swiper-wrapper {
    height: 60em;
    align-items: center;
}

section.result .swiper {
    padding: 0 3.5em;
}

@media (max-width: 1599px) {
    .slide-result {
        font-size: 0.9rem;
    }

    section.result .card .img-bg-card1 {
        height: 70%;
    }
}

@media (max-width: 1399px) {
    .slide-result {
        font-size: 0.8rem;
    }

    section.result .card .img-bg-card1 {
        height: 75%;
    }
}

@media (max-width: 1200px) {
    .slide-result {
        font-size: 0.75rem;
    }

    section.result .card .img-bg-card1 {
        height: 65%;
    }
}

@media (max-width: 991px) {
    .slide-result {
        font-size: 0.9rem;
    }

    section.result .card .img-bg-card1 {
        height: 65%;
    }
}

@media (max-width: 767px) {
    section.result {
        font-size: 0.6vw;
    }

    section.result .card .img-bg-card2 {
        right: -9%;
        top: 14em;
        max-height: 90%;
        height: auto;
    }

    section.result .card-4 .img-bg-card2 {
        right: -7%;
    }

    section.result .card .content-top h4 {
        font-size: 2.3em;
    }

    section.result .card .content-top p {
        width: 65%;
    }

    section.result .swiper-3d .swiper-wrapper {
        height: 80em;
    }

    section.result .card.swiper-slide-active {
        height: calc(100% - 2em);
    }

    section.result .card.swiper-slide-active .card-text {
        font-size: 2.2em;
    }
}

@media (max-width: 575px) {
    section.result .swiper-3d .swiper-wrapper {
        height: 65em;
    }

    section .main-title-section {
        font-size: 2rem;
    }

    section.result .card .content-top h4 {
        font-size: 2em;
    }

    section.result .card .content-top p {
        font-size: 1.4em;
    }

    section .subtitle-section {
        font-size: 1.5rem;
    }

    section.result .card.swiper-slide-active .card-text {
        font-size: 2em;
    }
}

@media (max-width: 391px) {
    .slide-result {
        font-size: 0.7rem;
    }

    section.result .card .img-bg-card2 {
        right: -9%;
        top: 14em;
        max-height: 90%;
        height: auto;
    }

    section.result .card.swiper-slide-active .content-top {
        font-size: 1.2em;
    }

    section.result .card .content-top h4 {
        font-size: 2em;
    }

    section.result .card .content-top p {
        width: 65%;
    }

    section.result .swiper-3d .swiper-wrapper {
        height: auto;
        min-height: 45em;
    }

    section.result .card.swiper-slide-active {
        height: auto;
        min-height: 40em;
    }

    section.result .card.swiper-slide-active .card-text {
        font-size: 1.4em;
    }
}

/* Section Article */
section.article {
    font-size: 0.625vw;
}

section.article .header-section {
    font-size: 3.5em;
    font-weight: 700;
    text-align: center;
    margin-bottom: 0;
    margin-top: 5svh;
}

.slide-article {
    padding: 5em 1em;
}

.slide-article .article-item {
    border-radius: 2em;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    text-align: center;
    font-weight: 400;
    position: relative;
    background: linear-gradient(150deg, rgba(252, 254, 244, 1) 0%, rgba(158, 237, 241, 1) 100%);
}

.slide-article .article-item__title {
    text-transform: uppercase;
    font-size: 1.8em;
    padding: 0.8em 1em;
    color: var(--primary-800);
    line-height: 1.3;
    height: 6em;
}

.slide-article .article-item__title span {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.5em;
}

.slide-article .article-item__avatar {
    aspect-ratio: 2.8 / 2;
    overflow: hidden;
}

.slide-article .article-item__avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slide-article .article-item__des {
    display: none;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.5em;
}

.slide-article .article-item__action {
    position: absolute;
    margin: auto;
    left: 0;
    right: 0;
    bottom: 1.5em;
}

.slide-article .article-item__action a {
    color: var(--primary-500);
    display: flex;
    flex-direction: row;
    width: fit-content;
    align-items: center;
    gap: 1em;
    padding: 0.7em 1.2em;
    margin: auto;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);
    background-color: var(--primary-100);
    color: var(--primary-800);
    cursor: pointer;
    border-radius: 1em;
    font-size: 1.5em;
    transition: all 0.3s ease-in;
}

.slide-article .article-item__action a:hover {
    background-color: var(--primary-600);
    color: white;
}

@media (max-width: 767px) {
    section.article {
        font-size: 0.6rem;
    }

    .slide-article {
        padding: 2em 0;
    }

    .slide-article .control-navigate-pagination {
        justify-content: center;
        width: 100%;
    }

    .slide-article .control-navigate-pagination .swiper-pagination {
        display: none;
    }
}

/* SECTION - HPV-Diseases */
.common-hpv-diseases {
    position: relative;
    width: 100%;
    font-size: 0.625vw;
    z-index: 0;
    contain: content;
}

.common-hpv-diseases__wrapper {
    height: inherit;
    display: flex;
    position: relative;
    flex-direction: column;
    align-items: center;
    z-index: 0;
    padding-bottom: 2vw;
    background: #ffffff;
    background: linear-gradient(180deg, rgba(255, 255, 255, 1) 55%, rgba(193, 244, 234, 1) 91%);
}


.common-hpv-diseases__title {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.6em;
    position: relative;
}

.title-main,
.title-highlight {
    font-size: 2em;
    font-weight: 600;
}

.title-highlight sup {
    font-size: 0.6em;
}

.title-highlight {
    background: linear-gradient(90deg, #00D6E2 0%, #009585 100%);
    border-radius: 50px;
    padding: 0.2em 1.5em;
    color: white;
}

.title-classify {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 4em;
}

.common-hpv-diseases__contents {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    position: relative;
    margin-top: 2vw;
}

.common-hpv-diseases__areas {
    width: 35%;
}

.common-hpv-diseases__areas.area-left,
.common-hpv-diseases__areas.area-right {
    display: flex;
    flex-direction: column;
    gap: 2.5em;
    justify-content: center;
}

.common-hpv-diseases__statistics {
    display: flex;
    flex-direction: column;
    gap: 7vw;
    margin-top: -18em;
}

.common-hpv-diseases__areas:not(.area-left, .area-right) {
    position: relative;
}

.common-hpv-diseases__image {
    filter: grayscale(1) blur(1px);
    transition: filter 0.15s linear;
    will-change: filter;
}

.common-hpv-diseases__image.male {
    position: relative;
    z-index: 1;
    aspect-ratio: 798 / 1639;
    width: 100%;
}

.common-hpv-diseases__image.male.active {
    filter: grayscale(0) blur(0);
    z-index: 3;
}

.common-hpv-diseases__image.female.active {
    filter: grayscale(0) blur(0);
    transform: scale(1.02);
    z-index: 4;
}

.common-hpv-diseases__image:not(.active):hover {
    transform: scale(1.02);
}

.common-hpv-diseases__statistic {
    position: relative;
    display: flex;
    flex-direction: column;
    text-align: center;
    align-items: center;
    padding: 2em;
    overflow: hidden;
    border-radius: 50%;
    justify-content: center;
    color: #adadad;
    width: 20em;
    height: 20em;
    transition: color 0.3s ease;
}

.area-left .common-hpv-diseases__disease-percent {
    z-index: 1;
    position: relative;
}

.area-left .common-hpv-diseases__disease-name {
    z-index: 1;
    position: relative;
}

.common-hpv-diseases__statistic.active {
    color: #0D867A;
}

.common-hpv-diseases__statistic.statistic-female.active {
    color: #ffdf1b;
}


.common-hpv-diseases__statistic.statistic-female.active .common-hpv-diseases__disease-name {
    color: #39514D;
}

.common-hpv-diseases__statistic.active .common-hpv-diseases__disease-name {
    color: #39514D;
}

.male-color .common-hpv-diseases__disease-percent {
    color: var(--third-500) !important;
}

.common-hpv-diseases__statistic::before {
    content: '';
    position: absolute;
    background: #ddffa633;
    transform: rotate(180deg);
    width: 100%;
    height: 100%;
    aspect-ratio: 1;
    z-index: 0;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background 0.3s ease;
}

.common-hpv-diseases__statistic {
    will-change: transform;
    transform: translateZ(0);
}
.common-hpv-diseases__statistic:hover {
    background: #d7fb9d80;
    transform: scale(1.2) translateZ(0);
    transition: transform 0.4s ease, background 0.4s ease;
}

.area-right .common-hpv-diseases__statistic {
    padding: 2em;
}

.area-right .common-hpv-diseases__statistic::before {
    transform: rotate(0);
}

.area-right .common-hpv-diseases__disease-name {
    position: relative;
    z-index: 1;
}

.area-right .common-hpv-diseases__disease-percent {
    position: relative;
    z-index: 1;
}

.common-hpv-diseases__disease-percent {
    font-size: 4em;
    line-height: 1.2;
    font-weight: 800;
}

.common-hpv-diseases__disease-name {
    font-size: 1.3em;
    font-weight: 500;
}

.orbit-circle {
    position: absolute;
    width: 71em;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 0;
}

.flex-end {
    align-items: flex-end;
}

.flex-start {
    align-items: flex-start;
}

.align-left {
    left: 20%;
}

.align-right {
    right: 20%;
}

.diseases-note {
    font-size: 1em;
    color: #555;
    margin-top: 2em;
}

@media only screen and (min-width: 1599px) {
    .title-main>br {
        display: none;
    }

    .diseases-note>br {
        display: none;
    }

    .orbit-circle {
        position: absolute;
        width: 71em;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 0;
    }

    .common-hpv-diseases__statistic {
        width: 20em;
        height: 20em;
    }

    .align-left {
        left: -2%;
    }

    .align-right {
        right: -2%;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .title-main>br {
        display: none;
    }

    .diseases-note>br {
        display: none;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .title-main>br {
        display: none;
    }

    .diseases-note>br {
        display: none;
    }

    .common-hpv-diseases__areas:not(.area-right, .area-left) {
        width: 30%;
    }

    .common-hpv-diseases__areas.area-right,
    .common-hpv-diseases__areas.area-left {
        position: relative;
        justify-content: center;
        width: 30%;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .common-hpv-diseases__areas:not(.area-left, .area-right) {
        width: 40%;
    }

    .common-hpv-diseases__contents {
        font-size: 1.1em;
    }

    .title-main>br {
        display: none;
    }

    .diseases-note>br {
        display: none;
    }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {

    .common-hpv-diseases__contents {
        font-size: 1.1em;
    }

    .title-main>br {
        display: none;
    }
}

@media (min-width: 576px) and (max-width: 768px) {
    .common-hpv-diseases {
        font-size: 0.8vw;
    }

    .common-hpv-diseases__wrapper {
        padding: 1em;
    }

    .common-hpv-diseases__contents {
        flex-direction: column;
        align-items: center;
        font-size: 1em;
    }

    .common-hpv-diseases__areas {
        width: 100% !important;
        position: static !important;
        transform: none !important;
    }

    .common-hpv-diseases__areas:not(.area-left, .area-right) {
        width: 80% !important;
        margin: 2em auto;
        order: -1;
    }

    .common-hpv-diseases__statistics {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1em;
        margin: 0;
        padding: 1em;
    }

    .common-hpv-diseases__statistic {
        position: static;
        width: 20em;
        height: 20em;
        margin: 0 auto;
    }

    .common-hpv-diseases__disease-percent {
        font-size: 2.5em;
    }

    .common-hpv-diseases__disease-name {
        font-size: 1em;
    }

    .title-main,
    .title-highlight {
        font-size: 1.5em;
        text-align: center;
    }

    .diseases-note {
        font-size: 0.8em;
        text-align: center;
        padding: 0 1em;
    }

    .orbit-circle {
        display: none;
    }
}

@media (max-width: 575px) {
    .common-hpv-diseases {
        font-size: 1.5vw;
    }

    .common-hpv-diseases__areas:not(.area-right, .area-left) {
        width: 33em;
        z-index: 1;
    }

    .common-hpv-diseases__areas.area-right,
    .common-hpv-diseases__areas.area-left {
        position: relative;
        justify-content: center;
        width: 40%;
    }

    .common-hpv-diseases__title {
        font-size: 1.2em;
    }

    .common-hpv-diseases__areas.area-right {
        right: 0;
    }

    .btn-cta {
        font-size: 4em;
        line-height: 1.4;
        padding: 1em 2em;
        min-width: 17em;
    }

    .common-hpv-diseases__contents {
        font-size: 1em;
        margin-top: 5vw;
    }

    .common-hpv-diseases__statistics {
        margin-top: 0;
    }

    .orbit-circle {
        top: 53%;
        width: 41em;
        aspect-ratio: 1;
    }

    .common-hpv-diseases__statistic {
        width: 10em;
        height: 10em;
    }

    .common-hpv-diseases__disease-percent {
        font-size: 2em;
    }

    .common-hpv-diseases__disease-name {
        font-size: 0.9em;
    }

    .title-main, .title-highlight {
        font-size: 2em;
        font-weight: 600;
    }


    .trans2 {
        transform: scale(1.1);
    }

    .trans3 {
        transform: scale(1.2);
    }

    .trans4 {
        transform: scale(1.3);
    }
}

@media (min-width: 319px) and (max-width: 376px) {


    .common-hpv-diseases__areas.area-right,
    .common-hpv-diseases__areas.area-left {
        width: 35% !important;
    }

    .common-hpv-diseases__statistic {
        width: 10em;
        height: 10em;
    }

}

/* Footer Main Content */

.footer-main-content-mobile {
    display: none;
}

.footer-main-content-male-page.mobile {
    background-color: #05A8AE !important;
    text-align: justify;
    padding: 10px;
    color: white;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.footer-main-content-male-page.mobile .content {
    margin: 2em auto 0 auto;
}

.footer-main-content-male-page.mobile .content b {
    font-size: 1.4em;
}

.footer-main-content-male-page.mobile .content ul {
    padding: 0;
    text-align: left;
    margin: auto;
}

.footer-main-content-male-page.mobile .content ul li {
    margin-bottom: 10px;
    font-size: 1.2em;
    overflow-wrap: break-word;
    display: list-item;
    list-style-type: none;
}

.footer-main-content-male-page.mobile .footer__content img {
    width: 7em;
    height: auto;
    overflow-x: hidden;
}

.footer-main-content-male-page.mobile .footer__content {
    display: none;
}

.footer-main-content-male-page.mobile .paragraph-2-col {
    -moz-column-count: 2;
    column-count: 2;
    -moz-column-gap: 3em;
    column-gap: 3em;
    padding: 0 2em;
}

.footer-main-content-mobile .footer__content {
    font-size: 0.8em;
    background: var(--secondary-500);
}

.footer-main-content-mobile .footer__content img {
    width: 7em;
    height: auto;
}

.footer-main-content-mobile .footer__content h3 {
    color: var(--primary-500);
}

.footer-main-content-mobile .footer__content .direction a {
    color: var(--primary-500);
}

.footer-main-content-mobile .container-footer {
    position: relative;
    max-width: 600px;
}

.footer-main-content-mobile .content-footer {
    max-height: 20px;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.footer-main-content-mobile .show-more {
    font-size: 1.5em;
    position: absolute;
    width: 100%;
    text-align: end;
    height: 20px;
    top: 0;
    right: 0;
    cursor: pointer;
    color: var(--natural-900);
    display: block;
    background-color: linear-gradient(to bottom, rgba(255, 255, 255, 0.4) 0%, rgb(255, 255, 255) 100%);
}

.footer-main-content .content ul li {
    font-size: 1em;
}

.bg-secondary {
    background-color: #05A8AE;
}

.footer-main-content {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    padding: 2em;
}

.footer-main-content.paragraph-2-col {
    font-size: 0.625vw;
}

@media (max-width: 1099px) {
    .footer-main-content-male-page .footer__content {
        display: flex;
    }

    .footer-main-content .content ul li {
        margin-bottom: 10px;
        font-size: 1.2em;
    }

    .container-content .main-container {
        overflow-x: hidden;
    }
}

@media (max-width: 768px) {
    .footer-main-content-male-page {
        font-size: 0.7rem;
    }

    .footer-main-content-male-page .content {
        padding: 0;
    }

    .footer-main-content .content ul li {
        margin-bottom: 10px;
        font-size: 1.2em;
    }

    .footer__content .code-footer {
        display: contents;
        white-space: nowrap;
    }

    .footer-main-content.paragraph-2-col {
        display: none;
    }

    .footer-main-content-mobile {
        display: block;
    }

    .footer-main-content-mobile .container-footer {
        position: relative;
        max-width: 100%;
    }

    .footer-main-content-mobile .footer__content {
        font-size: 1.2em;
        background: #05A8AE;
    }

    .footer-main-content-mobile .footer__content h3 {
        color: var(--natural-100);
    }

    .footer-main-content-mobile .footer__content .direction a {
        color: var(--neutral-300);
    }
}

@media ((max-width: 575px)) {
    .footer-main-content {
        display: none;
    }

    .footer-main-content-mobile {
        display: block;
    }

    .footer-main-content-male-page.mobile {
        display: block;
    }

    #footer-all {
        display: none;
    }

    .footer-main-content-mobile .footer__content {
        font-size: 1em;
        background: #05A8AE;
    }

    .footer-main-content-mobile .footer__content h3 {
        color: var(--natural-100);
    }

    .footer-main-content-mobile .footer__content .direction a {
        color: var(--neutral-300);
    }
}

/* Section - Protection */
.container-protection {
    align-items: center;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.protection {
    width: 100%;
    height: 100%;
    font-size: 0.652vw;
}

.protection-body-img {
    max-width: 90%;
    width: 94.5em;
    aspect-ratio: 1800 / 929;
    position: relative;
    top: 6em;
}

.protection-box {
    max-width: 100em;
    border-radius: 3em;
    position: relative;
    padding: 1em 2em 3em 2em;
    background: linear-gradient(0deg, rgba(247, 255, 230, 1) 0%, rgb(238 255 241 / 50%) 89%, transparent 250%);
    backdrop-filter: blur(10px);
}

.protection-text-box {
    text-align: center;
    display: flex;
    margin-left: auto;
    margin-right: auto;
    gap: 1em;
    align-items: center;
    justify-content: center;
}

.protection-text-box p {
    font-size: 3.5em;
    color: #000000;
}

.protection-text-box h2 {
    font-size: 3.9em;
    color: var(--primary-500);
    font-weight: 600;
    line-height: 1.2;
    margin: 0.5em;
    position: relative;
    width: fit-content;
    margin: auto;
}

.protection-btn {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    width: 100%;
    gap: 2em;
}

.protection-btn-left,
.protection-btn-right {
    box-sizing: border-box;
    background-color: var(--primary-500);
    color: white;
    border-radius: 4em;
    text-align: center;
    font-size: 1.4em;
    line-height: 1.1;
    font-weight: 700;
    padding: 0.8em 4em;
    box-shadow: 0 0.4em 0.4em rgb(14 55 54 / 15%);
    cursor: pointer;
    border: 1px solid #ffffff;
    transition: 0.3s linear;
}

.protection-btn-left:hover,
.protection-btn-right:hover {
    background-color: var(--primary-900);
    color: white;
    transform: translateY(-3px);
}

.hpv-section {
    background: linear-gradient(0deg, #f7ffe6 30%, rgba(209, 255, 218, 0) 100%);
    padding: 2em 1em;
    font-family: Arial, sans-serif;
    text-align: center;
}

.hpv-top {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3em;
    flex-wrap: wrap;
    margin-bottom: 2em;
}

.circle-border {
    width: 13em;
    height: 13em;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.circle-border .circle-gradient {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1;
}

.circle-border-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    position: relative;
    z-index: 2;
}

.circle-border-content p {
    font-size: 1.2em;
    color: #000;
    line-height: 1;
    margin: 0;
}

.million {
    font-size: 4.5em;
    font-weight: 900;
    background: linear-gradient(313.73deg, #FFDE00 -21.76%, #009885 74.4%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 0.8;
    display: block;
    margin: 0.1em 0;
}

.hpv-right-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    max-width: 100%;
    text-align: left;
}

.hpv-right-text p {
    font-size: 1.2em;
    font-weight: 600;
    margin-bottom: 0.4rem;
}

.hpv-subtitle {
    background: #008f84;
    color: white;
    padding: 0.5em 1em;
    border-radius: 1.5em;
    font-weight: 500;
    display: inline-block;
    font-size: 1.3em;
}

.highlight-yellow {
    background: #FEE003;
    padding: 0.5em 1em;
    border-radius: 1em;
}

.hpv-right-text small {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.7em;
    color: #555;
}

.hpv-list {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 6em;
    margin: 3em auto;
    max-width: 90em;
}

.hpv-item {
    display: flex;
    gap: 1em;
    align-items: center;
    text-align: left;
}

.triple-text {
    font-size: 1.5em;
    font-weight: 600;
}

.hpv-item img {
    width: 5em;
    height: 5em;
}

.hpv-buttons {
    display: flex;
    justify-content: center;
    gap: 2em;
    flex-wrap: wrap;
}

.btn-action {
    background: #00a98f;
    color: white;
    border-radius: 3em;
    padding: 1em 2em;
    font-weight: bold;
    text-decoration: none;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    transition: 0.2s ease;
    text-align: center;
    min-width: 240px;
}

.btn-action:hover {
    background: #007a6b;
}


@media only screen and (max-width: 992px) {
    .container-protection {
        font-size: 0.85rem;
    }

    .container-protection .protection-box {
        width: 90%;
        padding: 3em 6em;
    }
}

@media (max-width: 768px) {
    .container-protection {
        font-size: 0.75rem;
    }

    .container-protection .protection-box {
        width: 100%;
        padding: 3em 4em;
        background: linear-gradient(0deg, rgba(247, 255, 230, 1) 0%, rgb(238 255 241 / 50%) 89%, transparent 250%);
        backdrop-filter: blur(10px);
    }
}

@media (max-width: 576px) {
    .container-protection {
        font-size: 0.55rem;
    }

    .invi {
        display: inline-block !important;
        margin-left: 0.3em;
    }

    .container-protection .protection-text-box h2 {
        font-size: 3.2em;
        margin: 0.5em 1.5em;
    }

    .container-protection .protection-btn {
        flex-direction: column;
    }

    .container-protection .protection-btn-left,
    .container-protection .protection-btn-right {
        font-size: 2.2em;
        width: fit-content;
        margin: auto;
    }

    .protection-text-box {
        display: flex;
        justify-content: center;
    }

    .container-protection .protection-box {
        font-size: 1.2vw;
    }

    .hpv-list {
        flex-direction: column;
        gap: 2em;
        margin: 2em auto;
        width: 85%;
        align-items: flex-start;
    }

    .hpv-item {
        width: 100%;
        justify-content: flex-start;
        gap: 2em;
    }

    .triple-text {
        font-size: 2.2em;
        font-weight: 400;
    }

    .triple-text br {
        display: none;
    }

    .hpv-right-text {
        font-size: 1.1em;
    }

    .invi {
        display: block;
    }

    .circle-border-content p {
        font-size: 1em;
        color: #000;
        line-height: 1;
        margin: 0;
    }
}