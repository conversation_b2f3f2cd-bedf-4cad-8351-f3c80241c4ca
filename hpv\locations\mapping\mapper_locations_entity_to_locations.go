package mapping

import (
	"fmt"
	"webgo/hpv/entity"
	"webgo/hpv/locations/transport/responses"
)

/**
 * Mapper Location Entity to LocationsNew
 * page locations
 */
func MapperLocationsEntityToLocations(locations []*entity.LocationsEntity) *[]responses.Locations {
	var datas []responses.Locations
	for _, location := range locations {

		var lat string
		var lng string
		if location.Lat != "" {
			lat = fmt.Sprintf("%f", location.Lat)
		}
		if location.Lng != "" {
			lng = fmt.Sprintf("%f", location.Lng)
		}
		datas = append(datas, responses.Locations{
			Id:       location.Id,
			Name:     location.Name,
			PagePath: location.PagePath,
			Img:      location.Img,
			Address:  location.Address,
			Phone:    location.Phone,
			WorkTime: location.WorkTime,
			LinkMap:  location.LinkMap,
			Map:      location.Map,
			Lat:      lat,
			Lng:      lng,
		})
	}

	return &datas
}
