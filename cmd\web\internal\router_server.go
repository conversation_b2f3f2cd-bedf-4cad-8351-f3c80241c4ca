package internal

import (
	"webgo/hpv"
	"webgo/pkg/gos/midd"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/configs"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/compress"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
)

func RouterServer(app *fiber.App, serviceCtx sctx.ServiceContext) {

	app.Use(logger.New(logger.Config{
		Format: `{"ip":${ip}, "timestamp":"${time}", "status":${status}, "latency":"${latency}", "method":"${method}", "path":"${path}"}` + "\n",
	}))

	app.Use(compress.New())
	app.Use(cors.New())

	if serviceCtx.EnvName() == configs.AppProd {
		// csrfConfig := csrf.ConfigDefault
		// csrfConfig.CookieHTTPOnly = true
		// csrfConfig.CookieSecure = true
		// csrfConfig.CookieSameSite = "none"

		// app.Use(csrf.New(csrfConfig))
		app.Use(midd.Recovery(serviceCtx))

	}
	// Public folder
	app.Static("/asset", configs.PublicPath)

	if serviceCtx.EnvName() == configs.AppDev {
		app.Static("/media", configs.PathImgHpv)
	}

	hpv.SetupRoutesWebHpvV3(app, serviceCtx)
	// hpv.SetupRoutesWebHpv(app, serviceCtx)

}
