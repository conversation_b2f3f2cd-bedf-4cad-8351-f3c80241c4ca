<!-- Open head tag in layout pagespeed.html -->
<link type="text/css" rel="stylesheet" href="{{site}}/asset/js/lib/swiper/swiper-bundle.min.css" />
<link type="text/css" rel="stylesheet" href="{{site}}/asset/css/components/fast-action-controls.css">
<link type="text/css" rel="stylesheet" href="{{site}}/asset/css/pages/page-404/page-404.css">

{{template "headers"}}

<!-- Container-content -->
<main>
    <section class="page_404">
        <div class="container">
            <div class="row">
                <div class="col-sm-12 ">
                    <div class="col-sm-10 col-sm-offset-1  text-center">
                        <div class="four_zero_four_bg">



                        </div>

                        <div class="contant_box_404">
                            <h3 class="h2">
                                Trang không tồn tại
                            </h3>

                            <p><PERSON>ui lòng truy cập trang khác!</p>

                            <a href="{{site}}" class="link_404">Trang chủ</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>
</div>
{{template "icon_svg"}}

{{ template "js_main"}}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const contentContainers = document.querySelectorAll('.content-policy');

        contentContainers.forEach((content, index) => {
            const showMoreButton = document.createElement('div');
            showMoreButton.classList.add('show-more');
            showMoreButton.innerHTML = '&#9660'; // Use &#9650; for chevron-up
            content.parentNode.insertBefore(showMoreButton, content.nextSibling);

            showMoreButton.addEventListener('click', function () {
                if (content.style.maxHeight) {
                    content.style.maxHeight = null;
                    showMoreButton.innerHTML = '&#9660'; // Use &#9650; for chevron-up
                } else {
                    content.style.maxHeight = content.scrollHeight + 'px';
                    showMoreButton.innerHTML = '&#9650'; // Use &#9660; for chevron-down
                }
            });
        });
    });
</script>