package mapping

import (
	"webgo/hpv/entity"
	"webgo/hpv/homev3/transport/responses"
)

func MapperPostToHomeItemResq(postsHome []*entity.PostEntity) []responses.HomeItem {
	if postsHome == nil {
		return nil
	}
	var res []responses.HomeItem
	for _, post := range postsHome {
		res = append(res, responses.HomeItem{
			PagePath:    post.PagePath,
			Title:       post.Title,
			Img:         post.Img,
			Description: post.Description,
			Views:       post.Views,
		})
	}

	return res
}