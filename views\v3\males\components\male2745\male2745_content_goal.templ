package male2745

import "webgo/pkg/gos/templates"

templ Male2745ContentGoal() {
    <section class="container goal">
        <div class="swiper swiper-container-goal">
            <div class="boder-bg-slide">
                <div class="slide-to-show">
                    <svg style="transform: rotate(180deg);" class="icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <use xlink:href="#read-more-line-icon" />
                    </svg>
                    Lướt để xem
                </div>
            </div>
            <div class="swiper-wrapper">
                <div class="swiper-slide slide-1">
                    <div class="content-think-area">
                    <div class="pop-up-think right">Đ<PERSON>u là những mục tiêu <br> bạn muốn thực hiện?</div>
                    </div>
                    <div class="img-swip-area">
                    <img src={templates.AssetURL("/asset/images/male-27-45/1-male.webp")} alt="dau la nhung muc tieu cua ban">
                    </div>
                </div>
                <div class="swiper-slide slide-2">
                    <h2 class="title-slide">
                    Đâu là những mục tiêu <br>
                    bạn muốn thực hiện?
                    </h2>
                    <div class="content-think-area">
                    <div class="pop-up-think left pop-up-1">Start-up<br> thuận lợi</div>
                    </div>
                    <div class="img-swip-area">
                    <img src={templates.AssetURL("/asset/images/male-27-45/2-male.webp")} alt="starup thuan loi">
                    </div>
                </div>
                <div class="swiper-slide slide-3">
                    <h2 class="title-slide">
                    Đâu là những mục tiêu <br>
                    bạn muốn thực hiện?
                    </h2>
                    <div class="content-think-area">
                    <div class="pop-up-think right">Chăm lo<br> Gia đình</div>
                    </div>
                    <div class="img-swip-area">
                    <img src={templates.AssetURL("/asset/images/male-27-45/3-male.webp")} alt="cham lo gia dinh">
                    </div>
                </div>
                <div class="swiper-slide slide-4">
                    <h2 class="title-slide">
                    Đâu là những mục tiêu <br>
                    bạn muốn thực hiện?
                    </h2>
                    <div class="content-think-area">
                    <div class="pop-up-think right">Đạt được thành tựu<br> trong công việc</div>
                    </div>
                    <div class="img-swip-area">
                    <img src={templates.AssetURL("/asset/images/male-27-45/4-male.webp")} alt="du lich nuoc ngoai">
                    </div>
                </div>
                <div class="swiper-slide slide-5">
                    <h2 class="title-slide">
                    Đâu là những mục tiêu <br>
                    bạn muốn thực hiện?
                    </h2>
                    <div class="content-think-area">
                    <div class="pop-up-think right">Kết hôn<br> ổn định cuộc sống</div>
                    </div>
                    <div class="img-swip-area">
                    <img src={templates.AssetURL("/asset/images/male-27-45/5-male.webp")} alt="tot nghiep">
                    </div>
                </div>
                <div class="swiper-slide slide-6">
                    <div class="content-think-area">
                    <div class="pop-up-think left" style="font-weight: 400;">
                        Là đàn ông, trên vai là gia đình, trước mặt là sự nghiệp.
                        Đừng để sức khoẻ ngăn cản bạn bảo vệ người thân và thực hiện những dự định tương lai.</div>
                    <div class="pop-up-think right">
                        <span>Chủ động</span>
                        <span class="note">DỰ PHÒNG HPV<br>NGAY HÔM NAY</span>
                        <span>để an tâm hiện thực hóa<br> những dự định</span>
                        <a href="#result-section" class="now-btn">Tìm hiểu ngay về HPV </a>
                    </div>
                    </div>
                    <div class="img-swip-area">
                    <img src={templates.AssetURL("/asset/images/male-27-45/6-male.webp")} alt="du phong ngay">
                    </div>
                </div>
            </div>
            <div class="control-navigate-pagination">
                <div class="action-navigate">
                    <span class="btn-play">
                    <svg class="icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="#play-solid-icon" />
                    </svg>
                    </span>
                    <span class="btn-pause active">
                    <svg class="icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="#pause-solid-icon" />
                    </svg>
                    </span>
                </div>
                <div class="swiper-pagination bg-transparent"></div>
            </div>
        </div>
    </section>
}
