package usecase

import (
	"context"
	"errors"
	"webgo/hpv/common/enums"
	"webgo/hpv/entity"
	"webgo/hpv/malesv3/mapping"
	"webgo/hpv/malesv3/transport/responses"
	"webgo/pkg/gos/utils"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/core"

	"gorm.io/gorm/clause"
)

type Male2745V3PostRepo interface {
	FindPostRepo(ctx context.Context, filter *utils.Filters) ([]*entity.PostEntity, error)
}

type CategoryRepoMale2745 interface {
	FirstCategoryRepo(ctx context.Context, filter *utils.Filters) (*entity.CategoryEntity, error)
}

type Male2745V3OptionValueRepo interface {
	FindOptionValueRepo(ctx context.Context, filter *utils.Filters) ([]*entity.OptionValue, error)
}

type Male2745V3Usc struct {
	postRepo     Male2745V3PostRepo
	categoryRepo CategoryRepoMale2745
	optionRepo   Male2745V3OptionValueRepo
	logger       sctx.Logger
}

func NewMale2745V3Usc(postRepo Male2745V3PostRepo, logger sctx.Logger, categoryRepo CategoryRepoMale2745, optionRepo Male2745V3OptionValueRepo) *Male2745V3Usc {
	return &Male2745V3Usc{
		postRepo:     postRepo,
		logger:       logger,
		categoryRepo: categoryRepo,
		optionRepo:   optionRepo,
	}
}

/**
 * Process male 2745 usc
 * 1. get post & seo article by category 2745
 */
func (usc *Male2745V3Usc) ListMale2745V3Usc(ctx context.Context) (*responses.Male2745V3Resp, error) {
	conds := map[string]interface{}{
		"status": enums.POST_STATUS_PUBLISH,
	}

	subQuery := "SELECT post_id FROM cms.categories_posts WHERE category_id = ?"
	conds["id IN (?)"] = clause.Expr{SQL: subQuery, Vars: []interface{}{enums.CATEGORY_MALE_2745}}

	filter := utils.Filters{
		Columns: &[]string{
			"id",
			"title",
			"img",
			"page_path",
		},
		Conds:    &conds,
		PageSize: enums.LIMIT_10,
		OrderBy:  &[]string{"id DESC"},
	}

	posts, err := usc.postRepo.FindPostRepo(ctx, &filter)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	postsNew := mapping.MapperMale2745V3PostEntityToPostsNew(posts)

	options, err := usc.getOptionValueMale2745V3(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	seo, err := usc.getCategoryMale2745V3(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	return &responses.Male2745V3Resp{
		PostNew: postsNew,
		Options: options,
		Seo:     seo,
	}, nil

}

/**
 * get option value Female 2745
 */
func (usc *Male2745V3Usc) getOptionValueMale2745V3(ctx context.Context) (map[string]string, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"option_group_id": enums.OPTION_GROUP_MALE,
			"status":          enums.STATUS_ACTIVE,
		},
		Columns: &[]string{
			"key",
			"content",
		},
	}

	options, err := usc.optionRepo.FindOptionValueRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return mapping.MapperOptionValueToMapMale(options), nil
}

/**
 * get seo Female 2745
 */
func (usc *Male2745V3Usc) getCategoryMale2745V3(ctx context.Context) (*entity.Seo, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"id":     enums.CATEGORY_MALE_2745,
			"status": enums.STATUS_ACTIVE,
		},
		Columns: &[]string{
			"seo",
		},
	}

	category, err := usc.categoryRepo.FirstCategoryRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return category.Seo, nil
}
