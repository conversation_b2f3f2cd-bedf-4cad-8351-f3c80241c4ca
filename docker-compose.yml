version: "3.9"
services:
  goweb:
    build:
      context: '.'
      dockerfile: 'Dockerfile'
    env_file: .env
    ports:
      - "${FIBER_PORT:-3000}:${FIBER_PORT:-3000}"
    environment:
      DB_DSN: "${MYSQL_USER}:${MYSQL_PASS}@tcp(${MYSQL_HOST}:${MYSQL_PORT})/${MYSQL_DATABASE}?charset=utf8&parseTime=True&loc=Local"
      DB_DRIVER: "mysql"
      FIBER_PORT: "${FIBER_PORT}"
      VIEWS_PATH: "/views"
      PUBLIC_PATH: "/public"
    command: [ "./main", "web" ]
  db:
    image: postgres:latest
    container_name: 'goweb-hpv-postgres'
    ports:
      - '$POSTGRES_PORT:5432'
    environment:
      POSTGRES_USER: $POSTGRES_USER
      POSTGRES_PASSWORD: $POSTGRES_PASS
      POSTGRES_DB: $POSTGRES_DATABASE
    volumes:
      - ./docker/postgres/database:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
