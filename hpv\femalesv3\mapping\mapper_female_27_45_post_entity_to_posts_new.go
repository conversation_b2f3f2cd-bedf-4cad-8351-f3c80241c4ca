package mapping


import (
	"webgo/hpv/entity"
	"webgo/hpv/femalesv3/transport/responses"
)

/**
 * Mapper Post Entity to PostsNew
 * page female2745V3
 */
func MapperFemale2745V3PostEntityToPostsNew(posts []*entity.PostEntity) []responses.Female2745V3PostNew {
	if posts == nil {
		return nil
	}
	var res []responses.Female2745V3PostNew
	for _, post := range posts {
		res = append(res, responses.Female2745V3PostNew{
			Title:    post.Title,
			PagePath: post.PagePath,
			Img:      post.Img,
		})
	}

	return res
}
