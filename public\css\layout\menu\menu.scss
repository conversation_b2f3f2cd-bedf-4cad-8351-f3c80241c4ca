// @import './search';
@import './modal-search.scss';
@import './menu-mobile';


.main-menu {
   display: flex;
   border-radius: 50px;
   flex: auto;
   align-items: center;
   justify-content: space-between;
   padding: 0.5em 1.8em;
   background-color: transparent;
   box-shadow: none;
   // margin-top: 15px;
   position: relative;

   .btn-open-menu-mobile {
      cursor: pointer;
      font-size: 2.2em;
      padding: 10px 10px;
      border-radius: var(--radius-sm);
      transition: all 250ms ease-in-out;
      color: var(--primary-800);

      &:hover {
         color: white;
         background-color: var(--primary-800);
      }
   }

   .logos {
      display: flex;
      gap: 10px;

      img {
         height: 3.6em
      }
   }

   .nav-menu {
      justify-content: space-between;
      align-items: center;
      gap: 0.4em;
      display: flex;

      ul {
         display: none;
         gap: 0.3em;
         width: 100%;
         justify-content: center;
         justify-content: flex-end;
      }

      &__item {
         display: inline-block;

         a {
            text-transform: capitalize;
            text-decoration: none;
            display: block;
            padding: 0.6em 1.5em;
            border: 1px solid var(--primary-800);
            border-radius: 50px;
            font-size: 1em;
            transition: all 0.25s ease-in-out;
            color: var(--primary-800);
            font-weight: 400;
            text-align: center;

            transition: all .4s;
            position: relative;
            overflow: hidden;
            z-index: 1;

            &:before {
               content: '';
               position: absolute;
               bottom: 0;
               left: 0;
               width: 0%;
               height: calc(100% + 2px);
               background-color: var(--primary-800);
               transition: all .3s;
               border-radius: 10rem;
               z-index: -1;
               left: -2px;
            }

            &:hover {
               background-color: var(--primary-800);
               border-color: var(--primary-500);
               color: white;

               // &:before {
               //    width: calc(100% + 4px);
               // }
            }


         }

         &.active a {
            color: green;
         }
      }

      .search-control {
         padding: 8px;
         cursor: pointer;
         border-radius: 50%;
         color: var(--primary-800);
         transition: all 0.25s ease-in-out;

         svg {
            font-size: 30px;
         }

         &:hover {
            background-color: var(--primary-800);
            color: white;
         }
      }
   }

   .btn-open-menu-mobile {
      display: block;
   }
}


@media (min-width: 1100px) {
   .main-menu {
      // margin-top: 15px;
      background-color: white;
      justify-content: flex-start;
      // box-shadow: 0px 4px 10px 0px rgba(51, 51, 51, 0.2470588235);

      .btn-open-menu-mobile {
         display: none;
      }

      .nav-menu {
         flex-grow: 1;

         ul {
            flex-grow: 1;
            display: flex;

            .nav-menu__item {
               transition: all 250ms ease-in-out;

               &.is-female {

                  a {
                     &:hover {
                        color: var(--neutral-100);
                        background-color: var(--secondary-500);
                        border-color: var(--secondary-500);

                        &:before {
                           background-color: var(--secondary-500);
                        }
                     }
                  }
               }

               &.is-male {
                  a {
                     &:hover {
                        color: var(--neutral-900);
                        background-color: var(--third-500);
                        border-color: var(--third-500);

                        &:before {
                           background-color: var(--third-500);
                        }
                     }
                  }
               }
            }
         }
      }
   }

   .popup-search .container-search {
      width: 400px;
   }
}

@media (min-width: 1300px) {
   .main-menu {
      // .logos {
      //    img {
      //       height: 65px
      //    }
      // }
      font-size: 1.1rem;

      .nav-menu {
         display: flex;

         ul {
            gap: 1em;
         }

         &__item {

            // a {
            //    padding: 0.8em 1.2em;
            //    font-size: 1.3em;
            // }

            &.active a {
               color: green;
            }
         }

      }

   }
}

@media (min-width: 1800px) {
   .main-menu {
      font-size: 1.3rem;
   }
}