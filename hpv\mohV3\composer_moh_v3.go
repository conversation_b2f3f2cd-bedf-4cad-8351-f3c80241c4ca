package mohv3

import (
	"webgo/hpv/mohV3/transport/handlers"
	"webgo/hpv/mohV3/usecase"
	"webgo/hpv/repository"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/component/gormc"
	"webgo/pkg/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type composerMoh interface {
	ListMohHdl() fiber.Handler
}

func ComposerMohSVC(serviceCtx sctx.ServiceContext) composerMoh {
	db := serviceCtx.MustGet(configs.KeyCompGorm).(gormc.GormComponent).GetDB()
	log := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web")

	repoPost := repository.NewPostRepo(db)
	categoryRepo := repository.NewCategoryRepo(db)
	repoOption := repository.NewOptionValueRepo(db)
	
	usc := usecase.NewMohUsc(repoPost, repoOption, categoryRepo, log)
	hdl := handlers.NewMohHdl(usc)

	return hdl
}
