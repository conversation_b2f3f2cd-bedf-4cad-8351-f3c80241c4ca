
name: Deploy to VPS 97
on:
  push:
    branches:
      - v3
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2
      - name: SSH Deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST_S97 }}
          USERNAME: ${{ secrets.USERNAME_S97 }}
          PORT: ${{ secrets.PORT_S97 }}
          KEY: ${{ secrets.PRI_KEY_S97_GOWEB }}
          script: |            
            sudo /usr/bin/systemctl stop msd
            destination_dir="/home/<USER>/msd-goweb"              
            cd $destination_dir
            git pull
            sudo /usr/bin/systemctl restart msd
