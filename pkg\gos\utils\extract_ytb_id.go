package utils

import (
	"regexp"
	"strings"
)

func ExtractIDFromLink(link string) string {
	// Pattern for standard YouTube URLs and shortened URLs
	pattern := `(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})`

	re := regexp.MustCompile(pattern)
	matches := re.FindStringSubmatch(link)
	if len(matches) > 1 {
		return matches[1] // The second match group is the video ID
	}

	// short video
	if strings.Contains(link, "/shorts/") {
		linkPieces := strings.Split(link, "/")
		shortLink := linkPieces[len(linkPieces)-1]
		shortLinkParams := strings.Split(shortLink, "?")
		shortLinkId := shortLink
		if len(shortLinkParams) > 0 {
			shortLinkId = shortLinkParams[0]
		}
		return shortLinkId
	}
	return ""
}
