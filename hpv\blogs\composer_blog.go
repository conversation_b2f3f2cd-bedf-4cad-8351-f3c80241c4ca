package blogs

import (
	"webgo/hpv/blogs/transport/handlers"
	"webgo/hpv/blogs/usecase"
	"webgo/hpv/repository"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/component/gormc"
	"webgo/pkg/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type composerBlogs interface {
	ListBlogHdl() fiber.Handler
}

func ComposerBlogsSVC(serviceCtx sctx.ServiceContext) composerBlogs {
	db := serviceCtx.MustGet(configs.KeyCompGorm).(gormc.GormComponent).GetDB()
	log := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web")

	repoPost := repository.NewPostRepo(db)
	repoOption := repository.NewOptionValueRepo(db)
	usc := usecase.NewBlogUsc(repoPost, repoOption, log)
	hdl := handlers.NewBlogHdl(usc)

	return hdl
}
