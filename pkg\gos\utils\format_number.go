package utils

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
)

func FormatNumberCommasDot(num int64) string {
	str := fmt.Sprintf("%d", num)
	re := regexp.MustCompile(`(\\d+)(\\d{3})`)
	for n := ""; n != str; {
		n = str
		str = re.ReplaceAllString(str, "$1.$2")
	}

	return str
}

func FormatNumberWithDot(num int64) string {
	s := strconv.FormatInt(num, 10)
	n := len(s)
	if n <= 3 {
		return s
	}

	var b strings.Builder
	pre := n % 3
	if pre > 0 {
		b.WriteString(s[:pre])
		if n > pre {
			b.WriteString(".")
		}
	}

	for i := pre; i < n; i += 3 {
		b.WriteString(s[i : i+3])
		if i+3 < n {
			b.WriteString(".")
		}
	}

	return b.String()
}
