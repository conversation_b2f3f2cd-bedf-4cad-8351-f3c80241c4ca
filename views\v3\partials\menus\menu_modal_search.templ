package menus

import "webgo/hpv/searchpost/transport/responses"
import "webgo/pkg/gos/templates"

templ MenuModalSearch(datas *responses.SearchPostResp) {
    if datas != nil && len(datas.Posts) > 0 {
        for _, post := range datas.Posts {
            <article class="result-item" title={ post.Title }>
                <a class="result-item__avatar" href={ templates.SafeURL(post.PagePath) } title={ post.Title }>
                    <img src={ templates.ImgURL(post.Img) } alt={ post.Title }>
                </a>
                <p class="result-item__des">
                    <a href={ templates.SafeURL(post.PagePath) } title={ post.Title }>
                        { post.Title }
                    </a>
                </p>
            </article>
        }

    }
}