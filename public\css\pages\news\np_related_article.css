.grid-wrapper {
    margin-top: 3em;
    font-size: 0.625vw;
}
.grid-wrapper .row-wrapper{
    font-size: 0.652vw;
    display: flex;
    gap: 1.5em;
    
}
.grid-wrapper .item-row {
    width: 33%;
}
.grid-wrapper .nitem__content--row {
    width: 50%;
}

.grid-wrapper-detail {
    padding: 2em;
    margin-top: 2em;
    width: 100%;
    height: 100%;
    border-radius: 1em;
}

.grid-wrapper__title {
    align-items: center;
    margin-bottom: 2em;
}

.grid__analytic {
    font-size: 2.4em;
    font-weight: 700;
    color: var(--primary-500);
}

.grid__support {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5em;
    cursor: pointer;
    font-size: 1.3em;
}

.row-wrapper {
    display: flex;
    height: 100%;
    width: 100%;
    font-size: 0.652vw;
    justify-content: space-between;
}

.nitem:hover .nitem__thumb-img {
    transform: scale(1.05);
    transition: all 0.3s linear;
}

.nitem__thumb--row {
    width: 60%;
    padding: 1em;
    aspect-ratio: 4 / 3;
}

.grid-wrapper .nitem__thumb--row{
    width: 50%;
    border-top-left-radius: 1em;
    border-bottom-left-radius: 1em;
    overflow: hidden;
}

.grid-wrapper .nitem {
    flex: 1;
    display: flex;
    gap: 0.5em;
}

.nitem-row {
    display: flex;
    border-radius: 0 !important;
}

.nitem-row:after {
    content: "";
    display: block;
    width: 1px; 
    background: #b5b4b4;
    height: 100%;
}

.nitem-row:last-child:after {
    display: none;
}

.nitem__thumb-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-top-left-radius: 1em;
    border-bottom-left-radius: 1em;
}

.nitem__content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.nitem__thumb--row,
.nitem__content--row {
    width: 17.5em;
    position: relative;
    padding: 1em;
    aspect-ratio: 4 / 3;
}

.nitem__thumb-link {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.nitem__title {
    font-size: 2em;
    font-weight: 500;
}

.nitem__title__grid {
    font-weight: 500;
    font-size: 1.5em;
    line-height: 1.7em;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis
}

.nitem-row__bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9em;
}

.nitem-row__support {
    display: flex;
    gap: 2em;
}

.nitem__support-item {
    font-size: 1em;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.3em;
    color: rgb(var(--gray));
}

.nitem__support-item span {
    font-size: 1.2em;
    color: var(--txt-lightgray);
    font-style: italic;
}

.nitem__support-icon {
    width: 1.368em;
    aspect-ratio: 1;
}

.nitem__support-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.vertical-line {
    width: 1px;
    height: 100%;
    background-color: #ddd;
}

/* Responsive */
@media (max-width: 768px) {
    .grid-wrapper {
        border-radius: 2em;
    }   
    .grid-wrapper__title {
        margin-bottom: 0;
        background: linear-gradient(269.28deg, #009585 0%, #67CCC3 99.94%);
        text-align: center;
        font-size: 1.5vw;
        padding: 1em;
        border-radius: 1em 1em 0 0;
    }
    .grid__analytic {
        font-size: 1.6em;
        color: var(--neutral-900);
    }
    .grid-wrapper .row-wrapper {
        font-size: 1.5vw;
        display: flex;
        flex-direction: column;
        gap: 2vw;
        background: rgba(246, 248, 250, 1);
        border-radius: 0 0 1em 1em;
    }
    .grid-wrapper .nitem {
        width: 100%;
        flex: 1;
        flex-direction: row-reverse;
    }
    
    .grid-wrapper .nitem__thumb--row {
        width: 26%;
        padding: 0;
    }
    .grid-wrapper .nitem__content--row {
        width: 67%;
        aspect-ratio: unset;
        display: flex;
        justify-content: space-between;
    }
    .nitem__support-item span {
        color: #8d8a8a;
    }
    .grid-wrapper .nitem__thumb--row {
        border-radius: 1em;
    }
    .nitem-row {
        position: relative;
    }
    .nitem-row:after {
        position: absolute;
        content: "";
        display: block;
        height: 1px;
        bottom: -0.7em;
        background: #b5b4b4;
        width: 100%;
    }
    .nitem-row:last-child:after {
        display: none;
    }
}
@media (max-width: 575px) {
    .grid-wrapper__title {
        font-size: 2.25vw;
    }
    .grid-wrapper .row-wrapper {
        font-size: 2.25vw;
    }
}