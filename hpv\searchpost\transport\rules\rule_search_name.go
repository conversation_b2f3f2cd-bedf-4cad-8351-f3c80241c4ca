package rules

import (
	"reflect"
	"regexp"
	"strings"

	"github.com/go-playground/validator/v10"
)

func RuleSearchNameNoSQLInjection(fl validator.FieldLevel) bool {
	field := fl.Field()

	// Trường hợp nil pointer
	if field.Kind() == reflect.Ptr {

		if field.IsNil() {
			return true // hợp lệ nếu rỗng vì có omitempty
		}
		field = field.Elem()
	}

	value := strings.TrimSpace(field.String())

	if value == "" {
		return true // hợp lệ nếu rỗng vì có omitempty
	}

	// Regex kiểm tra chuỗi hợp lệ
	validPattern := `^[a-zA-Z0-9ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẰẮẲẴẶắẳẵặẸẻẽẹỀẾỂỄỆềếểễệỈỊỉịỌỌỎỎỐỒỔỖỘốồổỗộỚỜỞỠỢớờởỡợỤỦỨỪỬỮỰụủứừửữựỳỵỷỹýỵỷỹ\s\-_.,]*$`
	match, _ := regexp.MatchString(validPattern, value)

	// Kiểm tra SQL injection keyword
	sqlKeywords := regexp.MustCompile(`(?i)\b(union|select|insert|update|delete|drop|;|--|\/\*|\*\/|xp_)\b`)
	isSQLInject := sqlKeywords.MatchString(value)

	return match && !isSQLInject
}
