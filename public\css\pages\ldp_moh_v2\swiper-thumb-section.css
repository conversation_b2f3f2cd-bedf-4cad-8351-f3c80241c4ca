.swiper-container-main,
.swiper-container-thumbs {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
}

.swiper-container-main {
    height: 100%;
}

.swiper-container-thumbs {
    position: absolute;
    left: 50%;
    height: 100%;
    box-sizing: border-box;
    padding: 3% 0;
    width: 85%;
    overflow: hidden;
    transform: translateX(-50%);
}

.group-swiper-detail {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
}

.gallery__wrapper {
    display: flex;
}

.gallery__slide {
    aspect-ratio: 16 / 9;
}

.gallery__slide,
.gallery__thumb {
    background: #fff;
    text-align: center;
    font-size: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: cover;
    background-position: center;
}

.gallery__slide img,
.gallery__thumb img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gallery__thumb {
    width: 25%;
    height: 100%;
    opacity: 0.8;
    transition: 0.2s linear;
    transform: scale(0.5);
}

.gallery__thumb.swiper-slide-thumb-active {
    opacity: 1;
    transform: scale(1.2);
}

.gallery__button {
    color: #fff;
}

.group-bottom-swiper-thumb {
    width: 100%;
    /* margin: 0 10%; */
    position: absolute;
    bottom: 0;
    left: 0;
    height: 20%;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0.04%, #000000 100%);
    z-index: 1;
}

.detail-campain__left .swiper-button-prev:after,
.swiper-rtl .swiper-button-next:after {
    content: none;
}

.detail-campain__left .swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
    content: none;
}

.detail-campain__left .swiper-button-prev,
.swiper-rtl .swiper-button-next {
    left: var(--swiper-navigation-sides-offset, 3%);
    right: auto;
    width: 1.7vw;
}

.detail-campain__left .swiper-button-next,
.swiper-rtl .swiper-button-prev {
    right: var(--swiper-navigation-sides-offset, 3%);
    left: auto;
    width: 1.7vw;
}

.gallery__button--prev img,
.gallery__button--next img {
    width: 100%;
    object-fit: cover;
}

.gallery__controls--extra {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.6em;
    position: absolute;
    bottom: 0;
    margin: 0 auto;
    width: 100%;
}

.gallery__btn--prev-custom,
.gallery__btn--next-custom {
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
    width: 3em;
}

.swiper-pagination-bullets.swiper-pagination-horizontal.gallery__pagination--custom {
    width: fit-content;
}

.gallery__pagination--custom {
    display: flex;
    gap: 8px;
}

.gallery__bullet {
    width: 2em;
    height: 2em;
    background-color: #aaa;
    border-radius: 50%;
    opacity: 0.5;
    transition: 0.3s;
    cursor: pointer;
}

.gallery__bullet:hover {
    opacity: 0.8;
}

.gallery__bullet--active {
    background-color: var(--primary-500);
    opacity: 1;
    width: 4em;
    border-radius: 1em;
}

.gallery__pagination--custom {
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: center;
}

.gallery__btn--prev-custom img,
.gallery__btn--next-custom img,
.gallery__btn--prev-custom img {
    width: 100%;
    object-fit: cover;
}

.gallery__slide--video {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    height: 100%;
    padding-top: 2svh;
    background: #000;
    /* Optional: padding bên trong */
    box-sizing: border-box;
}

.gallery__slide--video iframe {
    aspect-ratio: 16 / 9;
    width: 100%;
    max-height: 80%;
    border: 0;
}
.hat-tag {
    position: absolute;
    top: 10%;
    left: 5%;
    width: 25%;
    aspect-ratio: 752 / 220;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
}
.hat-tag img{
    width: 100%;
    object-fit: cover;
}