package searchpost

import (
	"webgo/hpv/repository"
	"webgo/hpv/searchpost/transport/api"
	"webgo/hpv/searchpost/usecase"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/component/gormc"
	"webgo/pkg/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type composerSearchPost interface {
	ListSearchPostApi() fiber.Handler
	GetPostApi() fiber.Handler
}

func ComposerSearchPostService(serviceCtx sctx.ServiceContext) composerSearchPost {
	db := serviceCtx.MustGet(configs.KeyCompGorm).(gormc.GormComponent).GetDB()
	repoPost := repository.NewPostRepo(db)

	usc := usecase.NewSearchPostUsc(repoPost)

	api := api.NewSearchPostApi(usc)

	return api
}
