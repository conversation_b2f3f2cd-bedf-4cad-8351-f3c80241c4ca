package handlers

import (
	"context"
	"errors"
	"webgo/hpv/malesv3/transport/responses"
	"webgo/pkg/gos/templates"
	"webgo/pkg/sctx/core"
	"webgo/views/v3/males"

	"github.com/gofiber/fiber/v2"
)

type Male918V3PostUsc interface {
	ListMale918V3Usc(ctx context.Context) (*responses.Male918V3Resp, error)
}

type Male918V3Hdl struct {
	usc Male918V3PostUsc
}

func NewMale918V3Hdl(usc Male918V3PostUsc) *Male918V3Hdl {
	return &Male918V3Hdl{
		usc: usc,
	}
}

/***
 * 
 * Page: Female 918
 */

func (h *Male918V3Hdl) ListMale918V3Hdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		datas, err := h.usc.ListMale918V3Usc(c.Context())
		if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
			return c.Redirect("/page-404")
		}
		return templates.Render(c, males.Male918(datas))
	}
}