package female2745hariwon

import "webgo/pkg/gos/templates"
import "webgo/hpv/femalesv3/transport/responses"

templ Female2745ContentArticle(PostNew []responses.Female2745V3PostNew) {
    <section class="article">
         <div class="container"> 
            <div class="header-section">
               TÌM HIỂU THÊM
            </div>
            <div class="slide-article" id="male-19-26-slide-article">
               <div class="posts swiper-article swiper" id="article-list">
                  <div class="swiper-wrapper">
                        if len(PostNew) > 0 {
                            for _, post := range PostNew {
                                @Female2745ContentArticleItem(post)
                            }
                        } 
                  </div>         
                  <div class="control-navigate-pagination">            
                     <div class="swiper-button-prev"></div>
                     <div class="swiper-pagination"></div>
                     <div class="swiper-button-next"></div>
                  </div>
               </div>
            </div>
         </div>
    </section>
}

templ Female2745ContentArticleItem(post responses.Female2745V3PostNew) {
    <div class="swiper-slide post-item article-item box-shadow-sm">
        <div class="article-item__title ">
            <span>{ post.Title }</span>
        </div>
        <div class="article-item__avatar">
            <img src={ templates.ImgURL(post.Img) } alt={ post.Title }>
        </div>                  
        <div class="article-item__action">
            <a href={ templates.SafeURL(post.PagePath)} title={ post.Title }>
                Tìm hiểu thêm
                <svg class="icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <use xlink:href="#read-more-line-icon" />
                </svg>
            </a>
        </div>
    </div>
}