package malesv3

import (
	"webgo/hpv/malesv3/transport/handlers"
	"webgo/hpv/malesv3/usecase"
	"webgo/hpv/repository"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/component/gormc"
	"webgo/pkg/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type composerMale918V3 interface {
	ListMale918V3Hdl() fiber.Handler
}

func ComposerMale918V3Service(serviceCtx sctx.ServiceContext) composerMale918V3 {
	db := serviceCtx.MustGet(configs.KeyCompGorm).(gormc.GormComponent).GetDB()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web")
	
	repoPost := repository.NewPostRepo(db)
	categoryRepo := repository.NewCategoryRepo(db)
	optionRepo := repository.NewOptionValueRepo(db)
	
	usc := usecase.NewMale918V3Usc(repoPost, logger, categoryRepo, optionRepo)
	hdl := handlers.NewMale918V3Hdl(usc)

	return hdl
}
