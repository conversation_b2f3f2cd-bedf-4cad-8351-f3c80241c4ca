package mapping


import (
	"webgo/hpv/entity"
	"webgo/hpv/malesv3/transport/responses"
)

/**
 * Mapper Post Entity to PostsNew
 * page Male1926V3
 */
func MapperMale1926V3PostEntityToPostsNew(posts []*entity.PostEntity) []responses.Male1926V3PostNew {
	if posts == nil {
		return nil
	}
	var res []responses.Male1926V3PostNew
	for _, post := range posts {
		res = append(res, responses.Male1926V3PostNew{
			Title:    post.Title,
			PagePath: post.PagePath,
			Img:      post.Img,
		})
	}

	return res
}
